<header data-replit-metadata="client/src/components/enhanced-layout.tsx:208:6" data-component-name="header" class="sticky top-0 z-50 w-full border-b border-border/40 bg-background"><div data-replit-metadata="client/src/components/enhanced-layout.tsx:209:8" data-component-name="div" class="container flex h-16 max-w-screen-2xl items-center justify-between px-4 md:px-6"><div data-replit-metadata="client/src/components/enhanced-layout.tsx:212:10" data-component-name="div" class="flex items-center space-x-8"><a data-replit-metadata="client/src/components/enhanced-layout.tsx:213:12" data-component-name="Link" href="/" class="flex items-center space-x-2 hover:opacity-80 transition-opacity"><div data-replit-metadata="client/src/components/enhanced-layout.tsx:214:14" data-component-name="div" class="flex h-8 w-8 items-center justify-center rounded-lg bg-primary"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-video h-4 w-4 text-primary-foreground" data-replit-metadata="client/src/components/enhanced-layout.tsx:215:16" data-component-name="Video"><path d="m16 13 5.223 3.482a.5.5 0 0 0 .777-.416V7.87a.5.5 0 0 0-.752-.432L16 10.5"></path><rect x="2" y="6" width="14" height="12" rx="2"></rect></svg></div><span data-replit-metadata="client/src/components/enhanced-layout.tsx:217:14" data-component-name="span" class="font-bold text-lg">VideoSummarizer</span></a><nav data-replit-metadata="client/src/components/enhanced-layout.tsx:221:12" data-component-name="nav" class="hidden md:flex items-center space-x-1"><a data-replit-metadata="client/src/components/enhanced-layout.tsx:222:14" data-component-name="Link" href="/"><button data-replit-metadata="client/src/components/enhanced-layout.tsx:223:16" data-component-name="Button" class="justify-center whitespace-nowrap text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&amp;_svg]:pointer-events-none [&amp;_svg]:size-4 [&amp;_svg]:shrink-0 hover:bg-accent hover:text-accent-foreground h-9 rounded-md px-3 flex items-center gap-2"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-plus h-4 w-4" data-replit-metadata="client/src/components/enhanced-layout.tsx:228:18" data-component-name="Plus"><path d="M5 12h14"></path><path d="M12 5v14"></path></svg>Create</button></a><a data-replit-metadata="client/src/components/enhanced-layout.tsx:233:14" data-component-name="Link" href="/library"><button data-replit-metadata="client/src/components/enhanced-layout.tsx:234:16" data-component-name="Button" class="justify-center whitespace-nowrap text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&amp;_svg]:pointer-events-none [&amp;_svg]:size-4 [&amp;_svg]:shrink-0 hover:bg-accent hover:text-accent-foreground h-9 rounded-md px-3 flex items-center gap-2"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-library h-4 w-4" data-replit-metadata="client/src/components/enhanced-layout.tsx:239:18" data-component-name="Library"><path d="m16 6 4 14"></path><path d="M12 6v14"></path><path d="M8 8v12"></path><path d="M4 4v16"></path></svg>Library</button></a><a data-replit-metadata="client/src/components/enhanced-layout.tsx:244:14" data-component-name="Link" href="/channels"><button data-replit-metadata="client/src/components/enhanced-layout.tsx:245:16" data-component-name="Button" class="justify-center whitespace-nowrap text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&amp;_svg]:pointer-events-none [&amp;_svg]:size-4 [&amp;_svg]:shrink-0 hover:bg-accent hover:text-accent-foreground h-9 rounded-md px-3 flex items-center gap-2"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-monitor-speaker h-4 w-4" data-replit-metadata="client/src/components/enhanced-layout.tsx:250:18" data-component-name="MonitorSpeaker"><path d="M5.5 20H8"></path><path d="M17 9h.01"></path><rect width="10" height="16" x="12" y="4" rx="2"></rect><path d="M8 6H4a2 2 0 0 0-2 2v6a2 2 0 0 0 2 2h4"></path><circle cx="17" cy="15" r="1"></circle></svg>Channels</button></a><a data-replit-metadata="client/src/components/enhanced-layout.tsx:255:14" data-component-name="Link" href="/topics"><button data-replit-metadata="client/src/components/enhanced-layout.tsx:256:16" data-component-name="Button" class="justify-center whitespace-nowrap text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&amp;_svg]:pointer-events-none [&amp;_svg]:size-4 [&amp;_svg]:shrink-0 hover:bg-accent hover:text-accent-foreground h-9 rounded-md px-3 flex items-center gap-2"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-tag h-4 w-4" data-replit-metadata="client/src/components/enhanced-layout.tsx:261:18" data-component-name="Tag"><path d="M12.586 2.586A2 2 0 0 0 11.172 2H4a2 2 0 0 0-2 2v7.172a2 2 0 0 0 .586 1.414l8.704 8.704a2.426 2.426 0 0 0 3.42 0l6.58-6.58a2.426 2.426 0 0 0 0-3.42z"></path><circle cx="7.5" cy="7.5" r=".5" fill="currentColor"></circle></svg>Topics</button></a></nav></div><div data-replit-metadata="client/src/components/enhanced-layout.tsx:284:10" data-component-name="div" class="flex items-center space-x-3"><div data-replit-metadata="client/src/components/enhanced-layout.tsx:287:12" data-component-name="div" class="hidden lg:flex items-center space-x-2 px-3 py-1 rounded-lg bg-green-50 dark:bg-green-950/20 border border-green-200 dark:border-green-800"><div data-replit-metadata="client/src/components/enhanced-layout.tsx:288:16" data-component-name="div" class="h-2 w-2 bg-green-500 rounded-full animate-pulse"></div><span data-replit-metadata="client/src/components/enhanced-layout.tsx:289:16" data-component-name="span" class="text-sm text-green-700 dark:text-green-300">20 new summaries</span></div><div data-replit-metadata="client/src/components/enhanced-layout.tsx:296:12" data-component-name="div" class="hidden md:flex items-center space-x-2"><button data-replit-metadata="client/src/components/enhanced-layout.tsx:298:14" data-component-name="Button" class="inline-flex items-center justify-center gap-2 whitespace-nowrap text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&amp;_svg]:pointer-events-none [&amp;_svg]:size-4 [&amp;_svg]:shrink-0 hover:bg-accent hover:text-accent-foreground h-9 rounded-md px-3 relative"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-search h-4 w-4 mr-2" data-replit-metadata="client/src/components/enhanced-layout.tsx:305:18" data-component-name="action.icon"><circle cx="11" cy="11" r="8"></circle><path d="m21 21-4.3-4.3"></path></svg><span data-replit-metadata="client/src/components/enhanced-layout.tsx:306:18" data-component-name="span" class="hidden lg:inline">Search</span></button></div><button data-replit-metadata="client/src/components/enhanced-layout.tsx:323:18" data-component-name="Button" class="justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&amp;_svg]:pointer-events-none [&amp;_svg]:size-4 [&amp;_svg]:shrink-0 hover:bg-accent hover:text-accent-foreground h-10 py-2 flex items-center space-x-2 px-2" type="button" id="radix-:r0:" aria-haspopup="menu" aria-expanded="false" data-state="closed"><span data-replit-metadata="client/src/components/enhanced-layout.tsx:324:20" data-component-name="Avatar" class="relative flex shrink-0 overflow-hidden rounded-full w-8 h-8"><span data-replit-metadata="client/src/components/enhanced-layout.tsx:326:22" data-component-name="AvatarFallback" class="flex h-full w-full items-center justify-center rounded-full bg-muted text-xs">JW</span></span><div data-replit-metadata="client/src/components/enhanced-layout.tsx:330:20" data-component-name="div" class="hidden lg:block text-left"><div data-replit-metadata="client/src/components/enhanced-layout.tsx:331:22" data-component-name="div" class="text-sm font-medium">Jim </div><div data-replit-metadata="client/src/components/enhanced-layout.tsx:332:22" data-component-name="div" class="text-xs text-muted-foreground capitalize">enterprise Plan</div></div></button><button data-replit-metadata="client/src/components/enhanced-layout.tsx:384:16" data-component-name="Button" class="inline-flex items-center justify-center gap-2 whitespace-nowrap text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&amp;_svg]:pointer-events-none [&amp;_svg]:size-4 [&amp;_svg]:shrink-0 hover:bg-accent hover:text-accent-foreground h-9 rounded-md px-3 md:hidden" type="button" aria-haspopup="dialog" aria-expanded="false" aria-controls="radix-:r2:" data-state="closed"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-menu h-5 w-5" data-replit-metadata="client/src/components/enhanced-layout.tsx:385:18" data-component-name="Menu"><line x1="4" x2="20" y1="12" y2="12"></line><line x1="4" x2="20" y1="6" y2="6"></line><line x1="4" x2="20" y1="18" y2="18"></line></svg></button></div></div></header>