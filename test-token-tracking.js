// Complete token cost tracking test
import fetch from 'node-fetch';

async function testTokenTracking() {
  const apiKey = process.env.OPENAI_API_KEY;
  
  if (!apiKey) {
    console.log('❌ OPENAI_API_KEY not found');
    return;
  }
  
  console.log('🧪 Testing Complete Token Cost Tracking System');
  console.log('='.repeat(50));
  
  try {
    // Test OpenAI API call with summary generation
    const response = await fetch('https://api.openai.com/v1/chat/completions', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${apiKey}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        model: 'gpt-4o-mini',
        messages: [
          {
            role: 'system',
            content: 'You are a video summarization expert. Create concise, actionable summaries.'
          },
          {
            role: 'user', 
            content: 'Create a brief HTML summary for a YouTube video titled "How to Learn Programming". Include key takeaways as a bulleted list in a styled div.'
          }
        ],
        max_tokens: 300,
        temperature: 0.2,
      }),
    });
    
    console.log(`📊 API Response Status: ${response.status}`);
    
    const data = await response.json();
    
    if (!response.ok) {
      console.log('❌ API Error:', data);
      return;
    }
    
    console.log('✅ Summary generated successfully');
    console.log('📝 Content length:', data.choices[0]?.message?.content?.length, 'characters');
    
    // Token cost tracking
    if (data.usage) {
      console.log('\n💰 Token Usage Analysis:');
      console.log(`  Input tokens: ${data.usage.prompt_tokens}`);
      console.log(`  Output tokens: ${data.usage.completion_tokens}`);
      console.log(`  Total tokens: ${data.usage.total_tokens}`);
      
      // Calculate costs (GPT-4o pricing)
      const inputCost = (data.usage.prompt_tokens / 1000000) * 2.50;
      const outputCost = (data.usage.completion_tokens / 1000000) * 10.00;
      const totalCost = inputCost + outputCost;
      
      console.log('\n💵 Cost Breakdown:');
      console.log(`  Input cost: $${inputCost.toFixed(6)}`);
      console.log(`  Output cost: $${outputCost.toFixed(6)}`);
      console.log(`  Total cost: $${totalCost.toFixed(6)}`);
      
      // Storage format (hundredths of cents)
      const storageCost = Math.round(totalCost * 10000);
      console.log(`  Storage format: ${storageCost} hundredths of cents`);
      
      console.log('\n📈 Scaling Examples:');
      console.log(`  100 summaries: $${(totalCost * 100).toFixed(4)}`);
      console.log(`  1,000 summaries: $${(totalCost * 1000).toFixed(2)}`);
      console.log(`  10,000 summaries: $${(totalCost * 10000).toFixed(0)}`);
      
    } else {
      console.log('❌ No usage data in response');
    }
    
      // Test user cost accumulation simulation
      console.log('\n🔄 User Cost Accumulation Simulation:');
      const userStartingCost = 0;
      const newCost = Math.round(totalCost * 10000);
      const updatedCost = userStartingCost + newCost;
    
    console.log(`  Starting cost: ${userStartingCost} hundredths of cents`);
    console.log(`  This summary cost: ${newCost} hundredths of cents`);
    console.log(`  Updated total: ${updatedCost} hundredths of cents ($${(updatedCost / 10000).toFixed(6)})`);
    
  } catch (error) {
    console.log('❌ Test failed:', error.message);
  }
}

testTokenTracking();