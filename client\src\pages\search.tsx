import { useState } from "react";
import { Search, Filter, BookOpen, Calendar, Tag, User, Zap, X } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Checkbox } from "@/components/ui/checkbox";
import { Label } from "@/components/ui/label";
import { Switch } from "@/components/ui/switch";
import { Separator } from "@/components/ui/separator";
import EnhancedLayout from "@/components/enhanced-layout";
import { useQuery, useMutation } from "@tanstack/react-query";
import { Summary, Channel, Topic } from "shared/schema";

export default function SearchPage() {
  const [naturalQuery, setNaturalQuery] = useState("");
  const [booleanQuery, setBooleanQuery] = useState("");
  const [selectedChannels, setSelectedChannels] = useState<string[]>([]);
  const [selectedTopics, setSelectedTopics] = useState<string[]>([]);
  const [dateRange, setDateRange] = useState("all");
  const [sortBy, setSortBy] = useState("relevance");
  const [includeTranscripts, setIncludeTranscripts] = useState(true);
  const [activeSearchType, setActiveSearchType] = useState<"natural" | "boolean">("natural");
  const [searchResults, setSearchResults] = useState<Summary[]>([]);
  const [isSearching, setIsSearching] = useState(false);

  // Fetch channels for filtering
  const { data: channels = [] } = useQuery<Channel[]>({
    queryKey: ["/api/channels"],
  });

  // Fetch topics for filtering
  const { data: topics = [] } = useQuery<Topic[]>({
    queryKey: ["/api/topics"],
  });

  // Search mutation - enhanced to support multiple search modes
  const searchMutation = useMutation({
    mutationFn: async (searchData: any) => {
      let endpoint = "/api/search";
      
      // Route to appropriate search endpoint based on search type
      if (searchData.type === "topic-scoped") {
        endpoint = "/api/search/topic-scoped";
      } else if (searchData.type === "multi-mode") {
        endpoint = "/api/search/multi-mode";
      }
      
      const response = await fetch(endpoint, {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify(searchData),
      });
      if (!response.ok) throw new Error("Search failed");
      return response.json();
    },
    onSuccess: (data) => {
      setSearchResults(data);
      setIsSearching(false);
    },
  });

  // Topic-scoped search mutation
  const topicSearchMutation = useMutation({
    mutationFn: async (searchData: { topicId: number; query: string; exactMatch?: boolean; includeTranscripts?: boolean }) => {
      const response = await fetch("/api/search/topic-scoped", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify(searchData),
      });
      if (!response.ok) throw new Error("Topic search failed");
      return response.json();
    },
    onSuccess: (data) => {
      setSearchResults(data);
      setIsSearching(false);
    },
  });

  const handleNaturalSearch = () => {
    if (!naturalQuery.trim()) return;
    
    setIsSearching(true);
    searchMutation.mutate({
      type: "natural",
      query: naturalQuery,
      filters: {
        channels: selectedChannels,
        topics: selectedTopics,
        dateRange,
        includeTranscripts,
      },
      sortBy,
    });
  };

  const handleBooleanSearch = () => {
    if (!booleanQuery.trim()) return;
    
    setIsSearching(true);
    searchMutation.mutate({
      type: "boolean",
      query: booleanQuery,
      filters: {
        channels: selectedChannels,
        topics: selectedTopics,
        dateRange,
        includeTranscripts,
      },
      sortBy,
    });
  };

  const clearFilters = () => {
    setSelectedChannels([]);
    setSelectedTopics([]);
    setDateRange("all");
    setSortBy("relevance");
    setIncludeTranscripts(true);
  };

  const removeFilter = (type: string, value: string) => {
    if (type === "channel") {
      setSelectedChannels(prev => prev.filter(c => c !== value));
    } else if (type === "topic") {
      setSelectedTopics(prev => prev.filter(t => t !== value));
    }
  };

  return (
    <EnhancedLayout>
      <div className="max-w-6xl mx-auto space-y-6">
        
        {/* Page Title */}
        <div className="space-y-2">
          <h1 className="text-3xl font-bold tracking-tight flex items-center gap-3">
            <Search className="w-8 h-8" />
            Advanced Search
          </h1>
          <p className="text-muted-foreground">
            Search your video summaries using natural language or boolean operators
          </p>
        </div>

        {/* Search Interface */}
        <Card>
          <CardContent>
            <Tabs value={activeSearchType} onValueChange={(value) => setActiveSearchType(value as "natural" | "boolean")}>
              <TabsList className="grid w-full grid-cols-2">
                <TabsTrigger value="natural" className="flex items-center gap-2">
                  <Zap className="w-4 h-4" />
                  Natural Language
                </TabsTrigger>
                <TabsTrigger value="boolean" className="flex items-center gap-2">
                  <Filter className="w-4 h-4" />
                  Boolean Search
                </TabsTrigger>
              </TabsList>

              <TabsContent value="natural" className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="natural-search">Natural Language Query</Label>
                  <div className="flex gap-2">
                    <Textarea
                      id="natural-search"
                      placeholder="Ask anything about your videos... 
Examples:
• Find videos about machine learning from the last month
• Show me productivity tips from channels I follow
• What are the key points about React development?"
                      value={naturalQuery}
                      onChange={(e) => setNaturalQuery(e.target.value)}
                      className="min-h-24"
                    />
                  </div>
                  
                  {/* Topic-scoped search option */}
                  {selectedTopics.length === 1 && (
                    <div className="bg-blue-50 p-3 rounded-lg border border-blue-200">
                      <Label className="text-sm font-medium text-blue-800">
                        🎯 Search within "{topics.find(t => t.id.toString() === selectedTopics[0])?.name}" topic
                      </Label>
                      <p className="text-xs text-blue-600 mt-1">
                        This will search only within summaries from channels assigned to this topic
                      </p>
                      <div className="flex gap-2 mt-2">
                        <Button 
                          size="sm"
                          variant="outline"
                          onClick={() => {
                            setIsSearching(true);
                            topicSearchMutation.mutate({
                              topicId: parseInt(selectedTopics[0]),
                              query: naturalQuery,
                              exactMatch: false,
                              includeTranscripts: includeTranscripts
                            });
                          }}
                          disabled={!naturalQuery.trim() || isSearching}
                        >
                          Search in Topic
                        </Button>
                        <Button 
                          size="sm"
                          variant="outline"
                          onClick={() => {
                            setIsSearching(true);
                            topicSearchMutation.mutate({
                              topicId: parseInt(selectedTopics[0]),
                              query: naturalQuery,
                              exactMatch: true,
                              includeTranscripts: includeTranscripts
                            });
                          }}
                          disabled={!naturalQuery.trim() || isSearching}
                        >
                          Exact Match in Topic
                        </Button>
                      </div>
                    </div>
                  )}
                  
                  <Button 
                    onClick={handleNaturalSearch} 
                    disabled={!naturalQuery.trim() || isSearching}
                    className="w-full"
                  >
                    {isSearching ? "Searching..." : "Search with AI"}
                  </Button>
                </div>
              </TabsContent>

              <TabsContent value="boolean" className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="boolean-search">Boolean Query</Label>
                  <div className="flex gap-2">
                    <Textarea
                      id="boolean-search"
                      placeholder="Use boolean operators for precise searches...
Examples:
• (React OR Vue) AND tutorial
• machine learning NOT tensorflow
• productivity AND (tips OR tricks)
• channel:TechChannel AND topic:JavaScript"
                      value={booleanQuery}
                      onChange={(e) => setBooleanQuery(e.target.value)}
                      className="min-h-24"
                    />
                  </div>
                  <div className="text-sm text-muted-foreground space-y-1">
                    <p><strong>Operators:</strong> AND, OR, NOT, ( )</p>
                    <p><strong>Fields:</strong> channel:name, topic:name, title:text, content:text</p>
                  </div>
                  <Button 
                    onClick={handleBooleanSearch} 
                    disabled={!booleanQuery.trim() || isSearching}
                    className="w-full"
                  >
                    {isSearching ? "Searching..." : "Execute Boolean Search"}
                  </Button>
                </div>
              </TabsContent>
            </Tabs>
          </CardContent>
        </Card>

        {/* Filters */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center justify-between">
              <span className="flex items-center gap-2">
                <Filter className="w-5 h-5" />
                Search Filters
              </span>
              <Button variant="ghost" size="sm" onClick={clearFilters}>
                Clear All
              </Button>
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-6">
            
            {/* Active Filters */}
            {(selectedChannels.length > 0 || selectedTopics.length > 0 || dateRange !== "all") && (
              <div className="space-y-2">
                <Label>Active Filters</Label>
                <div className="flex flex-wrap gap-2">
                  {selectedChannels.map((channelId) => {
                    const channel = channels.find(c => c.id.toString() === channelId);
                    return (
                      <Badge key={channelId} variant="secondary" className="flex items-center gap-1">
                        <User className="w-3 h-3" />
                        {channel?.channelName || channelId}
                        <X 
                          className="w-3 h-3 cursor-pointer" 
                          onClick={() => removeFilter("channel", channelId)}
                        />
                      </Badge>
                    );
                  })}
                  {selectedTopics.map((topicId) => {
                    const topic = topics.find(t => t.id.toString() === topicId);
                    return (
                      <Badge key={topicId} variant="secondary" className="flex items-center gap-1">
                        <Tag className="w-3 h-3" />
                        {topic?.name || topicId}
                        <X 
                          className="w-3 h-3 cursor-pointer" 
                          onClick={() => removeFilter("topic", topicId)}
                        />
                      </Badge>
                    );
                  })}
                  {dateRange !== "all" && (
                    <Badge variant="secondary" className="flex items-center gap-1">
                      <Calendar className="w-3 h-3" />
                      {dateRange}
                      <X 
                        className="w-3 h-3 cursor-pointer" 
                        onClick={() => setDateRange("all")}
                      />
                    </Badge>
                  )}
                </div>
                <Separator />
              </div>
            )}

            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              
              {/* Channel Filter */}
              <div className="space-y-3">
                <Label>Channels</Label>
                <div className="space-y-2 max-h-40 overflow-y-auto">
                  {channels.map((channel) => (
                    <div key={channel.id} className="flex items-center space-x-2">
                      <Checkbox
                        id={`channel-${channel.id}`}
                        checked={selectedChannels.includes(channel.id.toString())}
                        onCheckedChange={(checked) => {
                          if (checked) {
                            setSelectedChannels(prev => [...prev, channel.id.toString()]);
                          } else {
                            setSelectedChannels(prev => prev.filter(c => c !== channel.id.toString()));
                          }
                        }}
                      />
                      <Label htmlFor={`channel-${channel.id}`} className="text-sm">
                        {channel.channelName}
                      </Label>
                    </div>
                  ))}
                </div>
              </div>

              {/* Topic Filter */}
              <div className="space-y-3">
                <Label>Topics</Label>
                <div className="space-y-2 max-h-40 overflow-y-auto">
                  {topics.map((topic) => (
                    <div key={topic.id} className="flex items-center space-x-2">
                      <Checkbox
                        id={`topic-${topic.id}`}
                        checked={selectedTopics.includes(topic.id.toString())}
                        onCheckedChange={(checked) => {
                          if (checked) {
                            setSelectedTopics(prev => [...prev, topic.id.toString()]);
                          } else {
                            setSelectedTopics(prev => prev.filter(t => t !== topic.id.toString()));
                          }
                        }}
                      />
                      <Label htmlFor={`topic-${topic.id}`} className="text-sm">
                        {topic.name}
                      </Label>
                    </div>
                  ))}
                </div>
              </div>

              {/* Date and Options */}
              <div className="space-y-4">
                <div className="space-y-2">
                  <Label>Date Range</Label>
                  <Select value={dateRange} onValueChange={setDateRange}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Time</SelectItem>
                      <SelectItem value="today">Today</SelectItem>
                      <SelectItem value="week">This Week</SelectItem>
                      <SelectItem value="month">This Month</SelectItem>
                      <SelectItem value="year">This Year</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label>Sort By</Label>
                  <Select value={sortBy} onValueChange={setSortBy}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="relevance">Relevance</SelectItem>
                      <SelectItem value="date">Date (Newest)</SelectItem>
                      <SelectItem value="date-asc">Date (Oldest)</SelectItem>
                      <SelectItem value="title">Title (A-Z)</SelectItem>
                      <SelectItem value="duration">Duration</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="flex items-center space-x-2">
                  <Switch
                    id="include-transcripts"
                    checked={includeTranscripts}
                    onCheckedChange={setIncludeTranscripts}
                  />
                  <Label htmlFor="include-transcripts" className="text-sm">
                    Include transcripts in search
                  </Label>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Search Results */}
        {searchResults.length > 0 && (
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <BookOpen className="w-5 h-5" />
                Search Results ({searchResults.length})
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid gap-4">
                {searchResults.map((summary) => (
                  <Card key={summary.id} className="p-4">
                    <div className="space-y-2">
                      <div className="flex items-start justify-between">
                        <h3 className="font-semibold text-lg">{summary.videoTitle}</h3>
                        <Badge variant="outline">{summary.videoDuration}</Badge>
                      </div>
                      <p className="text-sm text-muted-foreground line-clamp-3">
                        {summary.summaryContent}
                      </p>
                      <div className="flex items-center gap-4 text-xs text-muted-foreground">
                        <span>Channel: {summary.videoChannel}</span>
                        <span>Published: {summary.videoPublishedAt ? new Date(summary.videoPublishedAt).toLocaleDateString() : 'Unknown'}</span>
                        <span>Read Time: {summary.timeSaved}</span>
                      </div>
                    </div>
                  </Card>
                ))}
              </div>
            </CardContent>
          </Card>
        )}

        {/* No Results */}
        {searchResults.length === 0 && (naturalQuery || booleanQuery) && !isSearching && (
          <Card>
            <CardContent className="text-center py-8">
              <Search className="w-12 h-12 mx-auto text-muted-foreground mb-4" />
              <h3 className="text-lg font-semibold mb-2">No Results Found</h3>
              <p className="text-muted-foreground">
                Try adjusting your search terms or filters to find what you're looking for.
              </p>
            </CardContent>
          </Card>
        )}
      </div>
    </EnhancedLayout>
  );
}