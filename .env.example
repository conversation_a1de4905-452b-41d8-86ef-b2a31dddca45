# YouTube API Configuration
YOUTUBE_API_KEY=your_youtube_api_key_here

# OpenAI Configuration
OPENAI_API_KEY=your_openai_api_key_here

# Database Configuration
DATABASE_URL=your_database_url_here

# Session Configuration
SESSION_SECRET=your_session_secret_here

# Proxy Configuration for Transcript Extraction
# Format: JSON array of proxy objects
# Example with residential proxies (recommended for production):
PROXY_LIST=[
  {
    "host": "proxy.example.com",
    "port": 8080,
    "username": "your_username",
    "password": "your_password",
    "protocol": "http"
  },
  {
    "host": "socks-proxy.example.com",
    "port": 1080,
    "username": "your_username",
    "password": "your_password",
    "protocol": "socks5"
  }
]

# Popular Proxy Services for YouTube Transcript Extraction:
# 
# 1. Webshare (Recommended - $5-15/month)
#    - Residential proxies with high success rate
#    - Format: {"host": "proxy.webshare.io", "port": 80, "username": "user", "password": "pass", "protocol": "http"}
#
# 2. Bright Data (Premium - $500+/month)
#    - Enterprise-grade with 99%+ uptime
#    - Format: {"host": "zproxy.lum-superproxy.io", "port": 22225, "username": "user", "password": "pass", "protocol": "http"}
#
# 3. Smartproxy (Mid-tier - $75+/month)
#    - Good balance of price and reliability
#    - Format: {"host": "gate.smartproxy.com", "port": 10000, "username": "user", "password": "pass", "protocol": "http"}

# Transcript Extraction Configuration
TRANSCRIPT_MAX_RETRIES=3
TRANSCRIPT_RETRY_DELAY=1000
TRANSCRIPT_ENABLE_METRICS=true