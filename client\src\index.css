@tailwind base;
@tailwind components;
@tailwind utilities;

/* Fix form input display issues */
input[type="text"] {
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", <PERSON><PERSON>, "Helvetica Neue", <PERSON><PERSON>, sans-serif !important;
  -webkit-text-size-adjust: 100% !important;
  text-rendering: optimizeLegibility !important;
}

:root {
  --background: 210 11% 98%; /* #F5F7FA */
  --foreground: 217 32% 17%; /* #1E293B */
  --muted: 210 11% 96%; /* #F1F5F9 */
  --muted-foreground: 215 25% 27%; /* #334155 */
  --popover: 0 0% 100%; /* #FFFFFF */
  --popover-foreground: 217 32% 17%; /* #1E293B */
  --card: 0 0% 100%; /* #FFFFFF */
  --card-foreground: 217 32% 17%; /* #1E293B */
  --border: 214 13% 91%; /* #E2E8F0 */
  --input: 214 13% 91%; /* #E2E8F0 */
  --primary: 217 91% 60%; /* #2563EB */
  --primary-foreground: 210 40% 98%; /* #F8FAFC */
  --secondary: 210 11% 96%; /* #F1F5F9 */
  --secondary-foreground: 217 32% 17%; /* #1E293B */
  --accent: 142 76% 36%; /* #10B981 */
  --accent-foreground: 0 0% 100%; /* #FFFFFF */
  --destructive: 0 84% 60%; /* #EF4444 */
  --destructive-foreground: 0 0% 100%; /* #FFFFFF */
  --ring: 217 91% 60%; /* #2563EB */
  --radius: 0.5rem;
  
  /* Chart colors */
  --chart-1: 217 91% 60%; /* #2563EB */
  --chart-2: 142 76% 36%; /* #10B981 */
  --chart-3: 45 93% 47%; /* #F59E0B */
  --chart-4: 346 87% 43%; /* #EF4444 */
  --chart-5: 262 83% 58%; /* #8B5CF6 */
}

.dark {
  --background: 217 32% 17%; /* #1E293B */
  --foreground: 210 40% 98%; /* #F8FAFC */
  --muted: 217 32% 19%; /* #0F172A */
  --muted-foreground: 215 20% 65%; /* #94A3B8 */
  --popover: 217 32% 17%; /* #1E293B */
  --popover-foreground: 210 40% 98%; /* #F8FAFC */
  --card: 217 32% 17%; /* #1E293B */
  --card-foreground: 210 40% 98%; /* #F8FAFC */
  --border: 215 28% 17%; /* #334155 */
  --input: 215 28% 17%; /* #334155 */
  --primary: 217 91% 60%; /* #2563EB */
  --primary-foreground: 210 40% 98%; /* #F8FAFC */
  --secondary: 215 28% 17%; /* #334155 */
  --secondary-foreground: 210 40% 98%; /* #F8FAFC */
  --accent: 142 76% 36%; /* #10B981 */
  --accent-foreground: 0 0% 100%; /* #FFFFFF */
  --destructive: 0 84% 60%; /* #EF4444 */
  --destructive-foreground: 0 0% 100%; /* #FFFFFF */
  --ring: 217 91% 60%; /* #2563EB */
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply font-sans antialiased bg-background text-foreground;
  }
}

/* Custom utilities */
.text-secondary {
  color: hsl(var(--foreground));
}

.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* Prose styling for summary content */
.prose h4 {
  margin-bottom: 0.5rem;
  font-weight: 600;
}

.prose ul {
  margin: 0;
  padding-left: 0;
}

.prose li {
  margin: 0;
  list-style: none;
}
