# VideoSummarize AI - GitHub Pull Script (PowerShell)
# Pulls the latest changes from GitHub with proper Windows credential management

param(
    [switch]$Force,
    [switch]$Stash,
    [switch]$Help
)

function Show-Help {
    Write-Host "VideoSummarizer GitHub Pull Script" -ForegroundColor Cyan
    Write-Host ""
    Write-Host "Usage:" -ForegroundColor Yellow
    Write-Host "  .\pull-from-github.ps1           # Interactive mode"
    Write-Host "  .\pull-from-github.ps1 -Stash    # Auto-stash local changes"
    Write-Host "  .\pull-from-github.ps1 -Force    # Force pull (merge with local changes)"
    Write-Host "  .\pull-from-github.ps1 -Help     # Show this help"
    Write-Host ""
    Write-Host "Examples:" -ForegroundColor Yellow
    Write-Host "  .\pull-from-github.ps1           # Will prompt for options if local changes exist"
    Write-Host "  .\pull-from-github.ps1 -Stash    # Automatically stash changes before pull"
    Write-Host ""
}

function Write-Status {
    param([string]$Message, [string]$Color = "White")
    Write-Host $Message -ForegroundColor $Color
}

function Write-Success {
    param([string]$Message)
    Write-Status "✅ $Message" "Green"
}

function Write-Warning {
    param([string]$Message)
    Write-Status "⚠️ $Message" "Yellow"
}

function Write-Error {
    param([string]$Message)
    Write-Status "❌ $Message" "Red"
}

function Write-Info {
    param([string]$Message)
    Write-Status "🔄 $Message" "Cyan"
}

# Show help if requested
if ($Help) {
    Show-Help
    exit 0
}

Write-Info "Pulling latest changes from GitHub..."

# Check if git is initialized
if (-not (Test-Path ".git")) {
    Write-Error "Not a git repository. Please run this from the root of your git repository."
    exit 1
}

# Check for local changes
$hasLocalChanges = $false
try {
    $gitStatus = git status --porcelain 2>$null
    if ($gitStatus) {
        $hasLocalChanges = $true
    }
} catch {
    Write-Error "Failed to check git status."
    exit 1
}

$choice = 2  # Default to merge
$stashed = $false

if ($hasLocalChanges) {
    if ($Stash) {
        $choice = 1
        Write-Info "Auto-stashing local changes (--Stash flag used)..."
    } elseif ($Force) {
        $choice = 2
        Write-Info "Force pulling with local changes (--Force flag used)..."
    } else {
        Write-Warning "You have local changes that might conflict with the pull."
        Write-Host ""
        Write-Host "Options:" -ForegroundColor Yellow
        Write-Host "  1. Stash changes and pull"
        Write-Host "  2. Pull and merge with local changes"
        Write-Host "  3. Cancel"
        Write-Host ""
        
        do {
            $choice = Read-Host "Choose an option (1-3)"
        } while ($choice -notin @("1", "2", "3"))
        
        $choice = [int]$choice
    }
    
    switch ($choice) {
        1 {
            Write-Info "Stashing local changes..."
            try {
                git stash push -m "Auto-stash before pull $(Get-Date -Format 'yyyy-MM-dd HH:mm:ss')"
                $stashed = $true
                Write-Success "Local changes stashed successfully!"
            } catch {
                Write-Error "Failed to stash changes: $_"
                exit 1
            }
        }
        2 {
            Write-Info "Will attempt to merge changes..."
        }
        3 {
            Write-Warning "Operation cancelled."
            exit 0
        }
        default {
            Write-Error "Invalid option. Operation cancelled."
            exit 1
        }
    }
}

# Get current branch
try {
    $currentBranch = git branch --show-current
    if (-not $currentBranch) {
        $currentBranch = "main"
    }
} catch {
    Write-Warning "Could not determine current branch, defaulting to 'main'"
    $currentBranch = "main"
}

# Pull the latest changes
Write-Info "Pulling latest changes from origin/$currentBranch..."
try {
    $pullResult = git pull origin $currentBranch 2>&1
    $pullExitCode = $LASTEXITCODE
    
    if ($pullExitCode -eq 0) {
        Write-Success "Successfully pulled latest changes!"
        
        # If we stashed changes, pop them back
        if ($stashed) {
            Write-Info "Applying stashed changes..."
            try {
                git stash pop
                
                # Check for conflicts
                $conflicts = git diff --name-only --diff-filter=U 2>$null
                if ($conflicts) {
                    Write-Warning "There are merge conflicts in the following files:"
                    $conflicts | ForEach-Object { Write-Host "  - $_" -ForegroundColor Red }
                    Write-Warning "Please resolve them manually using 'git status' and 'git add' when done."
                } else {
                    Write-Success "Stashed changes applied successfully!"
                }
            } catch {
                Write-Error "Failed to apply stashed changes: $_"
                Write-Info "Your changes are still in the stash. Use 'git stash pop' to apply them manually."
            }
        }
        
        # Show what changed
        Write-Info "Summary of changes:"
        try {
            git log -1 --stat --oneline
        } catch {
            Write-Info "Could not display change summary."
        }
        
    } else {
        Write-Error "Pull failed. Error details:"
        Write-Host $pullResult -ForegroundColor Red
        
        # If we stashed changes, pop them back
        if ($stashed) {
            Write-Info "Restoring stashed changes..."
            try {
                git stash pop
                Write-Success "Stashed changes restored."
            } catch {
                Write-Warning "Could not restore stashed changes automatically. Use 'git stash pop' manually."
            }
        }
        
        exit 1
    }
} catch {
    Write-Error "Unexpected error during pull: $_"
    
    # If we stashed changes, pop them back
    if ($stashed) {
        Write-Info "Restoring stashed changes..."
        try {
            git stash pop
        } catch {
            Write-Warning "Could not restore stashed changes. Use 'git stash pop' manually."
        }
    }
    
    exit 1
}

Write-Success "Pull operation completed!"

# Show current status
Write-Info "Current repository status:"
try {
    git status --short
    if (-not (git status --porcelain)) {
        Write-Success "Working directory is clean."
    }
} catch {
    Write-Warning "Could not display repository status."
}
