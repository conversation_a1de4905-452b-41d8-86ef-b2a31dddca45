import { storage } from "./storage-simple";
import OpenAI from "openai";
import { YoutubeTranscript } from "youtube-transcript";

const openai = new OpenAI({ apiKey: process.env.OPENAI_API_KEY });

interface ChannelVideo {
  id: string;
  title: string;
  publishedAt: string;
  description: string;
  thumbnails: {
    default: { url: string };
    medium?: { url: string };
    high?: { url: string };
  };
}

export class ChannelMonitoringService {
  private isRunning = false;
  private processingJobs = new Set<number>();

  async startMonitoring() {
    if (this.isRunning) return;
    
    this.isRunning = true;
    console.log("Channel monitoring service started");
    
    // Check for new videos every hour
    setInterval(() => {
      this.processChannels().catch(error => {
        console.error("Error in channel monitoring:", error);
      });
    }, 60 * 60 * 1000);

    // Initial check
    this.processChannels();
  }

  async processChannels() {
    try {
      const activeChannels = await storage.getActiveChannels();
      console.log(`Processing ${activeChannels.length} active channels`);

      // Check channels every 8 hours (8 * 60 * 60 * 1000 = 28,800,000 ms)
      const EIGHT_HOURS_MS = 8 * 60 * 60 * 1000;
      const now = new Date();

      for (const channel of activeChannels) {
        // Skip if already processing
        if (this.processingJobs.has(channel.id)) {
          continue;
        }

        // Skip if checked within the last 8 hours
        if (channel.lastChecked) {
          const lastCheckedTime = new Date(channel.lastChecked).getTime();
          const timeSinceLastCheck = now.getTime() - lastCheckedTime;
          
          if (timeSinceLastCheck < EIGHT_HOURS_MS) {
            const hoursRemaining = Math.ceil((EIGHT_HOURS_MS - timeSinceLastCheck) / (60 * 60 * 1000));
            console.log(`Skipping channel ${channel.channelName}, checked ${hoursRemaining}h ago`);
            continue;
          }
        }

        console.log(`Checking channel ${channel.channelName} for new videos`);
        this.processChannel(channel.id, channel.channelId, channel.userId);
      }
    } catch (error) {
      console.error("Error processing channels:", error);
    }
  }

  private async processChannel(channelDbId: number, channelId: string, userId: string) {
    if (this.processingJobs.has(channelDbId)) return;

    this.processingJobs.add(channelDbId);

    try {
      // Create monitoring job
      const job = await storage.createMonitoringJob({
        channelId: channelDbId,
        status: "running",
        startedAt: new Date(),
        videosProcessed: 0,
      });

      console.log(`Starting monitoring job ${job.id} for channel ${channelId}`);

      // Get recent videos from YouTube
      const videos = await this.getChannelVideos(channelId, 10);
      let processedCount = 0;

      for (const video of videos) {
        try {
          // Check if we already have this video
          const existingSummary = await storage.getSummaryByVideoId(userId, video.id);
          if (existingSummary) {
            console.log(`Skipping existing video: ${video.title}`);
            continue;
          }

          // Process the video
          await this.processVideo(video, channelDbId, userId, channelId);
          processedCount++;

          // Update job progress
          await storage.updateMonitoringJob(job.id, {
            videosProcessed: processedCount,
            lastVideoId: video.id,
          });

          // Small delay to avoid rate limits
          await new Promise(resolve => setTimeout(resolve, 2000));

        } catch (error) {
          console.error(`Error processing video ${video.id}:`, error);
        }
      }

      // Complete the job
      await storage.updateMonitoringJob(job.id, {
        status: "completed",
        completedAt: new Date(),
        videosProcessed: processedCount,
      });

      // Update channel last checked time
      await storage.updateChannel(channelDbId, userId, {
        lastChecked: new Date(),
        videoCount: processedCount,
      });

      console.log(`Completed monitoring job ${job.id}, processed ${processedCount} videos`);

    } catch (error) {
      console.error(`Error in channel monitoring job for channel ${channelId}:`, error);
      
      // Mark job as failed
      const jobs = await storage.getChannelJobs(channelDbId);
      const runningJob = jobs.find(j => j.status === "running");
      if (runningJob) {
        await storage.updateMonitoringJob(runningJob.id, {
          status: "failed",
          errorMessage: error instanceof Error ? error.message : "Unknown error",
          completedAt: new Date(),
        });
      }
    } finally {
      this.processingJobs.delete(channelDbId);
    }
  }

  private async processVideo(video: ChannelVideo, channelDbId: number, userId: string, channelId: string) {
    console.log(`Processing video: ${video.title}`);

    // Get video metadata
    const metadata = await this.getVideoMetadata(video.id);
    
    // Get transcript
    let transcript = "";
    try {
      const transcriptData = await YoutubeTranscript.fetchTranscript(video.id);
      transcript = transcriptData.map(item => item.text).join(' ');
      console.log(`Successfully fetched transcript: ${transcript.length} characters`);
    } catch (error) {
      console.log(`No transcript available for video: ${video.title}`);
      transcript = video.description || "";
    }

    // Generate AI summary with structured sections
    const summary = await this.generateEnhancedSummary(video.id, video.title, transcript || video.description);

    // Generate embeddings for vector search
    const summaryEmbedding = await this.generateEmbedding(summary);
    const transcriptEmbedding = transcript ? await this.generateEmbedding(transcript) : null;

    // Extract topics and entities
    const topics = await this.extractTopics(summary, transcript);
    const entities = await this.extractEntities(summary, transcript);

    // Create summary record
    await storage.createSummary({
      userId,
      channelId: channelDbId,
      videoId: video.id,
      videoTitle: video.title,
      videoChannel: metadata.channel,
      videoChannelId: channelId,
      videoThumbnail: metadata.thumbnail,
      videoDuration: this.parseDuration(metadata.duration),
      videoViews: this.formatViewCount(metadata.views),
      videoPublishedAt: new Date(video.publishedAt),
      videoDescription: video.description,
      videoTranscript: transcript,
      summaryContent: summary,
      readTime: this.calculateReadTime(summary),
      timeSaved: this.calculateTimeSaved(metadata.duration),
      summaryEmbedding: JSON.stringify(summaryEmbedding),
      transcriptEmbedding: transcriptEmbedding ? JSON.stringify(transcriptEmbedding) : null,
      topics: JSON.stringify(topics),
      keyEntities: JSON.stringify(entities),
      mcpMetadata: JSON.stringify({
        channelId,
        videoId: video.id,
        publishedAt: video.publishedAt,
        processedAt: new Date().toISOString(),
      }),
      isMonitored: true,
    });

    console.log(`Successfully processed and stored summary for: ${video.title}`);
  }

  private async getChannelVideos(channelId: string, maxResults: number = 10): Promise<ChannelVideo[]> {
    const response = await fetch(
      `https://www.googleapis.com/youtube/v3/search?channelId=${channelId}&key=${process.env.YOUTUBE_API_KEY}&part=snippet&type=video&order=date&maxResults=${maxResults}`
    );
    
    if (!response.ok) {
      throw new Error(`YouTube API error: ${response.status}`);
    }
    
    const data = await response.json();
    return data.items?.map((item: any) => ({
      id: item.id.videoId,
      title: item.snippet.title,
      publishedAt: item.snippet.publishedAt,
      description: item.snippet.description,
      thumbnails: item.snippet.thumbnails,
    })) || [];
  }

  private async getVideoMetadata(videoId: string) {
    const response = await fetch(
      `https://www.googleapis.com/youtube/v3/videos?id=${videoId}&key=${process.env.YOUTUBE_API_KEY}&part=snippet,contentDetails,statistics`
    );
    
    if (!response.ok) {
      throw new Error(`YouTube API error: ${response.status}`);
    }
    
    const data = await response.json();
    if (!data.items || data.items.length === 0) {
      throw new Error('Video not found');
    }
    
    const video = data.items[0];
    return {
      title: video.snippet.title,
      channel: video.snippet.channelTitle,
      thumbnail: video.snippet.thumbnails.maxres?.url || video.snippet.thumbnails.high?.url,
      duration: video.contentDetails.duration,
      views: parseInt(video.statistics.viewCount || '0'),
      publishedAt: video.snippet.publishedAt,
      description: video.snippet.description || ''
    };
  }

  private async generateEnhancedSummary(videoId: string, videoTitle: string, content: string): Promise<string> {
    const prompt = `Analyze this YouTube video and create a comprehensive 8-section summary:

Video Title: ${videoTitle}
Content: ${content}

Please provide a structured summary with these exact sections:
1. **Title Explained** - Analyze if the title accurately represents the content
2. **Who?** - Identify key people, speakers, or personalities mentioned
3. **Key Takeaways** - List the main points and insights (3-5 bullet points)
4. **Important Sections** - Highlight key moments (include estimated timestamps if possible)
5. **Detailed Analysis** - Comprehensive breakdown of the content and its significance
6. **Tools/Services Mentioned** - List any software, platforms, or services discussed
7. **Important Quotes** - Notable statements or insights from the video
8. **Direct Implications** - Actionable outcomes and next steps for viewers

Make this comprehensive and valuable for building a knowledge base of ideas.`;

    const response = await openai.chat.completions.create({
      model: "gpt-4o", // the newest OpenAI model is "gpt-4o" which was released May 13, 2024. do not change this unless explicitly requested by the user
      messages: [{ role: "user", content: prompt }],
      max_tokens: 2000,
    });

    return response.choices[0].message.content || "";
  }

  private async generateEmbedding(text: string): Promise<number[]> {
    const response = await openai.embeddings.create({
      model: "text-embedding-3-small",
      input: text.substring(0, 8000), // Limit text length
    });

    return response.data[0].embedding;
  }

  private async extractTopics(summary: string, transcript: string): Promise<string[]> {
    const text = summary + " " + transcript;
    const response = await openai.chat.completions.create({
      model: "gpt-4o",
      messages: [{
        role: "user",
        content: `Extract 5-10 main topics/themes from this content. Return only a JSON array of topic strings: ${text.substring(0, 4000)}`
      }],
      response_format: { type: "json_object" },
    });

    try {
      const result = JSON.parse(response.choices[0].message.content || "{}");
      return result.topics || [];
    } catch {
      return [];
    }
  }

  private async extractEntities(summary: string, transcript: string): Promise<string[]> {
    const text = summary + " " + transcript;
    const response = await openai.chat.completions.create({
      model: "gpt-4o",
      messages: [{
        role: "user",
        content: `Extract key entities (people, companies, products, concepts) from this content. Return only a JSON array of entity strings: ${text.substring(0, 4000)}`
      }],
      response_format: { type: "json_object" },
    });

    try {
      const result = JSON.parse(response.choices[0].message.content || "{}");
      return result.entities || [];
    } catch {
      return [];
    }
  }

  private parseDuration(duration: string): string {
    const match = duration.match(/PT(?:(\d+)H)?(?:(\d+)M)?(?:(\d+)S)?/);
    if (!match) return "0:00";
    
    const hours = parseInt(match[1] || '0');
    const minutes = parseInt(match[2] || '0');
    const seconds = parseInt(match[3] || '0');
    
    if (hours > 0) {
      return `${hours}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
    } else {
      return `${minutes}:${seconds.toString().padStart(2, '0')}`;
    }
  }

  private formatViewCount(count: number): string {
    if (count >= 1000000) {
      return Math.floor(count / 100000) / 10 + 'M views';
    } else if (count >= 1000) {
      return Math.floor(count / 100) / 10 + 'K views';
    }
    return count + ' views';
  }

  private calculateReadTime(text: string): string {
    const wordsPerMinute = 200;
    const words = text.split(' ').length;
    const minutes = Math.ceil(words / wordsPerMinute);
    return `${minutes} min read`;
  }

  private calculateTimeSaved(duration: string): string {
    const match = duration.match(/PT(?:(\d+)H)?(?:(\d+)M)?(?:(\d+)S)?/);
    if (!match) return "0 min saved";
    
    const hours = parseInt(match[1] || '0');
    const minutes = parseInt(match[2] || '0');
    const totalMinutes = hours * 60 + minutes;
    
    if (totalMinutes > 60) {
      return `${Math.floor(totalMinutes / 60)}h ${totalMinutes % 60}m saved`;
    } else {
      return `${totalMinutes}m saved`;
    }
  }
}

export const channelMonitor = new ChannelMonitoringService();