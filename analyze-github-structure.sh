#!/bin/bash

# GitHub Repository Structure Analysis Script
# Compares current repository structure to recommended best practices

echo "🔍 GitHub Repository Structure Analysis"
echo "========================================"

# Check if git is installed
if ! command -v git &> /dev/null; then
    echo "❌ Git is not installed. Please install Git first."
    exit 1
fi

# Check if current directory is a git repository
if [ ! -d ".git" ]; then
    echo "❌ Current directory is not a Git repository."
    echo "Please run this script from the root of your Git repository."
    exit 1
fi

# Get repository information
REPO_URL=$(git config --get remote.origin.url)
REPO_NAME=$(basename -s .git "$REPO_URL")
CURRENT_BRANCH=$(git branch --show-current)

echo "📊 Repository Information:"
echo "  Repository: $REPO_NAME"
echo "  Remote URL: $REPO_URL"
echo "  Current Branch: $CURRENT_BRANCH"
echo ""

# Analyze repository structure
echo "📁 Current Repository Structure:"
find . -type d -not -path "*/\.*" -not -path "*/node_modules/*" | sort | sed 's/\.\///' | sed 's/^/  /'
echo ""

# Check for multiple projects in single repository
PROJECT_COUNT=$(find . -maxdepth 1 -type d -not -path "*/\.*" -not -path "." | wc -l)
if [ "$PROJECT_COUNT" -gt 5 ]; then
    echo "⚠️  Potential Issue: Multiple projects in single repository"
    echo "   Found $PROJECT_COUNT top-level directories which may indicate multiple projects."
    echo "   Recommendation: Consider splitting into separate repositories for each project."
    echo ""
fi

# Check for GitHub workflows
if [ ! -d ".github/workflows" ]; then
    echo "⚠️  Missing CI/CD: No GitHub Actions workflows found"
    echo "   Recommendation: Add CI/CD pipelines with GitHub Actions."
    echo "   Create a .github/workflows directory with CI configuration."
    echo ""
fi

# Check for documentation
if [ ! -d "docs" ] && [ $(find . -name "*.md" -not -path "*/node_modules/*" | wc -l) -lt 3 ]; then
    echo "⚠️  Limited Documentation: Few markdown files found"
    echo "   Recommendation: Create a docs/ directory with comprehensive documentation."
    echo "   Include PRD.md, architecture.md, and setup instructions."
    echo ""
fi

# Check for contribution guidelines
if [ ! -f "CONTRIBUTING.md" ] && [ ! -f ".github/CONTRIBUTING.md" ]; then
    echo "⚠️  Missing Contribution Guidelines"
    echo "   Recommendation: Add CONTRIBUTING.md with clear workflow instructions."
    echo ""
fi

# Check for dev container configuration
if [ ! -d ".devcontainer" ]; then
    echo "⚠️  No Dev Container Configuration"
    echo "   Recommendation: Add .devcontainer/devcontainer.json for consistent environments."
    echo ""
fi

# Check branch strategy
BRANCH_COUNT=$(git branch -r | wc -l)
FEATURE_BRANCHES=$(git branch -r | grep "feature/" | wc -l)
DEVELOP_BRANCH=$(git branch -r | grep "develop" | wc -l)

if [ "$FEATURE_BRANCHES" -eq 0 ]; then
    echo "⚠️  Branch Strategy: No feature branches detected"
    echo "   Recommendation: Use feature branches for new development work."
    echo ""
fi

# Generate recommended structure
echo "📋 Recommended Repository Structure:"
echo "  .github/workflows/       # CI/CD configuration"
echo "  .devcontainer/           # Development environment configuration"
echo "  docs/                    # Documentation"
echo "  ├── PRD.md               # Product Requirements Document"
echo "  └── architecture.md      # Architecture documentation"
echo "  src/                     # Source code"
echo "  ├── client/              # Frontend code"
echo "  └── server/              # Backend code"
echo "  tests/                   # Test files"
echo "  CONTRIBUTING.md          # Contribution guidelines"
echo "  README.md                # Project overview"
echo ""

# Generate improvement plan
echo "🚀 Improvement Plan:"
echo "  1. Repository Organization:"
echo "     - Move to a 'one project per repository' model"
echo "     - Organize code into logical directories (src/, docs/, tests/)"
echo ""
echo "  2. Branch Strategy:"
echo "     - Implement GitHub Flow (feature branches + pull requests)"
echo "     - Protect the main branch with required reviews"
echo ""
echo "  3. Environment Consistency:"
echo "     - Add Dev Containers configuration"
echo "     - Document environment setup requirements"
echo ""
echo "  4. Collaboration Workflow:"
echo "     - Create clear contribution guidelines"
echo "     - Implement pull request templates"
echo ""
echo "  5. CI/CD Pipeline:"
echo "     - Add GitHub Actions for testing and deployment"
echo "     - Implement automated code quality checks"
echo ""

# Check for Replit integration
if [ -f ".replit" ] || [ -f "replit.nix" ]; then
    echo "✅ Replit Integration: Detected"
    echo "   Ensure Replit is configured to use the same branch strategy."
    echo ""
else
    echo "ℹ️  Replit Integration: Not detected in this repository"
    echo "   If using Replit, ensure it's properly configured to sync with GitHub."
    echo ""
fi

echo "✨ Analysis Complete!"
echo "Run this script periodically to track your progress toward the recommended structure."