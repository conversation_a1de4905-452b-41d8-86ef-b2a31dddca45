#!/bin/bash

# GitHub Verification Script for VideoSummarize AI
# Checks and displays GitHub repository status

echo "🔍 VideoSummarize AI - GitHub Repository Verification"
echo "==================================================="

# Check if Git is initialized
if [ ! -d ".git" ]; then
    echo "❌ Git repository not initialized"
    echo "Run: ./deploy-to-github.sh to set up"
    exit 1
fi

# Display current Git configuration
echo "📋 Git Configuration:"
echo "  Repository: $(pwd)"
echo "  Current branch: $(git branch --show-current 2>/dev/null || echo 'No branch')"

# Check remote configuration
echo ""
echo "🔗 Remote Configuration:"
if git remote -v &>/dev/null; then
    git remote -v
    REMOTE_URL=$(git remote get-url origin 2>/dev/null)
    if [[ $REMOTE_URL == *"github.com"* ]]; then
        echo "✅ GitHub remote configured correctly"
        echo "📍 Repository URL: $REMOTE_URL"
    else
        echo "⚠️  Remote exists but may not be GitHub"
    fi
else
    echo "❌ No remote repository configured"
    echo "Run: ./deploy-to-github.sh to set up"
fi

# Check local vs remote status
echo ""
echo "📊 Repository Status:"
if git remote get-url origin &>/dev/null; then
    # Fetch latest info from remote
    echo "Checking remote status..."
    if git fetch origin &>/dev/null; then
        LOCAL=$(git rev-parse @ 2>/dev/null)
        REMOTE=$(git rev-parse @{u} 2>/dev/null)
        
        if [ "$LOCAL" = "$REMOTE" ]; then
            echo "✅ Local repository is up to date with GitHub"
        elif [ -z "$REMOTE" ]; then
            echo "⚠️  No upstream branch set - may be first push needed"
        else
            echo "⚠️  Local repository differs from GitHub"
            echo "   Local commits ahead: $(git rev-list --count @{u}..@ 2>/dev/null || echo '0')"
            echo "   Remote commits ahead: $(git rev-list --count @..@{u} 2>/dev/null || echo '0')"
        fi
    else
        echo "❌ Cannot connect to GitHub repository"
        echo "   This might be a new repository that doesn't exist yet"
    fi
fi

# Show recent commits
echo ""
echo "📝 Recent Commits (Local):"
if git log --oneline -5 2>/dev/null; then
    echo ""
else
    echo "No commits found"
fi

# Show current changes
echo "📋 Current Working Directory Status:"
if git status --porcelain | head -10; then
    CHANGES=$(git status --porcelain | wc -l)
    if [ "$CHANGES" -gt 0 ]; then
        echo "⚠️  $CHANGES uncommitted changes detected"
        echo "Run: ./sync-github.sh to push changes"
    fi
else
    echo "✅ Working directory clean"
fi

# Test GitHub connectivity
echo ""
echo "🌐 Testing GitHub Connectivity:"
if command -v curl &> /dev/null; then
    if curl -s --connect-timeout 5 https://api.github.com/zen &>/dev/null; then
        echo "✅ GitHub API accessible"
    else
        echo "❌ Cannot reach GitHub API"
    fi
else
    echo "ℹ️  curl not available for connectivity test"
fi

echo ""
echo "💡 Quick Actions:"
echo "  First time setup: ./deploy-to-github.sh"
echo "  Push changes: ./sync-github.sh"
echo "  Check this status: ./verify-github.sh"