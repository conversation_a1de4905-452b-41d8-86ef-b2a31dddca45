import puppeteer from 'puppeteer-extra';
import StealthPlugin from 'puppeteer-extra-plugin-stealth';

// Configure stealth plugin to avoid detection
puppeteer.use(StealthPlugin());

/**
 * Scrapes YouTube transcript using browser automation
 */
export async function scrapeYouTubeTranscript(videoId: string): Promise<string> {
  console.log(`Attempting to scrape transcript for video ID: ${videoId}`);
  
  const browser = await puppeteer.launch({
    headless: true,
    args: ['--no-sandbox', '--disable-setuid-sandbox']
  });
  
  try {
    const page = await browser.newPage();
    
    // Set user agent to avoid detection
    await page.setUserAgent('Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36');
    
    // Navigate to video page
    await page.goto(`https://www.youtube.com/watch?v=${videoId}`, {
      waitUntil: 'networkidle2',
      timeout: 30000
    });
    
    // Click on "..." menu to open options
    await page.waitForSelector('button.ytp-button.ytp-settings-button');
    await page.click('button.ytp-button.ytp-settings-button');
    
    // Wait for menu and click on "Open transcript"
    // Note: Selectors may change; implement robust waiting and retry logic
    await page.waitForSelector('div.ytp-panel-menu');
    
    // Find and click transcript option
    const transcriptButton = await page.evaluateHandle(() => {
      const menuItems = Array.from(document.querySelectorAll('.ytp-menuitem'));
      return menuItems.find(item => item.textContent.includes('Transcript'));
    });
    
    if (transcriptButton) {
      await transcriptButton.click();
      
      // Wait for transcript panel to load
      await page.waitForSelector('.ytd-transcript-renderer');
      
      // Extract transcript text
      const transcriptText = await page.evaluate(() => {
        const segments = Array.from(document.querySelectorAll('.ytd-transcript-segment-renderer'));
        return segments.map(segment => {
          const textElement = segment.querySelector('#content-text');
          return textElement ? textElement.textContent.trim() : '';
        }).join(' ');
      });
      
      return transcriptText;
    }
    
    throw new Error('Transcript option not found in video settings');
  } finally {
    await browser.close();
  }
}