// Create some test monitored summaries for the Starter Story channel
async function createTestSummaries() {
  const summaries = [
    {
      userId: "40640447",
      channelId: 1, // Starter Story channel ID from database
      videoId: "dQw4w9WgXcQ",
      videoTitle: "How I Built a $1M Business in 12 Months",
      videoChannel: "Starter Story", 
      videoThumbnail: "https://i.ytimg.com/vi/dQw4w9WgXcQ/hqdefault.jpg",
      videoDuration: "15:32",
      videoViews: "125K views",
      videoPublishedAt: new Date('2024-01-15'),
      summaryContent: `<div class="bg-slate-50 p-4 rounded-lg mb-4">
        <h4 class="font-semibold text-secondary mb-3"><i class="fas fa-lightbulb mr-2"></i>Key Takeaways:</h4>
        <ul class="space-y-2 text-slate-700">
          <li>• Started with $500 and a laptop</li>
          <li>• Found profitable niche in B2B software</li>
          <li>• Built MVP in 3 months</li>
          <li>• Reached $1M ARR through content marketing</li>
        </ul>
      </div>`,
      readTime: "3 min read",
      timeSaved: "12 min saved",
      isMonitored: true
    },
    {
      userId: "40640447", 
      channelId: 1,
      videoId: "xyz123abc",
      videoTitle: "The $50K Monthly Newsletter That Started as a Side Project",
      videoChannel: "Starter Story",
      videoThumbnail: "https://i.ytimg.com/vi/xyz123abc/hqdefault.jpg", 
      videoDuration: "22:18",
      videoViews: "89K views",
      videoPublishedAt: new Date('2024-02-01'),
      summaryContent: `<div class="bg-slate-50 p-4 rounded-lg mb-4">
        <h4 class="font-semibold text-secondary mb-3"><i class="fas fa-chart-line mr-2"></i>Growth Strategy:</h4>
        <ul class="space-y-2 text-slate-700">
          <li>• Started newsletter with 0 subscribers</li>
          <li>• Focused on actionable business insights</li>
          <li>• Grew to 50K subscribers in 18 months</li>
          <li>• Monetized through premium subscriptions and sponsorships</li>
        </ul>
      </div>`,
      readTime: "4 min read", 
      timeSaved: "18 min saved",
      isMonitored: true
    }
  ];

  return summaries;
}

export { createTestSummaries };