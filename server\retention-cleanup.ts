
import { storage } from './storage-simple';

// Background job to handle data retention cleanup
export class RetentionCleanupJob {
  private intervalId: NodeJS.Timeout | null = null;
  private readonly CLEANUP_INTERVAL = 24 * 60 * 60 * 1000; // 24 hours in milliseconds

  start() {
    console.log('Starting retention cleanup job...');
    
    // Run immediately on start
    this.runCleanup();
    
    // Then run every 24 hours
    this.intervalId = setInterval(() => {
      this.runCleanup();
    }, this.CLEANUP_INTERVAL);
  }

  stop() {
    if (this.intervalId) {
      clearInterval(this.intervalId);
      this.intervalId = null;
      console.log('Retention cleanup job stopped');
    }
  }

  private async runCleanup() {
    try {
      console.log('Running data retention cleanup...');
      
      // First, aggregate expired feedback data
      await storage.aggregateExpiredFeedback();
      console.log('Feedback data aggregated successfully');
      
      // Then delete expired individual feedback records
      const deletedCount = await storage.deleteExpiredFeedback();
      console.log(`Deleted ${deletedCount} expired feedback records`);
      
    } catch (error) {
      console.error('Error during retention cleanup:', error);
    }
  }

  // Manual trigger for testing or admin operations
  async runManualCleanup() {
    await this.runCleanup();
  }
}

export const retentionCleanup = new RetentionCleanupJob();
