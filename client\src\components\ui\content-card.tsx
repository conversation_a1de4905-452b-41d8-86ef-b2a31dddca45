import * as React from "react";
import { <PERSON>, <PERSON><PERSON>ontent, CardHeader } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { cn } from "@/lib/utils";
import { 
  Clock, 
  Eye, 
  Share, 
  Bookmark, 
  MoreHorizontal,
  PlayCircle,
  Download
} from "lucide-react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";

interface ContentCardProps {
  variant?: "summary" | "channel" | "topic";
  title: string;
  subtitle?: string;
  description?: string;
  thumbnail?: string;
  duration?: string;
  metadata?: {
    readTime?: string;
    timeSaved?: string;
    views?: string;
    createdAt?: string;
  };
  status?: "active" | "inactive" | "monitored" | "new";
  colorAccent?: string;
  actions?: {
    primary?: {
      label: string;
      onClick: () => void;
      icon?: React.ComponentType<{ className?: string }>;
    };
    secondary?: Array<{
      label: string;
      onClick: () => void;
      icon?: React.ComponentType<{ className?: string }>;
    }>;
  };
  className?: string;
  children?: React.ReactNode;
}

export function ContentCard({
  variant = "summary",
  title,
  subtitle,
  description,
  thumbnail,
  duration,
  metadata,
  status,
  colorAccent,
  actions,
  className,
  children,
}: ContentCardProps) {
  const getStatusVariant = (status?: string) => {
    switch (status) {
      case "active":
      case "monitored":
        return "default";
      case "new":
        return "secondary";
      case "inactive":
        return "outline";
      default:
        return "outline";
    }
  };

  const getStatusColor = (status?: string) => {
    switch (status) {
      case "active":
      case "monitored":
        return "text-green-600";
      case "new":
        return "text-blue-600";
      case "inactive":
        return "text-gray-500";
      default:
        return "text-gray-500";
    }
  };

  return (
    <Card 
      className={cn(
        "group hover:shadow-lg transition-all duration-200 hover:-translate-y-1 cursor-pointer",
        "focus-within:ring-2 focus-within:ring-primary focus-within:ring-offset-2",
        className
      )}
      style={colorAccent ? { borderLeftColor: colorAccent, borderLeftWidth: '4px' } : undefined}
    >
      {/* Thumbnail Section */}
      {thumbnail && (
        <div className="relative aspect-video overflow-hidden rounded-t-lg">
          <img 
            src={thumbnail} 
            alt={title}
            className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-200"
          />
          
          {/* Overlay Content */}
          <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent" />
          
          {/* Duration Badge */}
          {duration && (
            <div className="absolute bottom-2 right-2 bg-black/80 text-white px-2 py-1 rounded text-xs font-medium">
              {duration}
            </div>
          )}
          
          {/* Play Button for Videos */}
          {variant === "summary" && (
            <div className="absolute inset-0 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity">
              <div className="bg-white/20 backdrop-blur-sm rounded-full p-3">
                <PlayCircle className="h-8 w-8 text-white" />
              </div>
            </div>
          )}
          
          {/* Status Badge */}
          {status && (
            <div className="absolute top-2 left-2">
              <Badge 
                variant={getStatusVariant(status)}
                className={cn("text-xs", getStatusColor(status))}
              >
                {status}
              </Badge>
            </div>
          )}
        </div>
      )}

      <CardContent className="p-4">
        {/* Header Section */}
        <div className="flex items-start justify-between mb-3">
          <div className="flex-1 min-w-0">
            {/* Color indicator for topics */}
            {variant === "topic" && colorAccent && (
              <div className="flex items-center gap-2 mb-2">
                <div 
                  className="w-3 h-3 rounded-full"
                  style={{ backgroundColor: colorAccent }}
                />
                <Badge variant="outline" className="text-xs">
                  Topic
                </Badge>
              </div>
            )}
            
            <h3 className="font-semibold text-sm leading-tight line-clamp-2 mb-1 group-hover:text-primary transition-colors">
              {title}
            </h3>
            
            {subtitle && (
              <p className="text-sm text-muted-foreground mb-2 line-clamp-1">
                {subtitle}
              </p>
            )}
            
            {description && (
              <p className="text-xs text-muted-foreground line-clamp-2 mb-3">
                {description}
              </p>
            )}
          </div>
          
          {/* Actions Menu */}
          {actions?.secondary && (
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button 
                  variant="ghost" 
                  size="sm"
                  className="h-8 w-8 p-0 opacity-60 hover:opacity-100 transition-opacity"
                >
                  <MoreHorizontal className="h-4 w-4" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                {actions.secondary.map((action, index) => (
                  <DropdownMenuItem key={index} onClick={action.onClick}>
                    {action.icon && <action.icon className="mr-2 h-4 w-4" />}
                    {action.label}
                  </DropdownMenuItem>
                ))}
              </DropdownMenuContent>
            </DropdownMenu>
          )}
        </div>

        {/* Metadata Section */}
        {metadata && (
          <div className="flex items-center gap-4 text-xs text-muted-foreground mb-3">
            {metadata.readTime && (
              <div className="flex items-center gap-1">
                <Clock className="h-3 w-3" />
                <span>{metadata.readTime}</span>
              </div>
            )}
            {metadata.timeSaved && (
              <div className="flex items-center gap-1">
                <span>💾</span>
                <span>{metadata.timeSaved}</span>
              </div>
            )}
            {metadata.views && (
              <div className="flex items-center gap-1">
                <Eye className="h-3 w-3" />
                <span>{metadata.views}</span>
              </div>
            )}
          </div>
        )}

        {/* Custom Content */}
        {children}

        {/* Primary Action */}
        {actions?.primary && (
          <div className="pt-3 border-t">
            <Button 
              size="sm" 
              onClick={actions.primary.onClick}
              className="w-full"
            >
              {actions.primary.icon && <actions.primary.icon className="mr-2 h-4 w-4" />}
              {actions.primary.label}
            </Button>
          </div>
        )}
      </CardContent>
    </Card>
  );
}

export default ContentCard;