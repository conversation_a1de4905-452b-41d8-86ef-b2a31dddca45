import { useState, useEffect } from "react";
import { useAuth } from "@/hooks/useAuth";
import { useToast } from "@/hooks/use-toast";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { apiRequest } from "@/lib/queryClient";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { Textarea } from "@/components/ui/textarea";
import ContentCard from "@/components/ui/content-card";
import { 
  Tag, 
  Plus, 
  Pencil, 
  Trash2, 
  Rss,
  MonitorSpeaker,
  Library,
  MoreHorizontal,
  Settings,
  Share,
  BookOpen,
  Users,
  TrendingUp,
  Palette,
  ArrowLeft
} from "lucide-react";
import { Link, useParams } from "wouter";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Label } from "@/components/ui/label";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";

interface Topic {
  id: number;
  name: string;
  description: string;
  parentTopicId?: number;
  colorHex: string;
  topicType: string;
  channelCount: number;
  summaryCount: number;
  createdAt: string;
}

interface Channel {
  id: number;
  channelName: string;
  channelThumbnail: string;
  isMonitored: boolean;
}

interface Summary {
  id: number;
  videoTitle: string;
  videoChannel: string;
  videoThumbnail: string;
  readTime: string;
  createdAt: string;
}

export default function EnhancedTopics() {
  const { user: authUser } = useAuth();
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const params = useParams();
  const [showCreateDialog, setShowCreateDialog] = useState(false);
  const [editingTopic, setEditingTopic] = useState<Topic | null>(null);
  const [selectedTopic, setSelectedTopic] = useState<Topic | null>(null);
  const [viewMode, setViewMode] = useState<"overview" | "details">("overview");
  const [showChannelAssignDialog, setShowChannelAssignDialog] = useState(false);
  const [selectedChannelIds, setSelectedChannelIds] = useState<number[]>([]);

  // Form state for creating/editing topics
  const [formData, setFormData] = useState({
    name: "",
    description: "",
    parentTopicId: "",
    colorHex: "#3b82f6",
    topicType: "general"
  });

  // Fetch topics
  const { data: topics = [], isLoading: topicsLoading } = useQuery<Topic[]>({
    queryKey: ["/api/topics"],
    enabled: !!authUser,
  });

  // Handle URL parameters for direct topic access
  useEffect(() => {
    if (params.id && topics.length > 0) {
      const topicId = parseInt(params.id);
      const topic = topics.find(t => t.id === topicId);
      if (topic) {
        setSelectedTopic(topic);
        setViewMode("details");
      }
    }
  }, [params.id, topics]);

  // Fetch all user channels for assignment
  const { data: allChannels = [], isLoading: allChannelsLoading } = useQuery<Channel[]>({
    queryKey: ["/api/channels"],
    enabled: !!authUser,
  });

  // Fetch channels for topic details
  const { data: topicChannels = [], isLoading: channelsLoading } = useQuery<Channel[]>({
    queryKey: ["/api/topics", selectedTopic?.id, "channels"],
    queryFn: () => selectedTopic ? fetch(`/api/topics/${selectedTopic.id}/channels`).then(r => r.json()) : [],
    enabled: !!authUser && !!selectedTopic,
  });

  // Fetch summaries for topic details
  const { data: topicSummaries = [], isLoading: summariesLoading } = useQuery<Summary[]>({
    queryKey: ["/api/topics", selectedTopic?.id, "summaries"],
    queryFn: () => selectedTopic ? fetch(`/api/topics/${selectedTopic.id}/summaries`).then(r => r.json()) : [],
    enabled: !!authUser && !!selectedTopic,
  });

  // Create topic mutation
  const createTopicMutation = useMutation({
    mutationFn: async (data: any) => {
      const response = await fetch(`/api/topics`, {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify(data),
      });
      if (!response.ok) throw new Error("Failed to create topic");
      return response.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["/api/topics"] });
      setShowCreateDialog(false);
      resetForm();
      toast({
        title: "Success",
        description: "Topic created successfully!",
      });
    },
    onError: (error: any) => {
      toast({
        title: "Error",
        description: error.message || "Failed to create topic",
        variant: "destructive",
      });
    },
  });

  // Update topic mutation
  const updateTopicMutation = useMutation({
    mutationFn: async ({ id, data }: { id: number; data: any }) => {
      const response = await fetch(`/api/topics/${id}`, {
        method: "PATCH",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify(data),
      });
      if (!response.ok) throw new Error("Failed to update topic");
      return response.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["/api/topics"] });
      setEditingTopic(null);
      resetForm();
      toast({
        title: "Success",
        description: "Topic updated successfully!",
      });
    },
    onError: (error: any) => {
      toast({
        title: "Error",
        description: error.message || "Failed to update topic",
        variant: "destructive",
      });
    },
  });

  // Delete topic mutation
  const deleteTopicMutation = useMutation({
    mutationFn: async (id: number) => {
      const response = await fetch(`/api/topics/${id}`, {
        method: "DELETE",
      });
      if (!response.ok) throw new Error("Failed to delete topic");
      return response.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["/api/topics"] });
      toast({
        title: "Success",
        description: "Topic deleted successfully!",
      });
    },
    onError: (error: any) => {
      toast({
        title: "Error",
        description: error.message || "Failed to delete topic",
        variant: "destructive",
      });
    },
  });

  // Assign channels to topic mutation
  const assignChannelsMutation = useMutation({
    mutationFn: async ({ channelIds, topicId }: { channelIds: number[]; topicId: number }) => {
      const promises = channelIds.map(channelId =>
        fetch(`/api/channels/${channelId}/topics`, {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify({ topicId }),
        })
      );

      const responses = await Promise.all(promises);
      const failedResponses = responses.filter(r => !r.ok);

      if (failedResponses.length > 0) {
        throw new Error(`Failed to assign ${failedResponses.length} channels`);
      }

      return responses;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["/api/topics", selectedTopic?.id, "channels"] });
      queryClient.invalidateQueries({ queryKey: ["/api/topics"] });
      setShowChannelAssignDialog(false);
      setSelectedChannelIds([]);
      toast({
        title: "Success",
        description: "Channels assigned to topic successfully!",
      });
    },
    onError: (error: any) => {
      toast({
        title: "Error",
        description: error.message || "Failed to assign channels",
        variant: "destructive",
      });
    },
  });

  const resetForm = () => {
    setFormData({
      name: "",
      description: "",
      parentTopicId: "",
      colorHex: "#3b82f6",
      topicType: "general"
    });
  };

  const handleCreateTopic = () => {
    if (!formData.name.trim()) {
      toast({
        title: "Error",
        description: "Topic name is required",
        variant: "destructive",
      });
      return;
    }

    const submitData = {
      ...formData,
      parentTopicId: formData.parentTopicId ? parseInt(formData.parentTopicId) : null,
    };

    createTopicMutation.mutate(submitData);
  };

  const handleUpdateTopic = () => {
    if (!editingTopic || !formData.name.trim()) return;

    const submitData = {
      ...formData,
      parentTopicId: formData.parentTopicId ? parseInt(formData.parentTopicId) : null,
    };

    updateTopicMutation.mutate({ id: editingTopic.id, data: submitData });
  };

  const handleEditTopic = (topic: Topic) => {
    setEditingTopic(topic);
    setFormData({
      name: topic.name || "",
      description: topic.description || "",
      parentTopicId: topic.parentTopicId ? topic.parentTopicId.toString() : "",
      colorHex: topic.colorHex || "#3b82f6",
      topicType: topic.topicType || "general"
    });
    setShowCreateDialog(true);
  };

  const handleCopyRSSUrl = (topicId: number) => {
    const rssUrl = `${window.location.origin}/rss/topic/${topicId}`;
    navigator.clipboard.writeText(rssUrl);
    toast({
      title: "RSS URL copied!",
      description: "Topic RSS feed URL copied to clipboard",
    });
  };

  // Topic statistics
  const topicStats = {
    total: topics.length,
    withChannels: topics.filter(t => (t.channelCount || 0) > 0).length,
    withContent: topics.filter(t => (t.summaryCount || 0) > 0).length,
    totalChannels: topics.reduce((sum, t) => sum + (t.channelCount || 0), 0),
    totalSummaries: topics.reduce((sum, t) => sum + (t.summaryCount || 0), 0)
  };

  // Color palette options
  const colorOptions = [
    "#3b82f6", "#ef4444", "#10b981", "#f59e0b",
    "#8b5cf6", "#06b6d4", "#f97316", "#84cc16",
    "#ec4899", "#6b7280", "#14b8a6", "#fbbf24"
  ];

  // Topic detail view
  if (viewMode === "details" && selectedTopic) {
    return (
      <div className="space-y-6">
        {/* Topic Detail Header */}
        <div className="flex items-center justify-between bg-white dark:bg-gray-900 p-4 rounded-lg border relative z-10">
          <div className="flex items-center gap-4">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => {
                setViewMode("overview");
                setSelectedTopic(null);
              }}
            >
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Topics
            </Button>
            <div className="flex items-center gap-3">
              <div 
                className="w-4 h-4 rounded-full" 
                style={{ backgroundColor: selectedTopic.colorHex || '#3b82f6' }}
              />
              <div>
                <h1 className="text-2xl font-bold">{selectedTopic.name}</h1>
                <p className="text-muted-foreground">{selectedTopic.description}</p>
              </div>
            </div>
          </div>

          <div className="flex items-center gap-2">
            <Dialog open={showChannelAssignDialog} onOpenChange={setShowChannelAssignDialog}>
              <DialogTrigger asChild>
                <Button
                  variant="outline"
                  size="sm"
                >
                  <Tag className="h-4 w-4 mr-2" />
                  Assign Channels
                </Button>
              </DialogTrigger>
              <DialogContent className="max-w-lg">
                <DialogHeader>
                  <DialogTitle>Assign Channels to Topic</DialogTitle>
                  <DialogDescription>
                    Select channels to assign to "{selectedTopic?.name}" topic
                  </DialogDescription>
                </DialogHeader>

                <div className="space-y-4">
                  {allChannelsLoading ? (
                    <div className="text-center py-4">Loading channels...</div>
                  ) : allChannels.length === 0 ? (
                    <div className="text-center py-4 text-muted-foreground">
                      No channels available. Add channels first from the Channels page.
                    </div>
                  ) : (
                    <div className="space-y-2 max-h-60 overflow-y-auto">
                      {allChannels.map((channel) => (
                        <div key={channel.id} className="flex items-center space-x-2">
                          <input
                            type="checkbox"
                            id={`channel-${channel.id}`}
                            checked={selectedChannelIds.includes(channel.id)}
                            onChange={(e) => {
                              if (e.target.checked) {
                                setSelectedChannelIds([...selectedChannelIds, channel.id]);
                              } else {
                                setSelectedChannelIds(selectedChannelIds.filter(id => id !== channel.id));
                              }
                            }}
                            className="rounded"
                          />
                          <label htmlFor={`channel-${channel.id}`} className="flex items-center space-x-2 flex-1 cursor-pointer">
                            <img 
                              src={channel.channelThumbnail} 
                              alt={channel.channelName}
                              className="w-6 h-6 rounded-full"
                            />
                            <span className="text-sm">{channel.channelName}</span>
                          </label>
                        </div>
                      ))}
                    </div>
                  )}

                  <div className="flex gap-2 pt-4">
                    <Button 
                      onClick={() => {
                        if (selectedTopic && selectedChannelIds.length > 0) {
                          assignChannelsMutation.mutate({
                            channelIds: selectedChannelIds,
                            topicId: selectedTopic.id
                          });
                        }
                      }}
                      disabled={assignChannelsMutation.isPending || selectedChannelIds.length === 0}
                      className="flex-1"
                    >
                      Assign {selectedChannelIds.length} Channel{selectedChannelIds.length !== 1 ? 's' : ''}
                    </Button>
                    <Button 
                      variant="outline" 
                      onClick={() => {
                        setShowChannelAssignDialog(false);
                        setSelectedChannelIds([]);
                      }}
                    >
                      Cancel
                    </Button>
                  </div>
                </div>
              </DialogContent>
            </Dialog>
            <Button
              variant="outline"
              size="sm"
              onClick={() => handleCopyRSSUrl(selectedTopic.id)}
            >
              <Rss className="h-4 w-4 mr-2" />
              RSS Feed
            </Button>
            <Dialog open={showCreateDialog} onOpenChange={setShowCreateDialog}>
              <DialogTrigger asChild>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handleEditTopic(selectedTopic)}
                >
                  <Pencil className="h-4 w-4 mr-2" />
                  Edit Topic
                </Button>
              </DialogTrigger>
              <DialogContent className="max-w-2xl">
                <DialogHeader>
                  <DialogTitle>
                    {editingTopic ? "Edit Topic" : "Create New Topic"}
                  </DialogTitle>
                  <DialogDescription>
                    {editingTopic 
                      ? "Update the topic details below"
                      : "Create a new topic to organize your channels and content"
                    }
                  </DialogDescription>
                </DialogHeader>

                <div className="space-y-4">
                  <div>
                    <Label htmlFor="name">Topic Name *</Label>
                    <Input
                      id="name"
                      type="text"
                      value={formData.name}
                      onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
                      placeholder="e.g., AI News, Tech Reviews"
                      maxLength={100}
                      autoComplete="off"
                    />
                  </div>

                  <div>
                    <Label htmlFor="description">Description</Label>
                    <Textarea
                      id="description"
                      value={formData.description}
                      onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
                      placeholder="Brief description of this topic"
                      rows={3}
                    />
                  </div>

                  <div>
                    <Label htmlFor="color">Color</Label>
                    <div className="flex gap-2 mt-2">
                      {["#3b82f6", "#ef4444", "#22c55e", "#f59e0b", "#8b5cf6", "#ec4899"].map((color) => (
                        <button
                          key={color}
                          type="button"
                          className={`w-8 h-8 rounded-full border-2 ${
                            formData.colorHex === color 
                              ? "border-foreground" 
                              : "border-muted"
                          }`}
                          style={{ backgroundColor: color }}
                          onClick={() => setFormData(prev => ({ ...prev, colorHex: color }))}
                        />
                      ))}
                    </div>
                  </div>

                  <div>
                    <Label htmlFor="type">Type</Label>
                    <select 
                      value={formData.topicType} 
                      onChange={(e) => setFormData(prev => ({ ...prev, topicType: e.target.value }))}
                      className="w-full p-2 border rounded-md"
                    >
                      <option value="general">General</option>
                      <option value="technology">Technology</option>
                      <option value="education">Education</option>
                      <option value="entertainment">Entertainment</option>
                      <option value="news">News</option>
                      <option value="business">Business</option>
                    </select>
                  </div>

                  <div className="flex gap-2 pt-4">
                    <Button 
                      onClick={editingTopic ? handleUpdateTopic : handleCreateTopic}
                      disabled={createTopicMutation.isPending || updateTopicMutation.isPending}
                      className="flex-1"
                    >
                      {editingTopic ? "Update Topic" : "Create Topic"}
                    </Button>
                    <Button 
                      variant="outline" 
                      onClick={() => {
                        setShowCreateDialog(false);
                        setEditingTopic(null);
                        resetForm();
                      }}
                    >
                      Cancel
                    </Button>
                  </div>
                </div>
              </DialogContent>
            </Dialog>
          </div>
        </div>

        {/* Topic Content Tabs */}
        <Tabs defaultValue="summaries" className="w-full">
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="summaries">Summaries ({topicSummaries.length})</TabsTrigger>
            <TabsTrigger value="channels">Channels ({topicChannels.length})</TabsTrigger>
          </TabsList>

          <TabsContent value="summaries" className="space-y-4">
            {summariesLoading ? (
              <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-4">
                {Array.from({ length: 6 }).map((_, i) => (
                  <Card key={i} className="animate-pulse">
                    <CardContent className="p-4">
                      <div className="h-4 bg-gray-200 rounded mb-2"></div>
                      <div className="h-3 bg-gray-200 rounded w-2/3"></div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            ) : topicSummaries.length === 0 ? (
              <Card className="border-dashed">
                <CardContent className="p-12 text-center">
                  <Library className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
                  <h3 className="text-lg font-semibold mb-2">No summaries in this topic</h3>
                  <p className="text-muted-foreground mb-6">
                    Summaries will appear here when channels assigned to this topic generate content
                  </p>
                </CardContent>
              </Card>
            ) : (
              <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-4">
                {topicSummaries.map((summary) => (
                  <ContentCard
                    key={summary.id}
                    variant="summary"
                    title={summary.videoTitle}
                    description={summary.videoChannel}
                    thumbnail={summary.videoThumbnail}
                    metadata={{
                      readTime: summary.readTime || "5 min",
                      timeSaved: (summary as any).timeSaved || "10 min",
                      views: (summary as any).videoViews || "N/A"
                    }}
                    status={(summary as any).isMonitored ? "monitored" : undefined}
                    actions={{
                      primary: {
                        label: "View Summary",
                        onClick: () => window.open(`https://youtu.be/${(summary as any).videoId || summary.id}`, '_blank')
                      }
                    }}
                  />
                ))}
              </div>
            )}
          </TabsContent>

          <TabsContent value="channels" className="space-y-4">
            {channelsLoading ? (
              <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-4">
                {Array.from({ length: 3 }).map((_, i) => (
                  <Card key={i} className="animate-pulse">
                    <CardContent className="p-4">
                      <div className="h-4 bg-gray-200 rounded mb-2"></div>
                      <div className="h-3 bg-gray-200 rounded w-2/3"></div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            ) : topicChannels.length === 0 ? (
              <Card className="border-dashed">
                <CardContent className="p-12 text-center">
                  <MonitorSpeaker className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
                  <h3 className="text-lg font-semibold mb-2">No channels assigned</h3>
                  <p className="text-muted-foreground mb-6">
                    Add YouTube channels to this topic by URL or assign existing channels
                  </p>

                  {/* Channel URL Input */}
                  <div className="mb-6 max-w-md mx-auto">
                    <div className="flex gap-2">
                      <Input
                        placeholder="Paste YouTube channel URL..."
                        className="flex-1"
                        onKeyDown={(e) => {
                          if (e.key === 'Enter') {
                            const input = e.target as HTMLInputElement;
                            if (input.value.trim()) {
                              // Add channel import logic here
                              window.location.href = '/channels';
                            }
                          }
                        }}
                      />
                      <Button 
                        size="sm"
                        onClick={() => {
                          const input = document.querySelector('input[placeholder="Paste YouTube channel URL..."]') as HTMLInputElement;
                          if (input?.value.trim()) {
                            window.location.href = '/channels';
                          }
                        }}
                      >
                        <Plus className="h-4 w-4" />
                      </Button>
                    </div>
                    <p className="text-xs text-muted-foreground mt-2">
                      Enter a YouTube channel URL and press Enter
                    </p>
                  </div>

                  <div className="flex gap-2 justify-center">
                    <Button onClick={() => window.location.href = '/channels'}>
                      <Plus className="h-4 w-4 mr-2" />
                      Manage Channels
                    </Button>
                    <Button 
                      variant="outline" 
                      onClick={() => setShowChannelAssignDialog(true)}
                    >
                      <Tag className="h-4 w-4 mr-2" />
                      Assign Existing
                    </Button>
                  </div>
                </CardContent>
              </Card>
            ) : (
              <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-4">
                {topicChannels.map((channel) => (
                  <ContentCard
                    key={channel.id}
                    variant="channel"
                    title={channel.channelName}
                    description={`${(channel as any).subscriberCount || 'Unknown'} subscribers`}
                    thumbnail={channel.channelThumbnail}
                    actions={{
                      primary: {
                        label: "Visit Channel",
                        onClick: () => window.open(`https://www.youtube.com/channel/${(channel as any).channelId}`, '_blank')
                      }
                    }}
                  />
                ))}
              </div>
            )}
          </TabsContent>
        </Tabs>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Topics Header with Statistics */}
      <div className="grid lg:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">Total Topics</p>
                <p className="text-2xl font-bold">{topicStats.total}</p>
              </div>
              <Tag className="h-8 w-8 text-blue-500" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">With Channels</p>
                <p className="text-2xl font-bold">{topicStats.withChannels}</p>
              </div>
              <MonitorSpeaker className="h-8 w-8 text-green-500" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">Total Channels</p>
                <p className="text-2xl font-bold">{topicStats.totalChannels}</p>
              </div>
              <Users className="h-8 w-8 text-purple-500" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">Total Summaries</p>
                <p className="text-2xl font-bold">{topicStats.totalSummaries}</p>
              </div>
              <Library className="h-8 w-8 text-orange-500" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Action Bar */}
      <Card>
        <CardContent className="p-4">
          <div className="flex items-center justify-between">
            <div>
              <h2 className="text-xl font-semibold">Topic Organization</h2>
              <p className="text-muted-foreground">
                Organize channels and content into focused collections
              </p>
            </div>
            <Dialog open={showCreateDialog} onOpenChange={setShowCreateDialog}>
              <DialogTrigger asChild>
                <Button>
                  <Plus className="h-4 w-4 mr-2" />
                  Create Topic
                </Button>
              </DialogTrigger>
              <DialogContent className="max-w-2xl">
                <DialogHeader>
                  <DialogTitle>
                    {editingTopic ? "Edit Topic" : "Create New Topic"}
                  </DialogTitle>
                  <DialogDescription>
                    {editingTopic 
                      ? "Update the topic details below"
                      : "Create a new topic to organize your channels and content"
                    }
                  </DialogDescription>
                </DialogHeader>

                <div className="space-y-4">
                  <div>
                    <Label htmlFor="name">Topic Name *</Label>
                    <Input
                      id="name"
                      type="text"
                      value={formData.name}
                      onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
                      placeholder="e.g., AI News, Tech Reviews"
                      maxLength={100}
                      autoComplete="off"
                    />
                  </div>

                  <div>
                    <Label htmlFor="description">Description</Label>
                    <Textarea
                      id="description"
                      value={formData.description}
                      onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
                      placeholder="Brief description of this topic"
                      rows={3}
                    />
                  </div>

                  <div>
                    <Label htmlFor="color">Color</Label>
                    <div className="flex gap-2 mt-2">
                      {["#3b82f6", "#ef4444", "#22c55e", "#f59e0b", "#8b5cf6", "#ec4899"].map((color) => (
                        <button
                          key={color}
                          type="button"
                          className={`w-8 h-8 rounded-full border-2 ${
                            formData.colorHex === color 
                              ? "border-foreground" 
                              : "border-muted"
                          }`}
                          style={{ backgroundColor: color }}
                          onClick={() => setFormData(prev => ({ ...prev, colorHex: color }))}
                        />
                      ))}
                    </div>
                  </div>

                  <div>
                    <Label htmlFor="type">Type</Label>
                    <select 
                      value={formData.topicType} 
                      onChange={(e) => setFormData(prev => ({ ...prev, topicType: e.target.value }))}
                      className="w-full p-2 border rounded-md"
                    >
                      <option value="general">General</option>
                      <option value="technology">Technology</option>
                      <option value="education">Education</option>
                      <option value="entertainment">Entertainment</option>
                      <option value="news">News</option>
                      <option value="business">Business</option>
                    </select>
                  </div>

                  <div className="flex gap-2 pt-4">
                    <Button 
                      onClick={editingTopic ? handleUpdateTopic : handleCreateTopic}
                      disabled={createTopicMutation.isPending || updateTopicMutation.isPending}
                      className="flex-1"
                    >
                      {editingTopic ? "Update Topic" : "Create Topic"}
                    </Button>
                    <Button 
                      variant="outline" 
                      onClick={() => {
                        setShowCreateDialog(false);
                        setEditingTopic(null);
                        resetForm();
                      }}
                    >
                      Cancel
                    </Button>
                  </div>
                </div>
              </DialogContent>
            </Dialog>
          </div>
        </CardContent>
      </Card>

      {/* Topics Grid */}
      <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-4">
        {topics.map((topic) => (
          <ContentCard
            key={topic.id}
            variant="topic"
            title={topic.name}
            description={topic.description}
            colorAccent={topic.colorHex}
            actions={{
              primary: {
                label: "View Details",
                onClick: () => {
                  setSelectedTopic(topic);
                  setViewMode("details");
                }
              },
              secondary: [
                {
                  label: "Edit",
                  onClick: () => handleEditTopic(topic),
                  icon: Pencil
                },
                {
                  label: "RSS Feed",
                  onClick: () => handleCopyRSSUrl(topic.id),
                  icon: Rss
                },
                {
                  label: "Delete",
                  onClick: () => deleteTopicMutation.mutate(topic.id),
                  icon: Trash2
                }
              ]
            }}
          >
            <div className="space-y-2">
              <div className="flex gap-4 text-sm text-muted-foreground">
                <span>{topic.channelCount} channels</span>
                <span>{topic.summaryCount} summaries</span>
              </div>
              <div className="flex items-center gap-2">
                <Badge variant="outline" className="text-xs">
                  {topic.topicType}
                </Badge>
                {topic.summaryCount > 0 && (
                  <Badge variant="secondary" className="text-xs">
                    Active
                  </Badge>
                )}
              </div>
            </div>
          </ContentCard>
        ))}
      </div>

      {/* Empty State */}
      {topics.length === 0 && (
        <Card className="border-dashed">
          <CardContent className="p-12 text-center">
            <Tag className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
            <h3 className="text-lg font-semibold mb-2">No topics yet</h3>
            <p className="text-muted-foreground mb-6">
              Create your first topic to start organizing channels and content
            </p>
            <Dialog open={showCreateDialog} onOpenChange={setShowCreateDialog}>
              <DialogTrigger asChild>
                <Button>
                  <Plus className="h-4 w-4 mr-2" />
                  Create Your First Topic
                </Button>
              </DialogTrigger>
              <DialogContent className="max-w-2xl">
                <DialogHeader>
                  <DialogTitle>
                    {editingTopic ? "Edit Topic" : "Create New Topic"}
                  </DialogTitle>
                  <DialogDescription>
                    {editingTopic 
                      ? "Update the topic details below"
                      : "Create a new topic to organize your channels and content"
                    }
                  </DialogDescription>
                </DialogHeader>

                <div className="space-y-4">
                  <div>
                    <Label htmlFor="name">Topic Name *</Label>
                    <Input
                      id="name"
                      type="text"
                      value={formData.name}
                      onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
                      placeholder="e.g., AI News, Tech Reviews"
                      maxLength={100}
                      autoComplete="off"
                    />
                  </div>

                  <div>
                    <Label htmlFor="description">Description</Label>
                    <Textarea
                      id="description"
                      value={formData.description}
                      onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
                      placeholder="Brief description of this topic"
                      rows={3}
                    />
                  </div>

                  <div>
                    <Label htmlFor="color">Color</Label>
                    <div className="flex gap-2 mt-2">
                      {["#3b82f6", "#ef4444", "#22c55e", "#f59e0b", "#8b5cf6", "#ec4899"].map((color) => (
                        <button
                          key={color}
                          type="button"
                          className={`w-8 h-8 rounded-full border-2 ${
                            formData.colorHex === color 
                              ? "border-foreground" 
                              : "border-muted"
                          }`}
                          style={{ backgroundColor: color }}
                          onClick={() => setFormData(prev => ({ ...prev, colorHex: color }))}
                        />
                      ))}
                    </div>
                  </div>

                  <div>
                    <Label htmlFor="type">Type</Label>
                    <select 
                      value={formData.topicType} 
                      onChange={(e) => setFormData(prev => ({ ...prev, topicType: e.target.value }))}
                      className="w-full p-2 border rounded-md"
                    >
                      <option value="general">General</option>
                      <option value="technology">Technology</option>
                      <option value="education">Education</option>
                      <option value="entertainment">Entertainment</option>
                      <option value="news">News</option>
                      <option value="business">Business</option>
                    </select>
                  </div>

                  <div className="flex gap-2 pt-4">
                    <Button 
                      onClick={editingTopic ? handleUpdateTopic : handleCreateTopic}
                      disabled={createTopicMutation.isPending || updateTopicMutation.isPending}
                      className="flex-1"
                    >
                      {editingTopic ? "Update Topic" : "Create Topic"}
                    </Button>
                    <Button 
                      variant="outline" 
                      onClick={() => {
                        setShowCreateDialog(false);
                        setEditingTopic(null);
                        resetForm();
                      }}
                    >
                      Cancel
                    </Button>
                  </div>
                </div>
              </DialogContent>
            </Dialog>
          </CardContent>
        </Card>
      )}







      {/* RSS Feed Info */}
      <Card className="bg-blue-50 dark:bg-blue-950/20 border-blue-200 dark:border-blue-800">
        <CardContent className="p-4">
          <div className="flex items-center gap-3">
            <Rss className="h-5 w-5 text-blue-600" />
            <div className="flex-1">
              <h4 className="font-medium text-blue-900 dark:text-blue-100">RSS Feeds for Topics</h4>
              <p className="text-sm text-blue-700 dark:text-blue-300">
                Each topic has its own RSS feed containing summaries from all assigned channels
              </p>
            </div>
            <Badge variant="outline" className="text-blue-600">
              /rss/topic/[id]
            </Badge>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}