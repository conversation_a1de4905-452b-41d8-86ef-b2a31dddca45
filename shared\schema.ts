import {
  pgTable,
  text,
  varchar,
  timestamp,
  jsonb,
  index,
  serial,
  integer,
  boolean,
} from "drizzle-orm/pg-core";

import { createInsertSchema } from "drizzle-zod";
import { z } from "zod";

// Session storage table.
// (IMPORTANT) This table is mandatory for Replit Auth, don't drop it.
export const sessions = pgTable(
  "sessions",
  {
    sid: varchar("sid").primaryKey(),
    sess: jsonb("sess").notNull(),
    expire: timestamp("expire").notNull(),
  },
  (table) => [index("IDX_session_expire").on(table.expire)],
);

// User storage table.
// (IMPORTANT) This table is mandatory for Replit Auth, don't drop it.
export const users = pgTable("users", {
  id: varchar("id").primaryKey().notNull(),
  email: varchar("email").unique(),
  firstName: varchar("first_name"),
  lastName: varchar("last_name"),
  profileImageUrl: varchar("profile_image_url"),
  createdAt: timestamp("created_at").defaultNow(),
  updatedAt: timestamp("updated_at").defaultNow(),
  // SaaS specific fields
  usageCount: integer("usage_count").default(0),
  usageLimit: integer("usage_limit").default(5),
  subscriptionType: varchar("subscription_type").default("free"), // 'free', 'pro', 'enterprise', or 'super-premium'
  subscriptionExpiresAt: timestamp("subscription_expires_at"),
});

// Monitored channels table
export const channels = pgTable("channels", {
  id: serial("id").primaryKey(),
  userId: varchar("user_id").references(() => users.id).notNull(),
  channelId: varchar("channel_id").notNull(), // YouTube channel ID
  channelName: varchar("channel_name").notNull(),
  channelHandle: varchar("channel_handle"), // @username
  channelThumbnail: text("channel_thumbnail"),
  subscriberCount: varchar("subscriber_count"),
  isActive: boolean("is_active").default(true), // For pausing monitoring
  lastChecked: timestamp("last_checked"),
  videoCount: integer("video_count").default(0),
  createdAt: timestamp("created_at").defaultNow(),
  updatedAt: timestamp("updated_at").defaultNow(),
}, (table) => ({
  uniqueUserChannel: index("unique_user_channel").on(table.userId, table.channelId),
}));

export const summaries = pgTable("summaries", {
  id: serial("id").primaryKey(),
  userId: varchar("user_id").references(() => users.id).notNull(),
  channelId: integer("channel_id").references(() => channels.id), // Optional: null for manual videos
  videoId: varchar("video_id").notNull(), // YouTube video ID
  videoTitle: text("video_title").notNull(),
  videoChannel: varchar("video_channel").notNull(),
  videoChannelId: varchar("video_channel_id"), // YouTube channel ID
  videoThumbnail: text("video_thumbnail").notNull(),
  videoDuration: varchar("video_duration").notNull(),
  videoViews: varchar("video_views"),
  videoPublishedAt: timestamp("video_published_at"),
  videoDescription: text("video_description"),
  videoTranscript: text("video_transcript"), // Full transcript storage
  summaryContent: text("summary_content").notNull(),
  readTime: varchar("read_time").notNull(),
  timeSaved: varchar("time_saved").notNull(),
  createdAt: timestamp("created_at").defaultNow(),
  // Vector search and MCP integration
  summaryEmbedding: text("summary_embedding"), // JSON string of vector embeddings
  transcriptEmbedding: text("transcript_embedding"), // Separate embedding for transcript
  topics: text("topics"), // JSON array of extracted topics/tags
  keyEntities: text("key_entities"), // JSON array of people, companies, concepts
  mcpMetadata: text("mcp_metadata"), // JSON metadata for MCP server
  isMonitored: boolean("is_monitored").default(false), // Auto-generated vs manual
}, (table) => ({
  videoIdIndex: index("video_id_idx").on(table.videoId),
  channelIndex: index("channel_idx").on(table.channelId),
  publishedIndex: index("published_idx").on(table.videoPublishedAt),
  monitoredIndex: index("monitored_idx").on(table.isMonitored),
}));

// Topics table for knowledge graph and user-defined playlists
export const topics = pgTable("topics", {
  id: serial("id").primaryKey(),
  name: varchar("name").unique().notNull(),
  description: text("description"),
  parentTopicId: integer("parent_topic_id"),
  topicType: varchar("topic_type", { enum: ["auto_extracted", "user_defined", "playlist"] }).default("user_defined"),
  colorHex: varchar("color_hex", { length: 7 }),
  isActive: boolean("is_active").default(true),
  embedding: text("embedding"), // JSON string of vector embeddings
  summaryCount: integer("summary_count").default(0),
  createdAt: timestamp("created_at").defaultNow(),
}, (table) => ({
  nameIndex: index("topic_name_idx").on(table.name),
  parentIndex: index("topic_parent_idx").on(table.parentTopicId),
  typeIndex: index("topic_type_idx").on(table.topicType),
}));

// Simplified - removed relations to fix import issue

// Entities table for people, companies, concepts
export const entities = pgTable("entities", {
  id: serial("id").primaryKey(),
  name: varchar("name").notNull(),
  type: varchar("type").notNull(), // 'person', 'company', 'concept', 'product'
  description: text("description"),
  embedding: text("embedding"), // JSON string of vector embeddings
  createdAt: timestamp("created_at").defaultNow(),
});

// Many-to-many relationship between channels and topics (NEW)
export const channelTopics = pgTable("channel_topics", {
  id: serial("id").primaryKey(),
  channelId: integer("channel_id").references(() => channels.id).notNull(),
  topicId: integer("topic_id").references(() => topics.id).notNull(),
  relevanceScore: varchar("relevance_score").default("1.0"), // 0-1 confidence
  assignmentType: varchar("assignment_type", { enum: ["manual", "auto", "inherited"] }).default("manual"),
  createdAt: timestamp("created_at").defaultNow(),
}, (table) => ({
  uniqueChannelTopic: index("unique_channel_topic").on(table.channelId, table.topicId),
  channelIndex: index("channel_topic_channel_idx").on(table.channelId),
  topicIndex: index("channel_topic_topic_idx").on(table.topicId),
}));

// Many-to-many relationship between summaries and topics (ENHANCED)
export const summaryTopics = pgTable("summary_topics", {
  id: serial("id").primaryKey(),
  summaryId: integer("summary_id").references(() => summaries.id).notNull(),
  topicId: integer("topic_id").references(() => topics.id).notNull(),
  relevanceScore: varchar("relevance_score"), // 0-1 confidence as string
  detectionMethod: varchar("detection_method", { enum: ["llm_extracted", "user_tagged", "channel_inherited"] }).default("llm_extracted"),
  createdAt: timestamp("created_at").defaultNow(),
}, (table) => ({
  summaryIndex: index("summary_topic_summary_idx").on(table.summaryId),
  topicIndex: index("summary_topic_topic_idx").on(table.topicId),
}));

// MCP server usage tracking
export const mcpUsage = pgTable("mcp_usage", {
  id: serial("id").primaryKey(),
  userId: varchar("user_id").references(() => users.id).notNull(),
  queryType: varchar("query_type").notNull(),
  timestamp: timestamp("timestamp").defaultNow(),
  tokensUsed: integer("tokens_used").default(0),
  vectorSearches: integer("vector_searches").default(0),
});

// Channel monitoring jobs for background processing
export const channelMonitoringJobs = pgTable("channel_monitoring_jobs", {
  id: serial("id").primaryKey(),
  channelId: integer("channel_id").references(() => channels.id).notNull(),
  status: varchar("status").default("pending"), // 'pending', 'running', 'completed', 'failed'
  lastVideoId: varchar("last_video_id"), // Track last processed video
  videosProcessed: integer("videos_processed").default(0),
  errorMessage: text("error_message"),
  startedAt: timestamp("started_at"),
  completedAt: timestamp("completed_at"),
  createdAt: timestamp("created_at").defaultNow(),
}, (table) => ({
  statusIndex: index("job_status_idx").on(table.status),
  channelJobIndex: index("channel_job_idx").on(table.channelId),
}));

export const summaryFeedback = pgTable("summary_feedback", {
  id: serial("id").primaryKey(),
  summaryId: integer("summary_id").notNull().references(() => summaries.id),
  userId: varchar("user_id").notNull().references(() => users.id),
  rating: varchar("rating").notNull(), // 'helpful' or 'not_helpful'
  createdAt: timestamp("created_at").defaultNow(),
}, (table) => ({
  uniqueUserSummary: index("unique_user_summary").on(table.summaryId, table.userId),
}));



export const insertSummarySchema = createInsertSchema(summaries).omit({
  id: true,
  createdAt: true,
});

export type UpsertUser = typeof users.$inferInsert;
export type User = typeof users.$inferSelect;
export type InsertSummary = z.infer<typeof insertSummarySchema>;
export type Summary = typeof summaries.$inferSelect;
export type Topic = typeof topics.$inferSelect;
export type InsertTopic = typeof topics.$inferInsert;
export type Entity = typeof entities.$inferSelect;
export type SummaryTopic = typeof summaryTopics.$inferSelect;
export type InsertSummaryTopic = typeof summaryTopics.$inferInsert;
export type ChannelTopic = typeof channelTopics.$inferSelect;
export type InsertChannelTopic = typeof channelTopics.$inferInsert;
export type McpUsage = typeof mcpUsage.$inferSelect;
export type SummaryFeedback = typeof summaryFeedback.$inferSelect;
export type Channel = typeof channels.$inferSelect;
export type InsertChannel = typeof channels.$inferInsert;
export type ChannelMonitoringJob = typeof channelMonitoringJobs.$inferSelect;
export type InsertChannelMonitoringJob = typeof channelMonitoringJobs.$inferInsert;
