import { Link, useLocation } from "wouter";
import { 
  Video, 
  Plus, 
  Library, 
  MonitorSpeaker, 
  Tag,
  BarChart3,
  Search,
  Bell,
  Settings
} from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { cn } from "@/lib/utils";
import { useState } from "react";

interface NavigationItem {
  href: string;
  label: string;
  icon: React.ComponentType<{ className?: string }>;
  description: string;
  badge?: number;
  shortcut?: string;
}

interface EnhancedNavigationProps {
  className?: string;
}

export function EnhancedNavigation({ className }: EnhancedNavigationProps) {
  const [location] = useLocation();
  const [searchOpen, setSearchOpen] = useState(false);

  const navigationItems: NavigationItem[] = [
    {
      href: "/",
      label: "Create",
      icon: Plus,
      description: "Summarize new videos",
      shortcut: "C"
    },
    {
      href: "/library", 
      label: "Library",
      icon: Library,
      description: "Browse all summaries",
      shortcut: "L"
    },
    {
      href: "/channels",
      label: "Channels",
      icon: MonitorSpeaker,
      description: "Monitor channels",
      badge: 3, // New videos from monitoring
      shortcut: "M"
    },
    {
      href: "/topics",
      label: "Topics",
      icon: Tag,
      description: "Organize content",
      shortcut: "T"
    }
  ];

  const isActiveRoute = (href: string) => {
    if (href === "/") return location === "/";
    return location.startsWith(href);
  };

  return (
    <nav className={cn("flex items-center space-x-1", className)}>
      {/* Search Trigger */}
      <Button
        variant="ghost"
        size="sm"
        onClick={() => setSearchOpen(!searchOpen)}
        className="hidden lg:flex items-center gap-2 text-muted-foreground hover:text-foreground"
      >
        <Search className="h-4 w-4" />
        <span className="text-sm">Search</span>
        <kbd className="pointer-events-none inline-flex h-5 select-none items-center gap-1 rounded border bg-muted px-1.5 font-mono text-[10px] font-medium text-muted-foreground">
          ⌘K
        </kbd>
      </Button>

      {/* Primary Navigation */}
      {navigationItems.map((item) => {
        const isActive = isActiveRoute(item.href);
        return (
          <Link key={item.href} href={item.href}>
            <Button
              variant="ghost"
              size="sm"
              className={cn(
                "relative flex items-center gap-2 px-3 py-2 text-sm font-medium transition-all",
                "hover:bg-accent hover:text-accent-foreground",
                "focus-visible:ring-2 focus-visible:ring-primary",
                isActive && "bg-primary/10 text-primary hover:bg-primary/15"
              )}
            >
              <item.icon className="h-4 w-4" />
              <span className="hidden md:inline">{item.label}</span>
              
              {/* Badge for notifications */}
              {item.badge && item.badge > 0 && (
                <Badge 
                  variant="secondary" 
                  className="ml-1 h-5 w-5 p-0 text-xs flex items-center justify-center"
                >
                  {item.badge}
                </Badge>
              )}
              
              {/* Keyboard shortcut hint */}
              {item.shortcut && isActive && (
                <kbd className="hidden lg:inline-flex h-4 select-none items-center gap-1 rounded border bg-muted px-1 font-mono text-[10px] font-medium text-muted-foreground">
                  {item.shortcut}
                </kbd>
              )}
            </Button>
          </Link>
        );
      })}

      {/* Secondary Actions */}
      <div className="flex items-center space-x-1 ml-4 pl-4 border-l border-border">
        <Button variant="ghost" size="sm" className="relative">
          <Bell className="h-4 w-4" />
          <span className="sr-only">Notifications</span>
          {/* Notification indicator */}
          <div className="absolute -top-1 -right-1 h-2 w-2 bg-red-500 rounded-full"></div>
        </Button>
        
        <Button variant="ghost" size="sm">
          <Settings className="h-4 w-4" />
          <span className="sr-only">Settings</span>
        </Button>
      </div>
    </nav>
  );
}

export default EnhancedNavigation;