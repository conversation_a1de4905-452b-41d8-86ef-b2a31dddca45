import { useState, useEffect } from "react";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { Link, useLocation } from "wouter";
import { apiRequest } from "@/lib/queryClient";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { useToast } from "@/hooks/use-toast";
import { Switch } from "@/components/ui/switch";
import { Plus, Search, Trash2 } from "lucide-react";
import { isUnauthorizedError } from "@/lib/authUtils";

interface Channel {
  id: number;
  channelId: string;
  channelName: string;
  channelHandle: string;
  channelThumbnail: string;
  subscriberCount: string;
  isActive: boolean;
  lastChecked: string;
  videoCount: number;
  createdAt: string;
}

interface Summary {
  id: number;
  channelId: number;
  videoId: string;
  videoTitle: string;
  videoChannel: string;
  videoThumbnail: string;
  videoDuration: string;
  videoViews: string;
  videoPublishedAt: string;
  summaryContent: string;
  videoTranscript: string;
  readTime: string;
  timeSaved: string;
  isMonitored: boolean;
  createdAt: string;
}

export default function Channels() {
  const [isAddingChannel, setIsAddingChannel] = useState(false);
  const [channelUrl, setChannelUrl] = useState("");
  const [searchQuery, setSearchQuery] = useState("");
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const [, setLocation] = useLocation();

  // Fetch user's channels
  const { data: channels = [], isLoading: channelsLoading } = useQuery<Channel[]>({
    queryKey: ["/api/channels"]
  });

  // Fetch monitored summaries
  const { data: monitoredSummaries = [] } = useQuery<Summary[]>({
    queryKey: ["/api/monitored-summaries", { limit: 50 }]
  });

  // Add channel mutation
  const addChannelMutation = useMutation({
    mutationFn: async ({ channelUrl }: { channelUrl: string }) => {
      return await apiRequest("POST", "/api/channels", { channelUrl });
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["/api/channels"] });
      setIsAddingChannel(false);
      setChannelUrl("");
      toast({
        title: "Channel Added",
        description: "Channel monitoring started successfully",
      });
    },
    onError: (error) => {
      if (isUnauthorizedError(error)) {
        toast({
          title: "Unauthorized",
          description: "You are logged out. Logging in again...",
          variant: "destructive",
        });
        setTimeout(() => {
          window.location.href = "/api/login";
        }, 500);
        return;
      }
      toast({
        title: "Error",
        description: error.message || "Failed to add channel",
        variant: "destructive",
      });
    },
  });

  // Toggle channel status
  const toggleChannelMutation = useMutation({
    mutationFn: async ({ channelId, isActive }: { channelId: number; isActive: boolean }) => {
      return await apiRequest("PATCH", `/api/channels/${channelId}`, { isActive });
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["/api/channels"] });
    },
    onError: (error) => {
      if (isUnauthorizedError(error)) {
        toast({
          title: "Unauthorized",
          description: "You are logged out. Logging in again...",
          variant: "destructive",
        });
        setTimeout(() => {
          window.location.href = "/api/login";
        }, 500);
        return;
      }
      toast({
        title: "Error",
        description: "Failed to update channel",
        variant: "destructive",
      });
    },
  });

  // Delete channel mutation
  const deleteChannelMutation = useMutation({
    mutationFn: async (channelId: number) => {
      return await apiRequest("DELETE", `/api/channels/${channelId}`);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["/api/channels"] });
      toast({
        title: "Channel Deleted",
        description: "Channel removed from monitoring",
      });
    },
    onError: (error) => {
      if (isUnauthorizedError(error)) {
        toast({
          title: "Unauthorized",
          description: "You are logged out. Logging in again...",
          variant: "destructive",
        });
        setTimeout(() => {
          window.location.href = "/api/login";
        }, 500);
        return;
      }
      toast({
        title: "Error",
        description: "Failed to delete channel",
        variant: "destructive",
      });
    },
  });

  const handleAddChannel = () => {
    if (!channelUrl.trim()) return;
    addChannelMutation.mutate({ channelUrl: channelUrl.trim() });
  };

  const handleVideoClick = (summary: Summary) => {
    setLocation(`/?summaryId=${summary.id}`);
  };

  return (
    <div className="container mx-auto p-6 max-w-7xl">
      
      {/* Page Header with Primary Action */}
      <div className="flex flex-col space-y-4 mb-8">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold">Channel Monitoring</h1>
            <p className="text-muted-foreground">Monitor YouTube channels for automatic summaries</p>
          </div>
          
          <Dialog open={isAddingChannel} onOpenChange={setIsAddingChannel}>
            <DialogTrigger asChild>
              <Button>
                <Plus className="w-4 h-4 mr-2" />
                Add Channel
              </Button>
            </DialogTrigger>
            <DialogContent>
              <DialogHeader>
                <DialogTitle>Add YouTube Channel</DialogTitle>
              </DialogHeader>
              <div className="space-y-4">
                <Input
                  placeholder="Enter YouTube channel URL or @handle"
                  value={channelUrl}
                  onChange={(e) => setChannelUrl(e.target.value)}
                  onKeyDown={(e) => e.key === "Enter" && handleAddChannel()}
                />
                <div className="flex justify-end gap-2">
                  <Button variant="outline" onClick={() => setIsAddingChannel(false)}>
                    Cancel
                  </Button>
                  <Button 
                    onClick={handleAddChannel}
                    disabled={!channelUrl.trim() || addChannelMutation.isPending}
                  >
                    {addChannelMutation.isPending ? "Adding..." : "Add Channel"}
                  </Button>
                </div>
              </div>
            </DialogContent>
          </Dialog>
        </div>

        {/* Search Control */}
        <div className="flex items-center space-x-4">
          <div className="flex-1 relative max-w-md">
            <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Search channels..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-10"
            />
          </div>
        </div>
      </div>

      {/* Monitoring Overview Stats */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Monitored Channels</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{channels.length}</div>
            <p className="text-xs text-muted-foreground">
              {channels.filter((c: Channel) => c.isActive).length} active
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Videos Analyzed</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{monitoredSummaries.length}</div>
            <p className="text-xs text-muted-foreground">From monitored channels</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Time Saved</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {Math.round(monitoredSummaries.length * 2.5)}h
            </div>
            <p className="text-xs text-muted-foreground">Estimated</p>
          </CardContent>
        </Card>
      </div>

      {/* Channels Management Section */}
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <h2 className="text-xl font-semibold">Your Channels</h2>
          <div className="text-sm text-muted-foreground">
            {channels.filter((c: Channel) => c.isActive).length} of {channels.length} active
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {channelsLoading ? (
            Array.from({ length: 6 }).map((_, i) => (
              <Card key={i} className="animate-pulse">
                <CardContent className="p-6">
                  <div className="flex items-center space-x-3">
                    <div className="w-12 h-12 bg-gray-200 rounded-full"></div>
                    <div className="flex-1">
                      <div className="h-4 bg-gray-200 rounded mb-2"></div>
                      <div className="h-3 bg-gray-200 rounded w-2/3"></div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))
          ) : channels.length === 0 ? (
            <Card className="col-span-full p-12 text-center">
              <div className="text-muted-foreground mb-4">
                No channels monitored yet. Add your first channel to start building your knowledge base.
              </div>
              <Button onClick={() => setIsAddingChannel(true)}>
                <Plus className="w-4 h-4 mr-2" />
                Add Your First Channel
              </Button>
            </Card>
          ) : (
            channels
              .filter((channel: Channel) => 
                !searchQuery || 
                channel.channelName.toLowerCase().includes(searchQuery.toLowerCase())
              )
              .map((channel: Channel) => (
                <Card key={channel.id} className="group hover:shadow-md transition-shadow">
                  <CardContent className="p-6">
                    <div className="flex items-start justify-between">
                      <div className="flex items-center space-x-3 flex-1">
                        <Avatar>
                          <AvatarImage src={channel.channelThumbnail} />
                          <AvatarFallback>{channel.channelName[0]}</AvatarFallback>
                        </Avatar>
                        <div className="flex-1 min-w-0">
                          <h3 className="font-medium truncate">{channel.channelName}</h3>
                          <p className="text-sm text-muted-foreground">{channel.subscriberCount} subscribers</p>
                          <div className="flex items-center gap-2 mt-2">
                            <Badge variant={channel.isActive ? "default" : "secondary"}>
                              {channel.isActive ? "Active" : "Paused"}
                            </Badge>
                            <Badge variant="outline">{channel.videoCount} videos</Badge>
                          </div>
                        </div>
                      </div>
                      
                      <div className="flex items-center gap-2 opacity-0 group-hover:opacity-100 transition-opacity">
                        <Switch
                          checked={channel.isActive}
                          onCheckedChange={(checked) => 
                            toggleChannelMutation.mutate({ channelId: channel.id, isActive: checked })
                          }
                        />
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => deleteChannelMutation.mutate(channel.id)}
                          className="text-red-500 hover:text-red-700"
                        >
                          <Trash2 className="w-4 h-4" />
                        </Button>
                      </div>
                    </div>
                    
                    {channel.lastChecked && (
                      <p className="text-xs text-muted-foreground mt-3">
                        Last checked: {new Date(channel.lastChecked).toLocaleDateString()}
                      </p>
                    )}
                  </CardContent>
                </Card>
              ))
          )}
        </div>
      </div>

      {/* Recent Activity Section */}
      {monitoredSummaries.length > 0 && (
        <div className="mt-12 space-y-6">
          <div className="flex items-center justify-between">
            <h2 className="text-xl font-semibold">Recent Activity</h2>
            <Link href="/library">
              <Button variant="outline" size="sm">
                View All Summaries
              </Button>
            </Link>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {monitoredSummaries.slice(0, 6).map((summary: Summary) => (
              <Card 
                key={summary.id} 
                className="hover:shadow-md transition-shadow cursor-pointer"
                onClick={() => handleVideoClick(summary)}
              >
                <CardContent className="p-4">
                  <div className="flex items-start space-x-3">
                    <img 
                      src={summary.videoThumbnail} 
                      alt={summary.videoTitle}
                      className="w-16 h-12 object-cover rounded flex-shrink-0"
                    />
                    <div className="flex-1 min-w-0">
                      <p className="text-sm font-medium line-clamp-2 mb-1">{summary.videoTitle}</p>
                      <p className="text-xs text-muted-foreground mb-2">{summary.videoChannel}</p>
                      <div className="flex items-center gap-2">
                        <Badge variant="secondary" className="text-xs">{summary.readTime}</Badge>
                        <span className="text-xs text-muted-foreground">
                          {new Date(summary.createdAt).toLocaleDateString()}
                        </span>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      )}
    </div>
  );
}