#!/bin/bash

# VideoSummarize AI - GitHub Pull Script
# Pulls the latest changes from GitHub

echo "🔄 Pulling latest changes from GitHub..."

# Check if git is initialized
if [ ! -d ".git" ]; then
    echo "❌ Not a git repository. Please run this from the root of your git repository."
    exit 1
fi

# Check for local changes
if ! git diff-index --quiet HEAD --; then
    echo "⚠️ You have local changes that might conflict with the pull."
    echo "Options:"
    echo "  1. <PERSON>ash changes and pull"
    echo "  2. Pull and merge with local changes"
    echo "  3. Cancel"
    read -p "Choose an option (1-3): " choice
    
    case $choice in
        1)
            echo "📦 Stashing local changes..."
            git stash
            ;;
        2)
            echo "🔄 Will attempt to merge changes..."
            ;;
        3)
            echo "❌ Operation cancelled."
            exit 0
            ;;
        *)
            echo "❌ Invalid option. Operation cancelled."
            exit 1
            ;;
    esac
fi

# Get current branch
CURRENT_BRANCH=$(git branch --show-current)

# Pull the latest changes
echo "🔄 Pulling latest changes from origin/$CURRENT_BRANCH..."
if git pull origin $CURRENT_BRANCH; then
    echo "✅ Successfully pulled latest changes!"
    
    # If we stashed changes, pop them back
    if [ "$choice" = "1" ]; then
        echo "📦 Applying stashed changes..."
        git stash pop
        
        # Check for conflicts
        if git diff --name-only --diff-filter=U | grep -q .; then
            echo "⚠️ There are merge conflicts. Please resolve them manually."
        else
            echo "✅ Stashed changes applied successfully!"
        fi
    fi
    
    # Show what changed
    echo "📋 Summary of changes:"
    git log -1 --stat
else
    echo "❌ Pull failed. Please check the error message above."
    
    # If we stashed changes, pop them back
    if [ "$choice" = "1" ]; then
        echo "📦 Applying stashed changes back..."
        git stash pop
    fi
    
    exit 1
fi

echo "✨ Pull operation completed!"