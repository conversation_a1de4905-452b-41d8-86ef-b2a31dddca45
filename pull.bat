@echo off
REM Quick launcher for the PowerShell pull script
REM Usage: pull.bat [options]
REM   pull.bat          - Interactive mode
REM   pull.bat stash    - Auto-stash changes
REM   pull.bat force    - Force pull with merge
REM   pull.bat help     - Show help

if "%1"=="help" (
    powershell -ExecutionPolicy Bypass -File pull-from-github.ps1 -Help
) else if "%1"=="stash" (
    powershell -ExecutionPolicy Bypass -File pull-from-github.ps1 -Stash
) else if "%1"=="force" (
    powershell -ExecutionPolicy Bypass -File pull-from-github.ps1 -Force
) else if "%1"=="" (
    powershell -ExecutionPolicy Bypass -File pull-from-github.ps1
) else (
    echo Unknown option: %1
    echo Use: pull.bat [help^|stash^|force]
    exit /b 1
)
