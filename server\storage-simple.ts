import { 
  users, 
  summaries, 
  summaryFeedback, 
  channels,
  channelMonitoringJobs,
  topics,
  channelTopics,
  summaryTopics,
  type User, 
  type UpsertUser, 
  type Summary, 
  type InsertSummary, 
  type SummaryFeedback,
  type Channel,
  type InsertChannel,
  type ChannelMonitoringJob,
  type InsertChannelMonitoringJob,
  type Topic,
  type InsertTopic,
  type ChannelTopic,
  type InsertChannelTopic,
  type SummaryTopic,
  type InsertSummaryTopic
} from "@shared/schema";
import { db } from "./db";
import { eq, and, desc, or, ilike, sql, lt, gte, SQL } from "drizzle-orm";

// Storage interface for channel monitoring and vector database operations
export interface IStorage {
  // User operations
  getUser(id: string): Promise<User | undefined>;
  upsertUser(user: UpsertUser): Promise<User>;
  incrementUserUsage(userId: string): Promise<void>;
  
  // Admin operations
  getAllUsers(): Promise<User[]>;
  getUserMetrics(): Promise<{
    totalUsers: number;
    activeUsers: number;
    subscriptionDistribution: { [key: string]: number };
    totalUsage: number;
    totalTokenCosts: number;
  }>;
  updateUserSubscription(userId: string, subscriptionType: string, usageLimit: number): Promise<User>;
  suspendUser(userId: string): Promise<void>;
  activateUser(userId: string): Promise<void>;
  resetUserUsage(userId: string): Promise<void>;
  updateUserTokenCosts(userId: string, tokenCosts: number): Promise<void>;
  
  // Channel operations
  getUserChannels(userId: string): Promise<Channel[]>;
  getChannel(id: number, userId: string): Promise<Channel | undefined>;
  getChannelByChannelId(userId: string, channelId: string): Promise<Channel | undefined>;
  createChannel(channel: InsertChannel): Promise<Channel>;
  updateChannel(id: number, userId: string, updates: Partial<InsertChannel>): Promise<Channel>;
  deleteChannel(id: number, userId: string): Promise<void>;
  getActiveChannels(): Promise<Channel[]>;
  
  // Channel monitoring jobs
  createMonitoringJob(job: InsertChannelMonitoringJob): Promise<ChannelMonitoringJob>;
  getChannelJobs(channelId: number): Promise<ChannelMonitoringJob[]>;
  updateMonitoringJob(id: number, updates: Partial<InsertChannelMonitoringJob>): Promise<void>;
  getPendingJobs(): Promise<ChannelMonitoringJob[]>;
  
  // Summary operations (enhanced for monitoring)
  getUserSummaries(userId: string): Promise<Summary[]>;
  getChannelSummaries(channelId: number, userId: string): Promise<Summary[]>;
  getSummaryByVideoId(userId: string, videoId: string): Promise<Summary | undefined>;
  createSummary(summary: InsertSummary): Promise<Summary>;
  getSummary(id: number, userId: string): Promise<Summary | undefined>;
  searchSummariesByText(userId: string, query: string): Promise<Summary[]>;
  getRecentMonitoredSummaries(userId: string, limit?: number): Promise<Summary[]>;
  
  // Vector database operations
  searchSummariesByVector(userId: string, query: string, filters?: {
    channelIds?: number[];
    topics?: string[];
    dateRange?: [Date, Date];
    limit?: number;
  }): Promise<Summary[]>;
  getSimilarSummaries(summaryId: number, userId: string, limit?: number): Promise<Summary[]>;
  
  // Feedback operations
  createFeedback(summaryId: number, userId: string, rating: 'helpful' | 'not_helpful'): Promise<void>;
  getFeedbackStats(summaryId?: number): Promise<{ helpful: number; notHelpful: number; total: number; }>;
  
  // Topic management operations
  getTopics(userId?: string): Promise<Topic[]>;
  getTopic(id: number): Promise<Topic | undefined>;
  createTopic(topic: InsertTopic): Promise<Topic>;
  updateTopic(id: number, updates: Partial<InsertTopic>): Promise<Topic>;
  deleteTopic(id: number): Promise<void>;
  getTopicHierarchy(): Promise<Topic[]>;
  
  // Channel-Topic operations
  getChannelTopics(channelId: number): Promise<ChannelTopic[]>;
  assignChannelToTopic(channelTopic: InsertChannelTopic): Promise<ChannelTopic>;
  removeChannelFromTopic(channelId: number, topicId: number): Promise<void>;
  getChannelsByTopic(topicId: number, userId: string): Promise<Channel[]>;
  getTopicsByChannel(channelId: number): Promise<Topic[]>;
  
  // Summary-Topic operations
  getSummaryTopics(summaryId: number): Promise<SummaryTopic[]>;
  getSummariesByTopic(topicId: number, userId: string): Promise<Summary[]>;
  autoTagSummaryWithTopics(summaryId: number, topics: string[]): Promise<void>;
}

export class DatabaseStorage implements IStorage {
  // User operations
  async getUser(id: string): Promise<User | undefined> {
    const [user] = await db.select().from(users).where(eq(users.id, id));
    return user;
  }

  async upsertUser(userData: UpsertUser): Promise<User> {
    const [user] = await db
      .insert(users)
      .values(userData)
      .onConflictDoUpdate({
        target: users.id,
        set: {
          ...userData,
          updatedAt: new Date(),
        },
      })
      .returning();
    return user;
  }

  async incrementUserUsage(userId: string): Promise<void> {
    // Get current user to increment usage count
    const user = await this.getUser(userId);
    if (user) {
      await db
        .update(users)
        .set({
          usageCount: (user.usageCount || 0) + 1,
        })
        .where(eq(users.id, userId));
    }
  }

  // Admin operations
  async getAllUsers(): Promise<User[]> {
    return await db
      .select()
      .from(users)
      .orderBy(desc(users.createdAt));
  }

  async getUserMetrics(): Promise<{
    totalUsers: number;
    activeUsers: number;
    subscriptionDistribution: { [key: string]: number };
    totalUsage: number;
    totalTokenCosts: number;
  }> {
    const allUsers = await this.getAllUsers();
    
    const metrics = {
      totalUsers: allUsers.length,
      activeUsers: allUsers.filter(u => u.isActive).length,
      subscriptionDistribution: {} as { [key: string]: number },
      totalUsage: allUsers.reduce((sum, u) => sum + (u.usageCount || 0), 0),
      totalTokenCosts: allUsers.reduce((sum, u) => sum + (u.tokenCosts || 0), 0),
    };

    // Calculate subscription distribution
    allUsers.forEach(user => {
      const subType = user.subscriptionType || 'free';
      metrics.subscriptionDistribution[subType] = (metrics.subscriptionDistribution[subType] || 0) + 1;
    });

    return metrics;
  }

  async updateUserSubscription(userId: string, subscriptionType: string, usageLimit: number): Promise<User> {
    const [user] = await db
      .update(users)
      .set({
        subscriptionType,
        usageLimit,
        updatedAt: new Date(),
      })
      .where(eq(users.id, userId))
      .returning();
    return user;
  }

  async suspendUser(userId: string): Promise<void> {
    await db
      .update(users)
      .set({
        isActive: false,
        updatedAt: new Date(),
      })
      .where(eq(users.id, userId));
  }

  async activateUser(userId: string): Promise<void> {
    await db
      .update(users)
      .set({
        isActive: true,
        updatedAt: new Date(),
      })
      .where(eq(users.id, userId));
  }

  async resetUserUsage(userId: string): Promise<void> {
    await db
      .update(users)
      .set({
        usageCount: 0,
        updatedAt: new Date(),
      })
      .where(eq(users.id, userId));
  }

  async updateUserTokenCosts(userId: string, additionalTokenCosts: number): Promise<void> {
    const user = await this.getUser(userId);
    const currentTokenCosts = user?.tokenCosts || 0;
    
    await db
      .update(users)
      .set({
        tokenCosts: currentTokenCosts + additionalTokenCosts,
        updatedAt: new Date(),
      })
      .where(eq(users.id, userId));
  }

  // Summary operations
  async getUserSummaries(userId: string): Promise<Summary[]> {
    return await db
      .select()
      .from(summaries)
      .where(eq(summaries.userId, userId))
      .orderBy(desc(summaries.createdAt));
  }

  async getSummaryByVideoId(userId: string, videoId: string): Promise<Summary | undefined> {
    const [summary] = await db
      .select()
      .from(summaries)
      .where(and(eq(summaries.userId, userId), eq(summaries.videoId, videoId)));
    return summary;
  }

  async createSummary(summary: InsertSummary): Promise<Summary> {
    const [newSummary] = await db
      .insert(summaries)
      .values(summary)
      .returning();
    return newSummary;
  }

  async getSummary(id: number, userId: string): Promise<Summary | undefined> {
    const [summary] = await db
      .select()
      .from(summaries)
      .where(and(eq(summaries.id, id), eq(summaries.userId, userId)));
    return summary;
  }

  async searchSummariesByText(userId: string, query: string): Promise<Summary[]> {
    return await db
      .select()
      .from(summaries)
      .where(
        and(
          eq(summaries.userId, userId),
          or(
            ilike(summaries.videoTitle, `%${query}%`),
            ilike(summaries.summaryContent, `%${query}%`),
            ilike(summaries.videoChannel, `%${query}%`)
          )
        )
      )
      .orderBy(desc(summaries.createdAt))
      .limit(50);
  }

  async createFeedback(summaryId: number, userId: string, rating: 'helpful' | 'not_helpful'): Promise<void> {
    // Check if feedback already exists
    const existingFeedback = await db
      .select()
      .from(summaryFeedback)
      .where(and(
        eq(summaryFeedback.summaryId, summaryId),
        eq(summaryFeedback.userId, userId)
      ))
      .limit(1);

    if (existingFeedback.length > 0) {
      // Update existing feedback
      await db
        .update(summaryFeedback)
        .set({ rating, createdAt: new Date() })
        .where(and(
          eq(summaryFeedback.summaryId, summaryId),
          eq(summaryFeedback.userId, userId)
        ));
    } else {
      // Insert new feedback
      await db
        .insert(summaryFeedback)
        .values({
          summaryId,
          userId,
          rating,
        });
    }
  }

  async getFeedbackStats(summaryId?: number): Promise<{ helpful: number; notHelpful: number; total: number; }> {
    const baseQuery = db.select().from(summaryFeedback);
    
    const results = summaryId 
      ? await baseQuery.where(eq(summaryFeedback.summaryId, summaryId))
      : await baseQuery;
    
    const helpful = results.filter(f => f.rating === 'helpful').length;
    const notHelpful = results.filter(f => f.rating === 'not_helpful').length;
    
    return {
      helpful,
      notHelpful,
      total: helpful + notHelpful,
    };
  }

  // Channel operations
  async getUserChannels(userId: string): Promise<Channel[]> {
    return await db
      .select()
      .from(channels)
      .where(eq(channels.userId, userId))
      .orderBy(desc(channels.createdAt));
  }

  async getChannel(id: number, userId: string): Promise<Channel | undefined> {
    const [channel] = await db
      .select()
      .from(channels)
      .where(and(eq(channels.id, id), eq(channels.userId, userId)));
    return channel;
  }

  async getChannelByChannelId(userId: string, channelId: string): Promise<Channel | undefined> {
    const [channel] = await db
      .select()
      .from(channels)
      .where(and(eq(channels.channelId, channelId), eq(channels.userId, userId)));
    return channel;
  }

  async createChannel(channel: InsertChannel): Promise<Channel> {
    const [newChannel] = await db
      .insert(channels)
      .values(channel)
      .returning();
    return newChannel;
  }

  async updateChannel(id: number, userId: string, updates: Partial<InsertChannel>): Promise<Channel> {
    const [updatedChannel] = await db
      .update(channels)
      .set({ ...updates, updatedAt: new Date() })
      .where(and(eq(channels.id, id), eq(channels.userId, userId)))
      .returning();
    return updatedChannel;
  }

  async deleteChannel(id: number, userId: string): Promise<void> {
    await db
      .delete(channels)
      .where(and(eq(channels.id, id), eq(channels.userId, userId)));
  }

  async getActiveChannels(): Promise<Channel[]> {
    return await db
      .select()
      .from(channels)
      .where(eq(channels.isActive, true))
      .orderBy(desc(channels.lastChecked));
  }

  // Channel monitoring jobs
  async createMonitoringJob(job: InsertChannelMonitoringJob): Promise<ChannelMonitoringJob> {
    const [newJob] = await db
      .insert(channelMonitoringJobs)
      .values(job)
      .returning();
    return newJob;
  }

  async getChannelJobs(channelId: number): Promise<ChannelMonitoringJob[]> {
    return await db
      .select()
      .from(channelMonitoringJobs)
      .where(eq(channelMonitoringJobs.channelId, channelId))
      .orderBy(desc(channelMonitoringJobs.createdAt));
  }

  async updateMonitoringJob(id: number, updates: Partial<InsertChannelMonitoringJob>): Promise<void> {
    await db
      .update(channelMonitoringJobs)
      .set(updates)
      .where(eq(channelMonitoringJobs.id, id));
  }

  async getPendingJobs(): Promise<ChannelMonitoringJob[]> {
    return await db
      .select()
      .from(channelMonitoringJobs)
      .where(eq(channelMonitoringJobs.status, "pending"))
      .orderBy(channelMonitoringJobs.createdAt);
  }

  // Enhanced summary operations
  async getChannelSummaries(channelId: number, userId: string): Promise<Summary[]> {
    return await db
      .select()
      .from(summaries)
      .where(and(eq(summaries.channelId, channelId), eq(summaries.userId, userId)))
      .orderBy(desc(summaries.videoPublishedAt));
  }

  async getRecentMonitoredSummaries(userId: string, limit: number = 20): Promise<Summary[]> {
    return await db
      .select()
      .from(summaries)
      .where(and(eq(summaries.userId, userId), eq(summaries.isMonitored, true)))
      .orderBy(desc(summaries.videoPublishedAt))
      .limit(limit);
  }

  // Enhanced search methods for different search types
  async searchSummariesByVector(userId: string, query: string, filters?: {
    channelIds?: number[];
    topics?: string[];
    dateRange?: [Date, Date];
    limit?: number;
  }): Promise<Summary[]> {
    // TODO: Implement actual vector similarity search when embeddings are ready
    // For now, use semantic text search as fallback
    return await this.searchSummariesByText(userId, query, filters);
  }

  // Topic-scoped text search - exact keyword matching within specific topics
  async searchSummariesInTopic(userId: string, topicId: number, query: string, options?: {
    exactMatch?: boolean;
    includeTranscripts?: boolean;
    limit?: number;
  }): Promise<Summary[]> {
    const { exactMatch = false, includeTranscripts = true, limit = 50 } = options || {};
    
    // Build search pattern based on exact match preference
    const searchPattern = exactMatch ? query : `%${query}%`;
    const searchOperator = exactMatch ? sql`=` : sql`ILIKE`;
    
    // Get summaries from channels assigned to this topic
    let queryBuilder = db
      .select({
        id: summaries.id,
        userId: summaries.userId,
        channelId: summaries.channelId,
        videoId: summaries.videoId,
        videoTitle: summaries.videoTitle,
        videoChannel: summaries.videoChannel,
        videoChannelId: summaries.videoChannelId,
        videoThumbnail: summaries.videoThumbnail,
        videoDuration: summaries.videoDuration,
        videoViews: summaries.videoViews,
        videoPublishedAt: summaries.videoPublishedAt,
        videoDescription: summaries.videoDescription,
        videoTranscript: summaries.videoTranscript,
        summaryContent: summaries.summaryContent,
        readTime: summaries.readTime,
        timeSaved: summaries.timeSaved,
        createdAt: summaries.createdAt,
        summaryEmbedding: summaries.summaryEmbedding,
        transcriptEmbedding: summaries.transcriptEmbedding,
        topics: summaries.topics,
        keyEntities: summaries.keyEntities,
        mcpMetadata: summaries.mcpMetadata,
        isMonitored: summaries.isMonitored,
      })
      .from(summaries)
      .innerJoin(channelTopics, eq(summaries.channelId, channelTopics.channelId))
      .where(
        and(
          eq(channelTopics.topicId, topicId),
          eq(summaries.userId, userId)
        )
      );

    // Add text search conditions
    const searchConditions = [];
    
    if (exactMatch) {
      searchConditions.push(
        sql`${summaries.summaryContent} ILIKE ${`%${query}%`}`,
        sql`${summaries.videoTitle} ILIKE ${`%${query}%`}`
      );
      
      if (includeTranscripts) {
        searchConditions.push(sql`${summaries.videoTranscript} ILIKE ${`%${query}%`}`);
      }
    } else {
      searchConditions.push(
        ilike(summaries.summaryContent, searchPattern),
        ilike(summaries.videoTitle, searchPattern)
      );
      
      if (includeTranscripts) {
        searchConditions.push(ilike(summaries.videoTranscript, searchPattern));
      }
    }

    queryBuilder = queryBuilder.where(
      and(
        eq(channelTopics.topicId, topicId),
        eq(summaries.userId, userId),
        or(...searchConditions)
      )
    );

    return await queryBuilder
      .orderBy(desc(summaries.videoPublishedAt))
      .limit(limit);
  }

  // Boolean search with operators (AND, OR, NOT)
  async searchSummariesBoolean(userId: string, booleanQuery: string, filters?: {
    topicIds?: number[];
    channelIds?: number[];
    dateRange?: [Date, Date];
    limit?: number;
  }): Promise<Summary[]> {
    // Parse boolean query into SQL conditions
    // This is a simplified implementation - could be enhanced with a proper parser
    const terms = this.parseBooleanQuery(booleanQuery);
    
    let baseQuery = db
      .select()
      .from(summaries)
      .where(eq(summaries.userId, userId));

    // Apply topic filter if specified
    if (filters?.topicIds && filters.topicIds.length > 0) {
      baseQuery = baseQuery
        .innerJoin(channelTopics, eq(summaries.channelId, channelTopics.channelId))
        .where(
          and(
            eq(summaries.userId, userId),
            sql`${channelTopics.topicId} = ANY(${filters.topicIds})`
          )
        );
    }

    // Apply channel filter
    if (filters?.channelIds && filters.channelIds.length > 0) {
      baseQuery = baseQuery.where(
        and(
          eq(summaries.userId, userId),
          sql`${summaries.channelId} = ANY(${filters.channelIds})`
        )
      );
    }

    // Apply date range filter
    if (filters?.dateRange) {
      baseQuery = baseQuery.where(
        and(
          eq(summaries.userId, userId),
          gte(summaries.videoPublishedAt, filters.dateRange[0]),
          lt(summaries.videoPublishedAt, filters.dateRange[1])
        )
      );
    }

    // Build text search conditions from parsed terms
    const searchConditions = this.buildSearchConditions(terms);
    
    return await baseQuery
      .where(
        and(
          eq(summaries.userId, userId),
          searchConditions
        )
      )
      .orderBy(desc(summaries.videoPublishedAt))
      .limit(filters?.limit || 50);
  }

  // Multi-mode search that combines different search types
  async searchSummariesMultiMode(userId: string, searchConfig: {
    query: string;
    searchType: 'text' | 'semantic' | 'boolean' | 'hybrid';
    topicScope?: number; // Search within specific topic
    filters?: {
      channelIds?: number[];
      dateRange?: [Date, Date];
      includeTranscripts?: boolean;
      exactMatch?: boolean;
    };
    limit?: number;
  }): Promise<Summary[]> {
    const { query, searchType, topicScope, filters, limit = 20 } = searchConfig;

    switch (searchType) {
      case 'text':
        if (topicScope) {
          return this.searchSummariesInTopic(userId, topicScope, query, {
            exactMatch: filters?.exactMatch,
            includeTranscripts: filters?.includeTranscripts,
            limit
          });
        }
        return this.searchSummariesByText(userId, query);

      case 'semantic':
        return this.searchSummariesByVector(userId, query, {
          topics: topicScope ? [topicScope.toString()] : undefined,
          channelIds: filters?.channelIds,
          dateRange: filters?.dateRange,
          limit
        });

      case 'boolean':
        return this.searchSummariesBoolean(userId, query, {
          topicIds: topicScope ? [topicScope] : undefined,
          channelIds: filters?.channelIds,
          dateRange: filters?.dateRange,
          limit
        });

      case 'hybrid':
        // Combine multiple search methods and merge results
        const [textResults, semanticResults] = await Promise.all([
          topicScope 
            ? this.searchSummariesInTopic(userId, topicScope, query, { limit: limit / 2 })
            : this.searchSummariesByText(userId, query),
          this.searchSummariesByVector(userId, query, { limit: limit / 2 })
        ]);
        
        // Merge and deduplicate results
        const mergedResults = this.mergeSearchResults([textResults, semanticResults]);
        return mergedResults.slice(0, limit);

      default:
        return this.searchSummariesByText(userId, query);
    }
  }

  // Helper method to parse boolean queries
  private parseBooleanQuery(query: string): { terms: string[]; operators: string[] } {
    // Simplified boolean query parsing
    const tokens = query.split(/\s+(AND|OR|NOT)\s+/i);
    const terms = tokens.filter((_, index) => index % 2 === 0);
    const operators = tokens.filter((_, index) => index % 2 === 1);
    
    return { terms, operators };
  }

  // Helper method to build SQL search conditions from parsed terms
  private buildSearchConditions(parsedQuery: { terms: string[]; operators: string[] }): SQL {
    const { terms } = parsedQuery;
    
    if (terms.length === 0) return sql`1=1`;
    
    // For now, implement simple AND logic between terms
    const conditions = terms.map(term => 
      or(
        ilike(summaries.summaryContent, `%${term.trim()}%`),
        ilike(summaries.videoTitle, `%${term.trim()}%`)
      )
    );
    
    return and(...conditions) as SQL;
  }

  // Helper method to merge and deduplicate search results
  private mergeSearchResults(resultSets: Summary[][]): Summary[] {
    const seen = new Set<number>();
    const merged: Summary[] = [];
    
    for (const resultSet of resultSets) {
      for (const summary of resultSet) {
        if (!seen.has(summary.id)) {
          seen.add(summary.id);
          merged.push(summary);
        }
      }
    }
    
    return merged;
  }

  async getSimilarSummaries(summaryId: number, userId: string, limit: number = 5): Promise<Summary[]> {
    // Simplified similarity based on topics for now
    const [targetSummary] = await db
      .select()
      .from(summaries)
      .where(and(eq(summaries.id, summaryId), eq(summaries.userId, userId)));

    if (!targetSummary?.topics) return [];

    return await db
      .select()
      .from(summaries)
      .where(
        and(
          eq(summaries.userId, userId),
          sql`${summaries.topics} IS NOT NULL AND ${summaries.topics} != ''`
        )
      )
      .orderBy(desc(summaries.createdAt))
      .limit(limit);
  }

  // Topic management operations
  async getTopics(userId?: string): Promise<Topic[]> {
    return await db
      .select()
      .from(topics)
      .where(eq(topics.isActive, true))
      .orderBy(topics.name);
  }

  async getTopic(id: number): Promise<Topic | undefined> {
    const [topic] = await db.select().from(topics).where(eq(topics.id, id));
    return topic;
  }

  async createTopic(topic: InsertTopic): Promise<Topic> {
    const [created] = await db.insert(topics).values(topic).returning();
    return created;
  }

  async updateTopic(id: number, updates: Partial<InsertTopic>): Promise<Topic> {
    const [updated] = await db
      .update(topics)
      .set(updates)
      .where(eq(topics.id, id))
      .returning();
    return updated;
  }

  async deleteTopic(id: number): Promise<void> {
    await db.delete(topics).where(eq(topics.id, id));
  }

  async getTopicHierarchy(): Promise<Topic[]> {
    return await db
      .select()
      .from(topics)
      .where(eq(topics.isActive, true))
      .orderBy(topics.parentTopicId, topics.name);
  }

  // Channel-Topic operations
  async getChannelTopics(channelId: number): Promise<ChannelTopic[]> {
    return await db
      .select()
      .from(channelTopics)
      .where(eq(channelTopics.channelId, channelId));
  }

  async assignChannelToTopic(channelTopic: InsertChannelTopic): Promise<ChannelTopic> {
    const [created] = await db
      .insert(channelTopics)
      .values(channelTopic)
      .onConflictDoUpdate({
        target: [channelTopics.channelId, channelTopics.topicId],
        set: {
          relevanceScore: channelTopic.relevanceScore,
          assignmentType: channelTopic.assignmentType,
        },
      })
      .returning();
    return created;
  }

  async removeChannelFromTopic(channelId: number, topicId: number): Promise<void> {
    await db
      .delete(channelTopics)
      .where(
        and(
          eq(channelTopics.channelId, channelId),
          eq(channelTopics.topicId, topicId)
        )
      );
  }

  async getChannelsByTopic(topicId: number, userId: string): Promise<Channel[]> {
    return await db
      .select({
        id: channels.id,
        userId: channels.userId,
        channelId: channels.channelId,
        channelName: channels.channelName,
        channelHandle: channels.channelHandle,
        channelThumbnail: channels.channelThumbnail,
        subscriberCount: channels.subscriberCount,
        isActive: channels.isActive,
        lastChecked: channels.lastChecked,
        videoCount: channels.videoCount,
        createdAt: channels.createdAt,
        updatedAt: channels.updatedAt,
      })
      .from(channels)
      .innerJoin(channelTopics, eq(channels.id, channelTopics.channelId))
      .where(
        and(
          eq(channelTopics.topicId, topicId),
          eq(channels.userId, userId)
        )
      );
  }

  async getTopicsByChannel(channelId: number): Promise<Topic[]> {
    return await db
      .select({
        id: topics.id,
        name: topics.name,
        description: topics.description,
        parentTopicId: topics.parentTopicId,
        topicType: topics.topicType,
        colorHex: topics.colorHex,
        isActive: topics.isActive,
        embedding: topics.embedding,
        summaryCount: topics.summaryCount,
        createdAt: topics.createdAt,
      })
      .from(topics)
      .innerJoin(channelTopics, eq(topics.id, channelTopics.topicId))
      .where(eq(channelTopics.channelId, channelId));
  }

  // Summary-Topic operations
  async getSummaryTopics(summaryId: number): Promise<SummaryTopic[]> {
    return await db
      .select()
      .from(summaryTopics)
      .where(eq(summaryTopics.summaryId, summaryId));
  }

  async getSummariesByTopic(topicId: number, userId: string): Promise<Summary[]> {
    // Get summaries from all channels assigned to this topic
    return await db
      .select({
        id: summaries.id,
        userId: summaries.userId,
        channelId: summaries.channelId,
        videoId: summaries.videoId,
        videoTitle: summaries.videoTitle,
        videoChannel: summaries.videoChannel,
        videoChannelId: summaries.videoChannelId,
        videoThumbnail: summaries.videoThumbnail,
        videoDuration: summaries.videoDuration,
        videoViews: summaries.videoViews,
        videoPublishedAt: summaries.videoPublishedAt,
        videoDescription: summaries.videoDescription,
        videoTranscript: summaries.videoTranscript,
        summaryContent: summaries.summaryContent,
        readTime: summaries.readTime,
        timeSaved: summaries.timeSaved,
        createdAt: summaries.createdAt,
        summaryEmbedding: summaries.summaryEmbedding,
        transcriptEmbedding: summaries.transcriptEmbedding,
        topics: summaries.topics,
        keyEntities: summaries.keyEntities,
        mcpMetadata: summaries.mcpMetadata,
        isMonitored: summaries.isMonitored,
      })
      .from(summaries)
      .innerJoin(channelTopics, eq(summaries.channelId, channelTopics.channelId))
      .where(
        and(
          eq(channelTopics.topicId, topicId),
          eq(summaries.userId, userId),
          eq(summaries.isMonitored, true)
        )
      )
      .orderBy(desc(summaries.createdAt))
      .limit(50);
  }

  async autoTagSummaryWithTopics(summaryId: number, topicNames: string[]): Promise<void> {
    // Create or find topics by name
    for (const topicName of topicNames) {
      let [topic] = await db
        .select()
        .from(topics)
        .where(eq(topics.name, topicName))
        .limit(1);

      if (!topic) {
        [topic] = await db
          .insert(topics)
          .values({
            name: topicName,
            topicType: "auto_extracted",
            description: `Auto-extracted topic: ${topicName}`,
          })
          .returning();
      }

      // Link summary to topic
      await db
        .insert(summaryTopics)
        .values({
          summaryId,
          topicId: topic.id,
          relevanceScore: "0.8",
          detectionMethod: "llm_extracted",
        })
        .onConflictDoNothing();
    }
  }

}

export const storage = new DatabaseStorage();