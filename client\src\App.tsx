import { Switch, Route } from "wouter";
import { queryClient } from "./lib/queryClient";
import { QueryClientProvider } from "@tanstack/react-query";
import { Toaster } from "@/components/ui/toaster";
import { TooltipProvider } from "@/components/ui/tooltip";
import { useAuth } from "@/hooks/useAuth";
import Layout from "@/components/Layout";
import EnhancedLayout from "@/components/enhanced-layout";
import Landing from "@/pages/landing";
import Dashboard from "@/pages/dashboard";
import ProgressiveDashboard from "@/components/progressive-dashboard";
import Library from "@/pages/library";
import EnhancedLibrary from "@/pages/enhanced-library";
import UnifiedLibrary from "@/pages/unified-library";
import Topics from "@/pages/topics";
import EnhancedTopics from "@/pages/enhanced-topics";
import Subscribe from "@/pages/subscribe";
import Channels from "@/pages/channels";
import CreatePage from "@/pages/create";
import SearchPage from "@/pages/search";
import Settings from "@/pages/settings";
import VideoSummary from "@/pages/video-summary";
import AdminPage from "@/pages/admin";
import NotFound from "@/pages/not-found";

function Router() {
  const { isAuthenticated, isLoading } = useAuth();

  return (
    <Switch>
      {isLoading || !isAuthenticated ? (
        <Route path="/">
          <Layout showNavigation={false}>
            <Landing />
          </Layout>
        </Route>
      ) : (
        <>
          <Route path="/">
            <EnhancedLayout>
              <CreatePage />
            </EnhancedLayout>
          </Route>
          <Route path="/library">
            <EnhancedLayout>
              <UnifiedLibrary />
            </EnhancedLayout>
          </Route>
          <Route path="/channels">
            <EnhancedLayout>
              <Channels />
            </EnhancedLayout>
          </Route>

          <Route path="/topics">
            <EnhancedLayout>
              <EnhancedTopics />
            </EnhancedLayout>
          </Route>
          <Route path="/topics/:id">
            <EnhancedLayout>
              <EnhancedTopics />
            </EnhancedLayout>
          </Route>
          <Route path="/search">
            <SearchPage />
          </Route>
          <Route path="/subscribe">
            <Layout showNavigation={true}>
              <Subscribe />
            </Layout>
          </Route>
          <Route path="/settings">
            <EnhancedLayout>
              <Settings />
            </EnhancedLayout>
          </Route>
          <Route path="/admin">
            <AdminPage />
          </Route>
          <Route path="/summary/:id">
            <EnhancedLayout>
              <VideoSummary />
            </EnhancedLayout>
          </Route>
        </>
      )}
      <Route>
        <Layout showNavigation={false}>
          <NotFound />
        </Layout>
      </Route>
    </Switch>
  );
}

function App() {
  return (
    <QueryClientProvider client={queryClient}>
      <TooltipProvider>
        <Toaster />
        <Router />
      </TooltipProvider>
    </QueryClientProvider>
  );
}

export default App;
