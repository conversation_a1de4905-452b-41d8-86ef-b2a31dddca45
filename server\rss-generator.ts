import type { Summary, Channel, Topic } from "@shared/schema";

interface RSSItem {
  title: string;
  description: string;
  link: string;
  pubDate: string;
  guid: string;
  author?: string;
  category?: string;
}

interface RSSFeed {
  title: string;
  description: string;
  link: string;
  items: RSSItem[];
  lastBuildDate: string;
}

export function generateRSSFeed(feed: RSSFeed): string {
  const escapeXML = (str: string) => {
    return str
      .replace(/&/g, '&amp;')
      .replace(/</g, '&lt;')
      .replace(/>/g, '&gt;')
      .replace(/"/g, '&quot;')
      .replace(/'/g, '&#39;');
  };

  const rssItems = feed.items.map(item => `
    <item>
      <title><![CDATA[${item.title}]]></title>
      <description><![CDATA[${item.description}]]></description>
      <link>${escapeXML(item.link)}</link>
      <pubDate>${item.pubDate}</pubDate>
      <guid>${escapeXML(item.guid)}</guid>
      ${item.author ? `<author>${escapeXML(item.author)}</author>` : ''}
      ${item.category ? `<category>${escapeXML(item.category)}</category>` : ''}
    </item>`).join('');

  return `<?xml version="1.0" encoding="UTF-8"?>
<rss version="2.0" xmlns:atom="http://www.w3.org/2005/Atom">
  <channel>
    <title><![CDATA[${feed.title}]]></title>
    <description><![CDATA[${feed.description}]]></description>
    <link>${escapeXML(feed.link)}</link>
    <lastBuildDate>${feed.lastBuildDate}</lastBuildDate>
    <ttl>60</ttl>
    <atom:link href="${escapeXML(feed.link)}" rel="self" type="application/rss+xml" />
    ${rssItems}
  </channel>
</rss>`;
}

export function createAllLibraryFeed(summaries: Summary[], baseUrl: string): RSSFeed {
  const items: RSSItem[] = summaries.map(summary => ({
    title: summary.videoTitle,
    description: stripHtml(summary.summaryContent).substring(0, 500) + '...',
    link: `${baseUrl}/?summaryId=${summary.id}`,
    pubDate: summary.createdAt ? new Date(summary.createdAt.toString()).toUTCString() : new Date().toUTCString(),
    guid: `${baseUrl}/summary/${summary.id}`,
    author: summary.videoChannel,
    category: 'Video Summary'
  }));

  return {
    title: 'VideoSummarizer - All Summaries',
    description: 'Latest video summaries from your personal library',
    link: `${baseUrl}/rss/library`,
    items,
    lastBuildDate: new Date().toUTCString()
  };
}

export function createChannelFeed(summaries: Summary[], channel: Channel, baseUrl: string): RSSFeed {
  const items: RSSItem[] = summaries.map(summary => ({
    title: summary.videoTitle,
    description: stripHtml(summary.summaryContent).substring(0, 500) + '...',
    link: `${baseUrl}/?summaryId=${summary.id}`,
    pubDate: summary.createdAt ? new Date(summary.createdAt.toString()).toUTCString() : new Date().toUTCString(),
    guid: `${baseUrl}/summary/${summary.id}`,
    author: summary.videoChannel,
    category: `Channel: ${channel.channelName}`
  }));

  return {
    title: `VideoSummarizer - ${channel.channelName}`,
    description: `Latest video summaries from ${channel.channelName}`,
    link: `${baseUrl}/rss/channel/${channel.id}`,
    items,
    lastBuildDate: new Date().toUTCString()
  };
}

export function createTopicFeed(summaries: Summary[], topic: Topic, baseUrl: string): RSSFeed {
  const items: RSSItem[] = summaries.map(summary => ({
    title: summary.videoTitle,
    description: stripHtml(summary.summaryContent).substring(0, 500) + '...',
    link: `${baseUrl}/?summaryId=${summary.id}`,
    pubDate: summary.createdAt ? new Date(summary.createdAt.toString()).toUTCString() : new Date().toUTCString(),
    guid: `${baseUrl}/summary/${summary.id}`,
    author: summary.videoChannel,
    category: `Topic: ${topic.name}`
  }));

  return {
    title: `VideoSummarizer - ${topic.name} Topic`,
    description: `Latest video summaries from channels in the ${topic.name} topic. ${topic.description || ''}`,
    link: `${baseUrl}/rss/topic/${topic.id}`,
    items,
    lastBuildDate: new Date().toUTCString()
  };
}

function stripHtml(html: string): string {
  return html.replace(/<[^>]*>/g, '').replace(/\s+/g, ' ').trim();
}