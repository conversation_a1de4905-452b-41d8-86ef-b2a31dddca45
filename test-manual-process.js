import { processRecentVideos, storage } from './server/routes-simple.js';

// Test the video processing function directly
async function testManualProcess() {
  try {
    console.log('Testing video processing for Parker Rex channel...');
    
    // Channel ID from your database: UCcuaQecz84wTuxKzr1Yxi4Q (Parker Rex)
    // Database ID: 17, User ID: 40640447
    
    await processRecentVideos('UCcuaQecz84wTuxKzr1Yxi4Q', '40640447', 17);
    
    console.log('Processing completed successfully');
  } catch (error) {
    console.error('Test error:', error);
  }
}

testManualProcess();