import { Link, useLocation } from "wouter";
import { Video, LogOut, Plus, Library, MonitorSpeaker, User, Settings, CreditCard, BarChart3, Tag } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { 
  DropdownMenu, 
  DropdownMenuContent, 
  DropdownMenuItem, 
  DropdownMenuSeparator, 
  DropdownMenuTrigger 
} from "@/components/ui/dropdown-menu";
import { Badge } from "@/components/ui/badge";
import { useAuth } from "@/hooks/useAuth";
import { useQuery } from "@tanstack/react-query";

interface User {
  id: string;
  email: string;
  firstName: string;
  lastName: string;
  profileImageUrl: string;
  usageCount: number;
  usageLimit: number;
  subscriptionType: string;
}

interface LayoutProps {
  children: React.ReactNode;
  showNavigation?: boolean;
}

export default function Layout({ children, showNavigation = false }: LayoutProps) {
  const { user: authUser } = useAuth();
  const [location] = useLocation();
  
  // Fetch user data if authenticated
  const { data: user } = useQuery<User>({
    queryKey: ["/api/auth/user"],
    enabled: !!authUser,
  });

  const handleLogout = () => {
    window.location.href = "/api/logout";
  };

  // Navigation items with grouping
  const navigationItems = [
    {
      href: "/",
      label: "Create",
      icon: Plus,
      description: "Summarize videos"
    },
    {
      href: "/library",
      label: "Library", 
      icon: Library,
      description: "Browse summaries"
    },
    {
      href: "/channels",
      label: "Channels",
      icon: MonitorSpeaker,
      description: "Monitor channels"
    },
    {
      href: "/topics",
      label: "Topics",
      icon: Tag,
      description: "Organize content"
    }
  ];

  // Check if current path matches navigation item
  const isActiveRoute = (href: string) => {
    if (href === "/") return location === "/";
    return location.startsWith(href);
  };

  // Calculate usage stats
  const currentLimit = user?.subscriptionType === 'pro' ? 100 : 
                      user?.subscriptionType === 'enterprise' ? 400 :
                      user?.subscriptionType === 'super-premium' ? 999999 : 5;
  const usagePercentage = user ? ((user.usageCount ?? 0) / currentLimit) * 100 : 0;

  return (
    <div className="min-h-screen bg-background">
      {/* Redesigned Header with Consistent Navigation */}
      <header className="sticky top-0 z-50 w-full border-b border-border/40 bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60 mt-4">
        <div className="container flex h-16 max-w-screen-2xl items-center justify-between px-8 md:px-12">
          
          {/* Logo and Brand */}
          <div className="flex items-center space-x-8">
            <Link href="/" className="flex items-center space-x-2 hover:opacity-80 transition-opacity">
              <div className="flex h-9 w-9 items-center justify-center rounded-lg bg-primary">
                <Video className="h-5 w-5 text-primary-foreground" />
              </div>
              <span className="font-bold text-xl">VideoSummarizer</span>
            </Link>

            {/* Primary Navigation - Consistent across all pages */}
            {showNavigation && (
              <nav className="hidden md:flex items-center space-x-1">
                {navigationItems.map((item) => {
                  const isActive = isActiveRoute(item.href);
                  return (
                    <Link key={item.href} href={item.href}>
                      <div className={`flex items-center space-x-2 px-4 py-2 rounded-lg text-sm font-medium transition-colors ${
                        isActive 
                          ? "bg-primary/10 text-primary" 
                          : "text-muted-foreground hover:text-foreground hover:bg-accent"
                      }`}>
                        <item.icon className="h-4 w-4" />
                        <span>{item.label}</span>
                      </div>
                    </Link>
                  );
                })}
              </nav>
            )}
          </div>

          {/* User Account Section - Grouped */}
          {showNavigation && user && (
            <div className="flex items-center space-x-3">
              
              {/* Usage Status Indicator */}
              <div className="hidden lg:flex items-center space-x-2 px-3 py-1 rounded-lg bg-accent/50">
                <BarChart3 className="h-4 w-4 text-muted-foreground" />
                <span className="text-sm font-medium">
                  {user.usageCount}/{currentLimit}
                </span>
                {user.subscriptionType === 'free' && usagePercentage > 80 && (
                  <Badge variant="secondary" className="text-xs">
                    {Math.floor((currentLimit - (user.usageCount ?? 0)))} left
                  </Badge>
                )}
              </div>

              {/* Quick Upgrade for Free Users */}
              {user.subscriptionType === 'free' && (
                <Button 
                  variant="outline" 
                  size="sm"
                  onClick={() => window.location.href = "/subscribe"}
                  className="hidden sm:flex"
                >
                  Upgrade
                </Button>
              )}

              {/* Account Dropdown - Grouped Account Management */}
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="ghost" className="flex items-center space-x-2 px-2">
                    <Avatar className="w-8 h-8">
                      <AvatarImage src={user.profileImageUrl || ""} />
                      <AvatarFallback className="text-xs">
                        {user.firstName?.[0]}{user.lastName?.[0]}
                      </AvatarFallback>
                    </Avatar>
                    <div className="hidden md:block text-left">
                      <div className="text-sm font-medium">{user.firstName}</div>
                      <div className="text-xs text-muted-foreground capitalize">
                        {user.subscriptionType} Plan
                      </div>
                    </div>
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end" className="w-56">
                  
                  {/* Account Info */}
                  <div className="px-2 py-2">
                    <div className="text-sm font-medium">{user.firstName} {user.lastName}</div>
                    <div className="text-xs text-muted-foreground">{user.email}</div>
                  </div>
                  
                  <DropdownMenuSeparator />
                  
                  {/* Usage & Billing */}
                  <DropdownMenuItem>
                    <BarChart3 className="mr-2 h-4 w-4" />
                    <span>Usage: {user.usageCount}/{currentLimit}</span>
                  </DropdownMenuItem>
                  
                  <DropdownMenuItem onClick={() => window.location.href = "/subscribe"}>
                    <CreditCard className="mr-2 h-4 w-4" />
                    <span>Billing & Plans</span>
                  </DropdownMenuItem>
                  
                  <DropdownMenuSeparator />
                  
                  {/* Settings & Account */}
                  <DropdownMenuItem>
                    <Settings className="mr-2 h-4 w-4" />
                    <span>Settings</span>
                  </DropdownMenuItem>
                  
                  <DropdownMenuItem>
                    <User className="mr-2 h-4 w-4" />
                    <span>Profile</span>
                  </DropdownMenuItem>
                  
                  <DropdownMenuSeparator />
                  
                  <DropdownMenuItem onClick={handleLogout} className="text-red-600">
                    <LogOut className="mr-2 h-4 w-4" />
                    <span>Sign Out</span>
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            </div>
          )}
        </div>
      </header>

      {/* Main Content */}
      <main className="flex-1">
        {children}
      </main>
    </div>
  );
}