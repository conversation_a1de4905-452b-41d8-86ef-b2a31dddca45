import { Link, useLocation } from "wouter";
import { Video, LogOut, Plus, Library, MonitorSpeaker, User, Settings, CreditCard, BarChart3, Tag } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { 
  DropdownMenu, 
  DropdownMenuContent, 
  DropdownMenuItem, 
  DropdownMenuSeparator, 
  DropdownMenuTrigger 
} from "@/components/ui/dropdown-menu";
import { Badge } from "@/components/ui/badge";
import { useAuth } from "@/hooks/useAuth";
import { useQuery } from "@tanstack/react-query";

interface User {
  id: string;
  email: string;
  firstName: string;
  lastName: string;
  profileImageUrl: string;
  usageCount: number;
  usageLimit: number;
  subscriptionType: string;
}

interface LayoutProps {
  children: React.ReactNode;
  showNavigation?: boolean;
}

export default function Layout({ children, showNavigation = false }: LayoutProps) {
  const { user: authUser } = useAuth();
  const [location] = useLocation();
  
  // Fetch user data if authenticated
  const { data: user } = useQuery<User>({
    queryKey: ["/api/auth/user"],
    enabled: !!authUser,
  });

  const handleLogout = () => {
    window.location.href = "/api/logout";
  };

  // Navigation items with grouping
  const navigationItems = [
    {
      href: "/",
      label: "Create",
      icon: Plus,
      description: "Summarize videos"
    },
    {
      href: "/library",
      label: "Library", 
      icon: Library,
      description: "Browse summaries"
    },
    {
      href: "/channels",
      label: "Channels",
      icon: MonitorSpeaker,
      description: "Monitor channels"
    },
    {
      href: "/topics",
      label: "Topics",
      icon: Tag,
      description: "Organize content"
    }
  ];

  // Check if current path matches navigation item
  const isActiveRoute = (href: string) => {
    if (href === "/") return location === "/";
    return location.startsWith(href);
  };

  // Calculate usage stats
  const currentLimit = user?.subscriptionType === 'pro' ? 100 : 
                      user?.subscriptionType === 'enterprise' ? 400 :
                      user?.subscriptionType === 'super-premium' ? 999999 : 5;
  const usagePercentage = user ? ((user.usageCount ?? 0) / currentLimit) * 100 : 0;

  return (
    <div className="min-h-screen bg-background">
      {/* Layout component no longer renders headers - EnhancedLayout handles all navigation */}

      {/* Main Content */}
      <main className="flex-1">
        {children}
      </main>
    </div>
  );
}