import { Link, useLocation } from "wouter";
import { useState, useEffect } from "react";
import { 
  Video, 
  Plus, 
  Library, 
  MonitorSpeaker, 
  Tag,
  Search,
  Bell,
  ChevronRight,
  Home,
  Zap,
  Clock,
  TrendingUp,
  Menu,
  X,
  LogOut,
  User,
  Settings,
  CreditCard,
  BarChart3
} from "lucide-react";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Card, CardContent } from "@/components/ui/card";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { 
  DropdownMenu, 
  DropdownMenuContent, 
  DropdownMenuItem, 
  DropdownMenuSeparator, 
  DropdownMenuTrigger 
} from "@/components/ui/dropdown-menu";
import {
  Sheet,
  SheetContent,
  SheetHeader,
  SheetTitle,
  SheetTrigger,
} from "@/components/ui/sheet";
import { cn } from "@/lib/utils";
import { useAuth } from "@/hooks/useAuth";
import { useQuery } from "@tanstack/react-query";

interface BreadcrumbItem {
  label: string;
  href?: string;
  isActive?: boolean;
}

interface QuickAction {
  id: string;
  label: string;
  icon: React.ComponentType<{ className?: string }>;
  onClick: () => void;
  variant?: "primary" | "secondary";
  badge?: number;
}

interface EnhancedLayoutProps {
  children: React.ReactNode;
  title?: string;
  breadcrumbs?: BreadcrumbItem[];
  quickActions?: QuickAction[];
  showGlobalCreate?: boolean;
  contentDensity?: "comfortable" | "compact" | "spacious";
}

export function EnhancedLayout({
  children,
  title,
  breadcrumbs = [],
  quickActions = [],
  showGlobalCreate = true,
  contentDensity = "comfortable"
}: EnhancedLayoutProps) {
  const [location] = useLocation();
  const { user: authUser } = useAuth();
  const [globalCreateUrl, setGlobalCreateUrl] = useState("");
  const [showQuickCreate, setShowQuickCreate] = useState(false);
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);

  // Fetch user data
  const { data: user } = useQuery({
    queryKey: ["/api/auth/user"],
    enabled: !!authUser,
  });

  // Fetch real-time data for contextual information
  const { data: recentActivity = [] } = useQuery({
    queryKey: ["/api/monitored-summaries"],
    enabled: !!authUser,
    refetchInterval: 30000 // Refresh every 30 seconds
  });

  const { data: pendingJobs = [] } = useQuery({
    queryKey: ["/api/channels/jobs/pending"],
    enabled: !!authUser,
    refetchInterval: 10000 // Check for pending jobs
  });

  // Generate contextual breadcrumbs based on location
  const getContextualBreadcrumbs = (): BreadcrumbItem[] => {
    const baseBreadcrumbs: BreadcrumbItem[] = [
      { label: "Home", href: "/" }
    ];

    if (location === "/library") {
      return [...baseBreadcrumbs, { label: "Library", isActive: true }];
    }
    if (location === "/channels") {
      return [...baseBreadcrumbs, { label: "Channels", isActive: true }];
    }
    if (location === "/topics") {
      return [...baseBreadcrumbs, { label: "Topics", isActive: true }];
    }

    return breadcrumbs.length > 0 ? breadcrumbs : baseBreadcrumbs;
  };

  // Get page-specific title
  const getPageTitle = (): string => {
    if (title) return title;
    
    switch (location) {
      case "/": return "Create Summary";
      case "/library": return "Summary Library";
      case "/channels": return "Channel Management";
      case "/topics": return "Topic Organization";
      default: return "VideoSummarizer";
    }
  };

  // Quick actions based on context
  const getContextualActions = (): QuickAction[] => {
    const baseActions: QuickAction[] = [
      {
        id: "search",
        label: "Search",
        icon: Search,
        onClick: () => {}, // Implement global search
        variant: "secondary"
      }
    ];

    // Add location-specific actions
    if (location === "/library") {
      baseActions.unshift({
        id: "create-summary",
        label: "New Summary",
        icon: Plus,
        onClick: () => setShowQuickCreate(true),
        variant: "primary"
      });
    }

    if (location === "/channels") {
      baseActions.unshift({
        id: "add-channel",
        label: "Add Channel",
        icon: MonitorSpeaker,
        onClick: () => {}, // Navigate to channel import
        variant: "primary"
      });
    }

    if (location === "/topics") {
      baseActions.unshift({
        id: "create-topic",
        label: "New Topic",
        icon: Tag,
        onClick: () => {}, // Open topic creation modal
        variant: "primary"
      });
    }

    // Add notifications for pending jobs
    if (pendingJobs && pendingJobs.length > 0) {
      baseActions.push({
        id: "pending-jobs",
        label: "Processing",
        icon: Clock,
        onClick: () => {}, // Show processing status
        variant: "secondary",
        badge: pendingJobs.length
      });
    }

    return quickActions.length > 0 ? quickActions : baseActions;
  };

  // Density classes for content spacing
  const getDensityClasses = () => {
    switch (contentDensity) {
      case "compact":
        return "space-y-3 px-4 py-4";
      case "spacious":
        return "space-y-8 px-8 py-8";
      default:
        return "space-y-6 px-6 py-6";
    }
  };

  const contextualBreadcrumbs = getContextualBreadcrumbs();
  const pageTitle = getPageTitle();
  const contextualActions = getContextualActions();

  return (
    <div className="min-h-screen bg-background">
      {/* Enhanced Header with Context */}
      <header className="sticky top-0 z-50 w-full border-b border-border/40 bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
        <div className="container flex h-16 max-w-screen-2xl items-center justify-between px-4 md:px-6">
          
          {/* Logo and Main Navigation */}
          <div className="flex items-center space-x-8">
            <Link href="/" className="flex items-center space-x-2 hover:opacity-80 transition-opacity">
              <div className="flex h-8 w-8 items-center justify-center rounded-lg bg-primary">
                <Video className="h-4 w-4 text-primary-foreground" />
              </div>
              <span className="font-bold text-lg">VideoSummarizer</span>
            </Link>

            {/* Main Navigation */}
            <nav className="hidden md:flex items-center space-x-1">
              <Link href="/">
                <Button 
                  variant={location === "/" ? "default" : "ghost"} 
                  size="sm"
                  className="flex items-center gap-2"
                >
                  <Plus className="h-4 w-4" />
                  Create
                </Button>
              </Link>
              
              <Link href="/library">
                <Button 
                  variant={location === "/library" ? "default" : "ghost"} 
                  size="sm"
                  className="flex items-center gap-2"
                >
                  <Library className="h-4 w-4" />
                  Library
                </Button>
              </Link>
              
              <Link href="/channels">
                <Button 
                  variant={location === "/channels" ? "default" : "ghost"} 
                  size="sm"
                  className="flex items-center gap-2"
                >
                  <MonitorSpeaker className="h-4 w-4" />
                  Channels
                </Button>
              </Link>
              
              <Link href="/topics">
                <Button 
                  variant={location === "/topics" ? "default" : "ghost"} 
                  size="sm"
                  className="flex items-center gap-2"
                >
                  <Tag className="h-4 w-4" />
                  Topics
                </Button>
              </Link>
            </nav>

            {/* Contextual Breadcrumbs - Secondary */}
            {breadcrumbs.length > 0 && (
              <div className="hidden lg:flex items-center space-x-1 text-sm text-muted-foreground">
                <ChevronRight className="h-4 w-4" />
                {breadcrumbs.map((breadcrumb, index) => (
                  <div key={index} className="flex items-center">
                    {index > 0 && <ChevronRight className="h-4 w-4 mx-1" />}
                    <span className={cn(breadcrumb.isActive ? "text-foreground font-medium" : "text-muted-foreground")}>
                      {breadcrumb.label}
                    </span>
                  </div>
                ))}
              </div>
            )}
          </div>

          {/* User Account & Mobile Menu */}
          <div className="flex items-center space-x-3">
            {/* Real-time Activity Indicator */}
            {recentActivity && recentActivity.length > 0 && (
              <div className="hidden lg:flex items-center space-x-2 px-3 py-1 rounded-lg bg-green-50 dark:bg-green-950/20 border border-green-200 dark:border-green-800">
                <div className="h-2 w-2 bg-green-500 rounded-full animate-pulse" />
                <span className="text-sm text-green-700 dark:text-green-300">
                  {recentActivity.length} new summaries
                </span>
              </div>
            )}

            {/* Quick Actions for Desktop */}
            <div className="hidden md:flex items-center space-x-2">
              {contextualActions.slice(0, 2).map((action) => (
                <Button
                  key={action.id}
                  variant={action.variant === "primary" ? "default" : "ghost"}
                  size="sm"
                  onClick={action.onClick}
                  className="relative"
                >
                  <action.icon className="h-4 w-4 mr-2" />
                  <span className="hidden lg:inline">{action.label}</span>
                  {action.badge && action.badge > 0 && (
                    <Badge 
                      variant="secondary" 
                      className="ml-1 h-4 w-4 p-0 text-xs flex items-center justify-center"
                    >
                      {action.badge}
                    </Badge>
                  )}
                </Button>
              ))}
            </div>

            {/* User Account Dropdown */}
            {user && (
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="ghost" className="flex items-center space-x-2 px-2">
                    <Avatar className="w-8 h-8">
                      <AvatarImage src={user.profileImageUrl || ""} />
                      <AvatarFallback className="text-xs">
                        {user.firstName?.[0]}{user.lastName?.[0]}
                      </AvatarFallback>
                    </Avatar>
                    <div className="hidden lg:block text-left">
                      <div className="text-sm font-medium">{user.firstName}</div>
                      <div className="text-xs text-muted-foreground capitalize">
                        {user.subscriptionType} Plan
                      </div>
                    </div>
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end" className="w-56">
                  <div className="px-2 py-2">
                    <div className="text-sm font-medium">{user.firstName} {user.lastName}</div>
                    <div className="text-xs text-muted-foreground">{user.email}</div>
                  </div>
                  
                  <DropdownMenuSeparator />
                  
                  <DropdownMenuItem>
                    <BarChart3 className="mr-2 h-4 w-4" />
                    <span>Usage: {user.usageCount}/{user.subscriptionType === 'free' ? 5 : 100}</span>
                  </DropdownMenuItem>
                  
                  <DropdownMenuItem onClick={() => window.location.href = "/subscribe"}>
                    <CreditCard className="mr-2 h-4 w-4" />
                    <span>Billing & Plans</span>
                  </DropdownMenuItem>
                  
                  <DropdownMenuSeparator />
                  
                  <DropdownMenuItem onClick={() => window.location.href = "/settings"}>
                    <Settings className="mr-2 h-4 w-4" />
                    <span>Settings</span>
                  </DropdownMenuItem>
                  
                  <DropdownMenuItem>
                    <User className="mr-2 h-4 w-4" />
                    <span>Profile</span>
                  </DropdownMenuItem>
                  
                  <DropdownMenuSeparator />
                  
                  <DropdownMenuItem 
                    onClick={() => window.location.href = "/api/logout"} 
                    className="text-red-600"
                  >
                    <LogOut className="mr-2 h-4 w-4" />
                    <span>Sign Out</span>
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            )}

            {/* Mobile Menu */}
            <Sheet open={mobileMenuOpen} onOpenChange={setMobileMenuOpen}>
              <SheetTrigger asChild>
                <Button variant="ghost" size="sm" className="md:hidden">
                  <Menu className="h-5 w-5" />
                </Button>
              </SheetTrigger>
              <SheetContent side="right" className="w-80">
                <SheetHeader>
                  <SheetTitle>Navigation</SheetTitle>
                </SheetHeader>
                
                <div className="mt-6 space-y-4">
                  {/* Mobile Navigation Links */}
                  <div className="space-y-2">
                    <Link href="/">
                      <Button 
                        variant={location === "/" ? "default" : "ghost"} 
                        className="w-full justify-start"
                        onClick={() => setMobileMenuOpen(false)}
                      >
                        <Plus className="h-4 w-4 mr-3" />
                        Create Summary
                      </Button>
                    </Link>
                    
                    <Link href="/library">
                      <Button 
                        variant={location === "/library" ? "default" : "ghost"} 
                        className="w-full justify-start"
                        onClick={() => setMobileMenuOpen(false)}
                      >
                        <Library className="h-4 w-4 mr-3" />
                        Library
                      </Button>
                    </Link>
                    
                    <Link href="/channels">
                      <Button 
                        variant={location === "/channels" ? "default" : "ghost"} 
                        className="w-full justify-start"
                        onClick={() => setMobileMenuOpen(false)}
                      >
                        <MonitorSpeaker className="h-4 w-4 mr-3" />
                        Channels
                      </Button>
                    </Link>
                    
                    <Link href="/topics">
                      <Button 
                        variant={location === "/topics" ? "default" : "ghost"} 
                        className="w-full justify-start"
                        onClick={() => setMobileMenuOpen(false)}
                      >
                        <Tag className="h-4 w-4 mr-3" />
                        Topics
                      </Button>
                    </Link>
                  </div>

                  {/* Activity Status */}
                  {recentActivity && recentActivity.length > 0 && (
                    <Card>
                      <CardContent className="p-3">
                        <div className="flex items-center gap-2">
                          <div className="h-2 w-2 bg-green-500 rounded-full animate-pulse" />
                          <span className="text-sm font-medium">
                            {recentActivity.length} new summaries
                          </span>
                        </div>
                      </CardContent>
                    </Card>
                  )}

                  {/* Quick Actions for Mobile */}
                  <div className="space-y-2">
                    {contextualActions.map((action) => (
                      <Button
                        key={action.id}
                        variant="outline"
                        className="w-full justify-start"
                        onClick={() => {
                          action.onClick();
                          setMobileMenuOpen(false);
                        }}
                      >
                        <action.icon className="h-4 w-4 mr-3" />
                        {action.label}
                        {action.badge && action.badge > 0 && (
                          <Badge variant="secondary" className="ml-auto">
                            {action.badge}
                          </Badge>
                        )}
                      </Button>
                    ))}
                  </div>
                </div>
              </SheetContent>
            </Sheet>
          </div>
        </div>
      </header>

      {/* Page Header with Context */}
      <div className="border-b border-border/40 bg-muted/30">
        <div className="container max-w-screen-2xl px-4 md:px-6 py-4">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-2xl font-bold tracking-tight">{pageTitle}</h1>
              <p className="text-muted-foreground mt-1">
                {location === "/" && "Transform YouTube videos into actionable insights"}
                {location === "/library" && "Browse and search your video summaries"}
                {location === "/channels" && "Monitor channels for automatic summaries"}
                {location === "/topics" && "Organize content with topic-based collections"}
              </p>
            </div>

            {/* Page-specific Status Indicators */}
            <div className="flex items-center space-x-4">
              {location === "/channels" && pendingJobs && (
                <Card className="p-3">
                  <div className="flex items-center gap-2">
                    <Clock className="h-4 w-4 text-blue-500" />
                    <span className="text-sm font-medium">{pendingJobs.length} processing</span>
                  </div>
                </Card>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Global Quick Create Bar (when enabled) */}
      {showGlobalCreate && showQuickCreate && (
        <div className="border-b border-border/40 bg-blue-50 dark:bg-blue-950/20">
          <div className="container max-w-screen-2xl px-4 md:px-6 py-3">
            <div className="flex items-center gap-3">
              <Zap className="h-5 w-5 text-blue-600" />
              <Input
                placeholder="Paste YouTube URL to create summary..."
                value={globalCreateUrl}
                onChange={(e) => setGlobalCreateUrl(e.target.value)}
                className="flex-1 max-w-md"
              />
              <Button size="sm">
                Create Summary
              </Button>
              <Button 
                variant="ghost" 
                size="sm"
                onClick={() => setShowQuickCreate(false)}
              >
                Cancel
              </Button>
            </div>
          </div>
        </div>
      )}

      {/* Main Content with Adaptive Density */}
      <main className={cn("container max-w-screen-2xl", getDensityClasses())}>
        {children}
      </main>
    </div>
  );
}

export default EnhancedLayout;