// Test adding Starter Story channel directly to database
import { Pool } from '@neondatabase/serverless';
import ws from "ws";

const pool = new Pool({ 
  connectionString: process.env.DATABASE_URL,
  webSocketConstructor: ws
});

async function testAddChannel() {
  const client = await pool.connect();
  
  try {
    // Test user ID from logs (<PERSON>)
    const userId = "40640447";
    
    // Channel data from YouTube API test
    const channelData = {
      userId: userId,
      channelId: "UChhw6DlKKTQ9mYSpTfXUYqA",
      channelName: "Starter Story",
      channelHandle: "@starterstory",
      channelThumbnail: "https://yt3.ggpht.com/default/channel/thumbnail.jpg",
      subscriberCount: "556K",
      videoCount: 201,
      isActive: true
    };
    
    console.log('Adding channel to database...');
    
    const result = await client.query(`
      INSERT INTO channels (
        user_id, channel_id, channel_name, channel_handle, 
        channel_thumbnail, subscriber_count, video_count, is_active
      ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
      RETURNING *
    `, [
      channelData.userId,
      channelData.channelId, 
      channelData.channelName,
      channelData.channelHandle,
      channelData.channelThumbnail,
      channelData.subscriberCount,
      channelData.videoCount,
      channelData.isActive
    ]);
    
    console.log('Channel added successfully:', result.rows[0]);
    
    // Verify the channel was added
    const verification = await client.query(
      'SELECT * FROM channels WHERE user_id = $1',
      [userId]
    );
    
    console.log('User channels:', verification.rows);
    
  } catch (error) {
    console.error('Error adding channel:', error.message);
  } finally {
    client.release();
  }
}

testAddChannel();