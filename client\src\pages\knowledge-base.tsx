
import { useState } from "react";
import { useAuth } from "@/hooks/useAuth";
import { useQuery, useMutation } from "@tanstack/react-query";
import { apiRequest } from "@/lib/queryClient";
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Badge } from "@/components/ui/badge";
import { Search, Brain, TrendingUp, Network } from "lucide-react";

export default function KnowledgeBase() {
  const { user } = useAuth();
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedSummaries, setSelectedSummaries] = useState<number[]>([]);
  const [synthesisQuery, setSynthesisQuery] = useState("");

  // Check if user has super-premium access
  if (user?.subscriptionType !== 'super-premium') {
    return (
      <div className="max-w-4xl mx-auto px-4 py-8">
        <Card>
          <CardContent className="p-8 text-center">
            <h1 className="text-2xl font-bold mb-4">Knowledge Base Access</h1>
            <p className="text-slate-600 mb-6">
              This feature is available for Super-Premium subscribers only.
            </p>
            <Button onClick={() => window.location.href = '/subscribe'}>
              Upgrade to Super-Premium
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  // Search summaries mutation
  const searchMutation = useMutation({
    mutationFn: async (query: string) => {
      const response = await apiRequest("POST", "/api/mcp/search-summaries", { 
        query,
        filters: {} 
      });
      return await response.json();
    },
  });

  // Knowledge synthesis mutation
  const synthesisMutation = useMutation({
    mutationFn: async (data: { query: string; summaryIds: number[] }) => {
      const response = await apiRequest("POST", "/api/mcp/synthesize-knowledge", data);
      return await response.json();
    },
  });

  // Fetch topics
  const { data: topics = [] } = useQuery({
    queryKey: ["/api/mcp/topics"],
    queryFn: async () => {
      const response = await apiRequest("GET", "/api/mcp/topics");
      return await response.json();
    },
  });

  const handleSearch = () => {
    if (searchQuery.trim()) {
      searchMutation.mutate(searchQuery);
    }
  };

  const handleSynthesize = () => {
    if (synthesisQuery.trim() && selectedSummaries.length > 0) {
      synthesisMutation.mutate({
        query: synthesisQuery,
        summaryIds: selectedSummaries
      });
    }
  };

  return (
    <div className="max-w-7xl mx-auto px-4 py-8">
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-secondary mb-2">Knowledge Base</h1>
        <p className="text-slate-600">
          Search across your video summaries and synthesize knowledge with AI-powered insights.
        </p>
      </div>

      {/* Search Section */}
      <Card className="mb-8">
        <CardContent className="p-6">
          <h2 className="text-xl font-semibold mb-4 flex items-center">
            <Search className="w-5 h-5 mr-2" />
            Semantic Search
          </h2>
          <div className="flex space-x-4">
            <Input
              placeholder="Search across all your video summaries..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="flex-1"
            />
            <Button 
              onClick={handleSearch}
              disabled={searchMutation.isPending}
            >
              Search
            </Button>
          </div>
          
          {searchMutation.data && (
            <div className="mt-6">
              <h3 className="font-semibold mb-3">Search Results ({searchMutation.data.length})</h3>
              <div className="space-y-3">
                {searchMutation.data.map((summary: any) => (
                  <div 
                    key={summary.id}
                    className="p-4 border rounded-lg cursor-pointer hover:border-primary/30"
                    onClick={() => {
                      const isSelected = selectedSummaries.includes(summary.id);
                      setSelectedSummaries(prev => 
                        isSelected 
                          ? prev.filter(id => id !== summary.id)
                          : [...prev, summary.id]
                      );
                    }}
                  >
                    <div className="flex items-center justify-between">
                      <div className="flex-1">
                        <h4 className="font-medium">{summary.videoTitle}</h4>
                        <p className="text-sm text-slate-500">{summary.videoChannel}</p>
                      </div>
                      {selectedSummaries.includes(summary.id) && (
                        <Badge variant="secondary">Selected</Badge>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Knowledge Synthesis Section */}
      <Card className="mb-8">
        <CardContent className="p-6">
          <h2 className="text-xl font-semibold mb-4 flex items-center">
            <Brain className="w-5 h-5 mr-2" />
            Knowledge Synthesis
          </h2>
          <div className="space-y-4">
            <Textarea
              placeholder="What would you like to know across your selected summaries?"
              value={synthesisQuery}
              onChange={(e) => setSynthesisQuery(e.target.value)}
              className="min-h-[100px]"
            />
            <div className="flex items-center justify-between">
              <p className="text-sm text-slate-500">
                {selectedSummaries.length} summaries selected
              </p>
              <Button 
                onClick={handleSynthesize}
                disabled={synthesisMutation.isPending || selectedSummaries.length === 0}
              >
                Synthesize Knowledge
              </Button>
            </div>
          </div>
          
          {synthesisMutation.data && (
            <div className="mt-6 p-4 bg-blue-50 rounded-lg">
              <h3 className="font-semibold mb-3">Knowledge Synthesis Results</h3>
              <div className="space-y-3">
                <p><strong>Query:</strong> {synthesisMutation.data.query}</p>
                <p><strong>Summaries Analyzed:</strong> {synthesisMutation.data.summaryCount}</p>
                <div>
                  <strong>Related Summaries:</strong>
                  <div className="mt-2 space-y-2">
                    {synthesisMutation.data.summaries.map((s: any) => (
                      <div key={s.id} className="p-2 bg-white rounded border">
                        <p className="font-medium text-sm">{s.title}</p>
                        <p className="text-xs text-slate-500">{s.channel}</p>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Topics Explorer */}
      <Card>
        <CardContent className="p-6">
          <h2 className="text-xl font-semibold mb-4 flex items-center">
            <Network className="w-5 h-5 mr-2" />
            Topics Explorer
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            {topics.slice(0, 9).map((topic: any) => (
              <div key={topic.id} className="p-4 border rounded-lg">
                <h3 className="font-medium">{topic.name}</h3>
                <p className="text-sm text-slate-500 mt-1">{topic.description}</p>
                <p className="text-xs text-slate-400 mt-2">
                  {topic.summaryCount} summaries
                </p>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
