#!/bin/bash

# VideoSummarize AI - GitHub Deployment Script
# Automates pushing the complete project to GitHub

set -e  # Exit on any error

echo "🚀 VideoSummarize AI - GitHub Deployment Script"
echo "================================================"

# Configuration
REPO_NAME="video-summarizer"
GITHUB_USERNAME=""  # You'll need to set this
COMMIT_MESSAGE="VideoSummarize AI Platform Update - $(date '+%Y-%m-%d %H:%M:%S')"

# Function to get GitHub username
get_github_username() {
    if [ -z "$GITHUB_USERNAME" ]; then
        echo "📝 Please enter your GitHub username:"
        read -r GITHUB_USERNAME
        
        if [ -z "$GITHUB_USERNAME" ]; then
            echo "❌ GitHub username is required"
            exit 1
        fi
    fi
}

# Function to clean Git locks
clean_git_locks() {
    echo "🧹 Cleaning Git lock files..."
    rm -f .git/config.lock .git/index.lock 2>/dev/null || true
}

# Function to check if remote exists
check_remote() {
    if git remote get-url origin &>/dev/null; then
        echo "✅ Git remote already configured"
        return 0
    else
        return 1
    fi
}

# Function to add GitHub remote
add_remote() {
    echo "🔗 Adding GitHub remote..."
    git remote add origin "https://github.com/${GITHUB_USERNAME}/${REPO_NAME}.git"
}

# Function to update remote URL
update_remote() {
    echo "🔄 Updating remote URL..."
    git remote set-url origin "https://github.com/${GITHUB_USERNAME}/${REPO_NAME}.git"
}

# Function to stage and commit changes
commit_changes() {
    echo "📦 Staging all changes..."
    git add .
    
    if git diff --staged --quiet; then
        echo "ℹ️  No changes to commit"
        return 1
    else
        echo "💾 Committing changes..."
        git commit -m "$COMMIT_MESSAGE

Features:
- AI-powered YouTube video summarization with OpenAI GPT-4o
- 8-section structured analysis with clickable timestamps
- Real YouTube API integration and transcript extraction
- Export functionality (PDF, CSV, Markdown, Text)
- User authentication with Replit Auth
- Feedback system and subscription management
- Responsive UI with dark/light mode
- PostgreSQL database with Drizzle ORM
- Full TypeScript implementation"
        return 0
    fi
}

# Function to push to GitHub
push_to_github() {
    echo "🚀 Pushing to GitHub..."
    
    # Store the output for verification
    PUSH_OUTPUT=$(mktemp)
    
    # Try to push, handle different scenarios
    if git push -u origin main 2>&1 | tee "$PUSH_OUTPUT"; then
        echo "✅ Successfully pushed to main branch"
        verify_push_success "$PUSH_OUTPUT"
    elif git push -u origin master 2>&1 | tee "$PUSH_OUTPUT"; then
        echo "✅ Successfully pushed to master branch"
        verify_push_success "$PUSH_OUTPUT"
    else
        echo "📋 First time push - creating main branch..."
        git branch -M main
        if git push -u origin main 2>&1 | tee "$PUSH_OUTPUT"; then
            echo "✅ Successfully created and pushed main branch"
            verify_push_success "$PUSH_OUTPUT"
        else
            echo "❌ Push failed. Check the output above for details."
            cat "$PUSH_OUTPUT"
            rm -f "$PUSH_OUTPUT"
            exit 1
        fi
    fi
    
    rm -f "$PUSH_OUTPUT"
}

# Function to verify push was successful
verify_push_success() {
    local output_file="$1"
    
    if grep -q "github.com" "$output_file"; then
        echo "✅ Confirmed: Push sent to GitHub"
        
        # Extract and display the repository URL
        if grep -q "remote:" "$output_file"; then
            echo "📍 GitHub repository updated:"
            grep "remote:" "$output_file" | head -1
        fi
        
        # Show commit hash if available
        COMMIT_HASH=$(git rev-parse HEAD 2>/dev/null | cut -c1-8)
        if [ -n "$COMMIT_HASH" ]; then
            echo "📝 Latest commit: $COMMIT_HASH"
            echo "🔗 View on GitHub: https://github.com/${GITHUB_USERNAME}/${REPO_NAME}/commit/$COMMIT_HASH"
        fi
    else
        echo "⚠️  Push completed but GitHub confirmation unclear"
    fi
}

# Main execution
main() {
    echo "🔍 Checking Git status..."
    
    # Initialize git if not already done
    if [ ! -d ".git" ]; then
        echo "📁 Initializing Git repository..."
        git init
        git branch -M main
    fi
    
    # Clean any lock files
    clean_git_locks
    
    # Get GitHub username
    get_github_username
    
    # Configure Git user if not set
    if [ -z "$(git config user.name)" ]; then
        echo "👤 Setting up Git user configuration..."
        echo "Enter your full name:"
        read -r user_name
        git config user.name "$user_name"
    fi
    
    if [ -z "$(git config user.email)" ]; then
        echo "📧 Enter your email address:"
        read -r user_email
        git config user.email "$user_email"
    fi
    
    # Handle remote configuration
    if check_remote; then
        update_remote
    else
        add_remote
    fi
    
    # Commit changes
    if commit_changes; then
        # Push to GitHub
        push_to_github
        
        echo ""
        echo "🎉 Deployment completed successfully!"
        echo "📍 Repository URL: https://github.com/${GITHUB_USERNAME}/${REPO_NAME}"
        echo ""
        echo "Next steps:"
        echo "1. Visit your GitHub repository"
        echo "2. Set up environment variables in your deployment platform"
        echo "3. Configure database and API keys"
        echo ""
    else
        echo "ℹ️  No new changes to deploy"
    fi
}

# Error handling
trap 'echo "❌ An error occurred during deployment. Please check the output above."' ERR

# Run main function
main

echo "✨ Script completed!"