#!/bin/bash

# Quick GitHub sync script for VideoSummarize AI
# Usage: ./sync-github.sh "Your commit message"

COMMIT_MSG="${1:-Update VideoSummarize AI - $(date '+%Y-%m-%d %H:%M:%S')}"

echo "Syncing VideoSummarize AI to GitHub..."

# Clean any Git locks
rm -f .git/config.lock .git/index.lock 2>/dev/null || true

# Stage all changes
git add .

# Check if there are changes to commit
if git diff --staged --quiet; then
    echo "No changes to commit"
    exit 0
fi

# Commit changes
git commit -m "$COMMIT_MSG"

# Push to GitHub
if git push; then
    echo "Successfully synced to GitHub"
else
    echo "Push failed - you may need to run the full deploy script first"
    echo "Try: ./deploy-to-github.sh"
fi