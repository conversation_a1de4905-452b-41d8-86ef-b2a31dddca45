import {
  users,
  summaries,
  topics,
  entities,
  summaryTopics,
  mcpUsage,
  type User,
  type UpsertUser,
  type InsertSummary,
  type Summary,
  type Topic,
  type Entity,
  type SummaryTopic,
  type McpUsage,
} from "@shared/schema";
import { db } from "./db";
import { eq, desc, and, like, sql } from "drizzle-orm";

// Interface for storage operations
export interface IStorage {
  // User operations
  getUser(id: string): Promise<User | undefined>;
  upsertUser(user: UpsertUser): Promise<User>;
  incrementUserUsage(userId: string): Promise<void>;
  
  // Summary operations
  getUserSummaries(userId: string): Promise<Summary[]>;
  getSummaryByVideoId(userId: string, videoId: string): Promise<Summary | undefined>;
  createSummary(summary: InsertSummary): Promise<Summary>;
  getSummary(id: number, userId: string): Promise<Summary | undefined>;
  
  // Vector search and knowledge base operations
  searchSummariesByText(userId: string, query: string, filters?: {
    topics?: string[];
    dateRange?: [Date, Date];
    channels?: string[];
    difficulty?: string;
  }): Promise<Summary[]>;
  getTopics(): Promise<Topic[]>;
  getTopicsByName(name: string): Promise<Topic[]>;
  getRelatedTopics(topicId: number, depth?: number): Promise<Topic[]>;
  createTopic(name: string, description?: string, parentTopicId?: number): Promise<Topic>;
  linkSummaryToTopic(summaryId: number, topicId: number, relevanceScore: number): Promise<void>;
  getTopicTrends(topicName: string, timeframe: string): Promise<any[]>;
  recordMcpUsage(userId: string, queryType: string, tokensUsed: number, vectorSearches: number): Promise<void>;
}

export class DatabaseStorage implements IStorage {
  // User operations
  async getUser(id: string): Promise<User | undefined> {
    const [user] = await db.select().from(users).where(eq(users.id, id));
    return user;
  }

  async upsertUser(userData: UpsertUser): Promise<User> {
    const [user] = await db
      .insert(users)
      .values(userData)
      .onConflictDoUpdate({
        target: users.id,
        set: {
          ...userData,
          updatedAt: new Date(),
        },
      })
      .returning();
    return user;
  }

  async incrementUserUsage(userId: string): Promise<void> {
    // Get current usage count and increment by 1
    const user = await this.getUser(userId);
    const currentCount = user?.usageCount ?? 0;
    
    await db
      .update(users)
      .set({ 
        usageCount: currentCount + 1
      })
      .where(eq(users.id, userId));
  }

  // Summary operations
  async getUserSummaries(userId: string): Promise<Summary[]> {
    return await db
      .select()
      .from(summaries)
      .where(eq(summaries.userId, userId))
      .orderBy(desc(summaries.createdAt));
  }

  async getSummaryByVideoId(userId: string, videoId: string): Promise<Summary | undefined> {
    const [summary] = await db
      .select()
      .from(summaries)
      .where(and(eq(summaries.userId, userId), eq(summaries.videoId, videoId)));
    return summary;
  }

  async createSummary(summary: InsertSummary): Promise<Summary> {
    const [newSummary] = await db
      .insert(summaries)
      .values(summary)
      .returning();
    return newSummary;
  }

  async getSummary(id: number, userId: string): Promise<Summary | undefined> {
    const [summary] = await db
      .select()
      .from(summaries)
      .where(and(eq(summaries.id, id), eq(summaries.userId, userId)));
    return summary;
  }

  // Vector search and knowledge base methods
  async searchSummariesByText(userId: string, query: string, filters?: {
    topics?: string[];
    dateRange?: [Date, Date];
    channels?: string[];
    difficulty?: string;
  }): Promise<Summary[]> {
    let queryBuilder = db
      .select()
      .from(summaries)
      .where(and(
        eq(summaries.userId, userId),
        // Simple text search for now - can be enhanced with actual vector similarity
        sql`(${summaries.summaryContent} ILIKE ${`%${query}%`} OR ${summaries.videoTitle} ILIKE ${`%${query}%`})`
      ));

    if (filters?.channels?.length) {
      queryBuilder = queryBuilder.where(
        and(
          eq(summaries.userId, userId),
          sql`${summaries.videoChannel} = ANY(${filters.channels})`
        )
      );
    }

    if (filters?.difficulty) {
      queryBuilder = queryBuilder.where(
        and(
          eq(summaries.userId, userId),
          eq(summaries.difficulty, filters.difficulty)
        )
      );
    }

    return await queryBuilder.orderBy(desc(summaries.createdAt));
  }

  async getTopics(): Promise<Topic[]> {
    return await db
      .select()
      .from(topics)
      .orderBy(desc(topics.summaryCount));
  }

  async getTopicsByName(name: string): Promise<Topic[]> {
    return await db
      .select()
      .from(topics)
      .where(like(topics.name, `%${name}%`));
  }

  async getRelatedTopics(topicId: number, depth: number = 1): Promise<Topic[]> {
    // Simple implementation - can be enhanced with recursive queries
    return await db
      .select()
      .from(topics)
      .where(eq(topics.parentTopicId, topicId));
  }

  async createTopic(name: string, description?: string, parentTopicId?: number): Promise<Topic> {
    const [topic] = await db
      .insert(topics)
      .values({
        name,
        description,
        parentTopicId,
      })
      .returning();
    return topic;
  }

  async linkSummaryToTopic(summaryId: number, topicId: number, relevanceScore: number): Promise<void> {
    await db
      .insert(summaryTopics)
      .values({
        summaryId,
        topicId,
        relevanceScore: relevanceScore.toString(),
      });

    // Increment topic summary count
    await db
      .update(topics)
      .set({
        summaryCount: sql`${topics.summaryCount} + 1`,
      })
      .where(eq(topics.id, topicId));
  }

  async getTopicTrends(topicName: string, timeframe: string): Promise<any[]> {
    // Placeholder for trend analysis - can be enhanced with actual time-series analysis
    const timeConstraint = timeframe === 'week' ? 
      sql`${summaries.createdAt} >= NOW() - INTERVAL '7 days'` :
      timeframe === 'month' ?
      sql`${summaries.createdAt} >= NOW() - INTERVAL '30 days'` :
      sql`${summaries.createdAt} >= NOW() - INTERVAL '1 year'`;

    return await db
      .select({
        date: sql`DATE(${summaries.createdAt})`,
        count: sql`COUNT(*)`,
      })
      .from(summaries)
      .innerJoin(summaryTopics, eq(summaries.id, summaryTopics.summaryId))
      .innerJoin(topics, eq(summaryTopics.topicId, topics.id))
      .where(and(
        eq(topics.name, topicName),
        timeConstraint
      ))
      .groupBy(sql`DATE(${summaries.createdAt})`)
      .orderBy(sql`DATE(${summaries.createdAt})`);
  }

  async recordMcpUsage(userId: string, queryType: string, tokensUsed: number, vectorSearches: number): Promise<void> {
    await db
      .insert(mcpUsage)
      .values({
        userId,
        queryType,
        tokensUsed,
        vectorSearches,
      });
  }
}

export const storage = new DatabaseStorage();