import type { Express } from "express";
import { createServer, type Server } from "http";
import { storage } from "./storage-simple";
import { setupAuth, isAuthenticated } from "./replitAuth";
import { insertSummarySchema } from "@shared/schema";
import { z } from "zod";
import Stripe from "stripe";
import { generateRSSFeed, createAllLibrary<PERSON>eed, createChannelFeed, createTopicFeed } from "./rss-generator";

// Extract video ID from YouTube URL
function extractVideoId(url: string): string | null {
  const regex = /(?:youtube\.com\/(?:[^\/]+\/.+\/|(?:v|e(?:mbed)?)\/|.*[?&]v=)|youtu\.be\/)([^"&?\/\s]{11})/;
  const match = url.match(regex);
  return match ? match[1] : null;
}

// Get real video metadata using YouTube API
async function getVideoMetadata(videoId: string) {
  const apiKey = process.env.YOUTUBE_API_KEY;
  if (!apiKey) {
    throw new Error("YouTube API key not configured");
  }

  const url = `https://www.googleapis.com/youtube/v3/videos?id=${videoId}&key=${apiKey}&part=snippet,statistics,contentDetails`;

  try {
    const response = await fetch(url);
    const data = await response.json();

    if (!data.items || data.items.length === 0) {
      throw new Error("Video not found");
    }

    const video = data.items[0];
    const snippet = video.snippet;
    const statistics = video.statistics;
    const contentDetails = video.contentDetails;

    // Parse duration from ISO 8601 format (PT4M13S) to readable format
    const duration = parseDuration(contentDetails.duration);

    // Format view count
    const viewCount = parseInt(statistics.viewCount);
    const views = formatViewCount(viewCount);

    return {
      title: snippet.title,
      channel: snippet.channelTitle,
      thumbnail: snippet.thumbnails.maxresdefault?.url || snippet.thumbnails.high?.url || snippet.thumbnails.default?.url,
      duration,
      views,
      description: snippet.description,
      publishedAt: snippet.publishedAt,
    };
  } catch (error) {
    console.error("Error fetching video metadata:", error);
    throw new Error("Failed to fetch video information");
  }
}

// Helper function to parse ISO 8601 duration
function parseDuration(duration: string): string {
  const match = duration.match(/PT(?:(\d+)H)?(?:(\d+)M)?(?:(\d+)S)?/);
  if (!match) return "0:00";

  const hours = parseInt(match[1] || "0");
  const minutes = parseInt(match[2] || "0");
  const seconds = parseInt(match[3] || "0");

  if (hours > 0) {
    return `${hours}:${minutes.toString().padStart(2, "0")}:${seconds.toString().padStart(2, "0")}`;
  } else {
    return `${minutes}:${seconds.toString().padStart(2, "0")}`;
  }
}

// Helper function to format view count
function formatViewCount(count: number): string {
  if (count >= 1000000) {
    return `${(count / 1000000).toFixed(1)}M views`;
  } else if (count >= 1000) {
    return `${(count / 1000).toFixed(1)}K views`;
  } else {
    return `${count} views`;
  }
}

async function getChannelMetadata(channelId: string) {
  const response = await fetch(
    `https://www.googleapis.com/youtube/v3/channels?id=${channelId}&key=${process.env.YOUTUBE_API_KEY}&part=snippet,statistics,brandingSettings`
  );
  
  if (!response.ok) {
    throw new Error(`YouTube API error: ${response.status}`);
  }
  
  const data = await response.json();
  if (!data.items || data.items.length === 0) {
    throw new Error('Channel not found');
  }
  
  const channel = data.items[0];
  return {
    title: channel.snippet.title,
    customUrl: channel.snippet.customUrl,
    thumbnail: channel.snippet.thumbnails.default?.url,
    subscriberCount: formatSubscriberCount(parseInt(channel.statistics.subscriberCount || '0')),
    videoCount: parseInt(channel.statistics.videoCount || '0'),
    description: channel.snippet.description || ''
  };
}

async function extractChannelIdFromUrl(url: string): Promise<string | null> {
  // Handle direct channel ID
  if (url.startsWith('UC') && url.length === 24) {
    return url;
  }
  
  // Handle @handle format
  const handleMatch = url.match(/(?:youtube\.com\/@|^@)([a-zA-Z0-9_-]+)/);
  if (handleMatch) {
    const handle = handleMatch[1];
    return await resolveHandleToChannelId(handle);
  }
  
  // Handle other formats
  const patterns = [
    /youtube\.com\/channel\/([a-zA-Z0-9_-]+)/,
    /youtube\.com\/c\/([a-zA-Z0-9_-]+)/,
    /youtube\.com\/user\/([a-zA-Z0-9_-]+)/
  ];
  
  for (const pattern of patterns) {
    const match = url.match(pattern);
    if (match) {
      // For c/ and user/ formats, we need to resolve to channel ID
      return await resolveHandleToChannelId(match[1]);
    }
  }
  return null;
}

async function resolveHandleToChannelId(handle: string): Promise<string | null> {
  try {
    console.log(`Resolving handle: ${handle}`);
    
    // Try the forHandle parameter first (more direct for @handles)
    const handleUrl = `https://www.googleapis.com/youtube/v3/channels?key=${process.env.YOUTUBE_API_KEY}&forHandle=${handle}&part=id`;
    console.log(`Trying handle URL: ${handleUrl}`);
    
    const handleResponse = await fetch(handleUrl);
    console.log(`Handle response status: ${handleResponse.status}`);
    
    if (handleResponse.ok) {
      const handleData = await handleResponse.json();
      console.log(`Handle response data:`, handleData);
      if (handleData.items && handleData.items.length > 0) {
        const channelId = handleData.items[0].id;
        console.log(`Found channel ID via handle: ${channelId}`);
        return channelId;
      }
    }
    
    // Fallback: Use search API
    const searchUrl = `https://www.googleapis.com/youtube/v3/search?key=${process.env.YOUTUBE_API_KEY}&q=${encodeURIComponent(handle)}&type=channel&part=id&maxResults=1`;
    console.log(`Trying search URL: ${searchUrl}`);
    
    const searchResponse = await fetch(searchUrl);
    console.log(`Search response status: ${searchResponse.status}`);
    
    if (!searchResponse.ok) {
      const errorText = await searchResponse.text();
      console.error(`YouTube API search error: ${searchResponse.status} - ${errorText}`);
      throw new Error(`YouTube API error: ${searchResponse.status}`);
    }
    
    const searchData = await searchResponse.json();
    console.log(`Search response data:`, searchData);
    
    if (searchData.items && searchData.items.length > 0) {
      const channelId = searchData.items[0].id.channelId;
      console.log(`Found channel ID via search: ${channelId}`);
      return channelId;
    }
    
    console.log(`No channel found for handle: ${handle}`);
    return null;
  } catch (error) {
    console.error(`Error resolving handle ${handle}:`, error);
    return null;
  }
}

function formatSubscriberCount(count: number): string {
  if (count >= 1000000) {
    return Math.floor(count / 100000) / 10 + 'M';
  } else if (count >= 1000) {
    return Math.floor(count / 100) / 10 + 'K';
  }
  return count.toString();
}

// Get video transcript and generate AI summary
async function generateSummary(videoId: string, videoTitle: string, description: string): Promise<string> {
  const openaiApiKey = process.env.OPENAI_API_KEY;
  if (!openaiApiKey) {
    throw new Error("OpenAI API key not configured");
  }

  try {
    // Get transcript from YouTube
    const transcript = await getVideoTranscript(videoId);

    // Use OpenAI to generate detailed summary
    const prompt = `
    Analyze this YouTube video and create a comprehensive summary in HTML format.

    Video Title: ${videoTitle}
    Description: ${description}
    Transcript: ${transcript}

    Create a detailed summary with these EXACT sections:

    1. **Title Explained** - One sentence about what the video title means, whether claims are likely true/false, and if title claims are backed up in the video content
    2. **Who?** - Very briefly list who is in the video (host, guests, speakers)
    3. **Key Takeaways** - List 3-5 main points from the video
    4. **Important Sections with Timestamps** - List 3-7 key moments with clickable timestamps that link to those exact spots in the video
    5. **Detailed Analysis** - Deep dive into the main concepts and methodology 
    6. **Tools or Services Mentioned** - List specific tools, products, or services discussed
    7. **Important Quotes** - Direct quotes or key statements from the video
    8. **Direct Implications** - Practical applications and what this means for viewers

    Format the response as clean HTML using:
    - <div class="bg-slate-50 p-4 rounded-lg mb-4"> for each section
    - <h4 class="font-semibold text-secondary mb-3"><i class="fas fa-icon mr-2"></i>Section Name:</h4> for headers
    - <ul class="space-y-2 text-slate-700"> for lists
    - <li>• content</li> for list items
    - <p class="text-slate-700 mb-2"> for paragraphs
    - For timestamps, use: <a href="https://youtu.be/${videoId}?t=XXs" target="_blank" class="text-blue-600 hover:text-blue-800 underline">MM:SS</a> (replace XX with seconds)

    Use these icons for sections:
    - fa-info-circle for Title Explained
    - fa-users for Who?
    - fa-lightbulb for Key Takeaways
    - fa-clock for Important Sections
    - fa-search for Detailed Analysis  
    - fa-tools for Tools/Services
    - fa-quote-left for Important Quotes
    - fa-arrow-right for Direct Implications

    Make it specific to the actual video content, not generic. Include all 8 sections even if some are brief.
    `;

    const response = await fetch("https://api.openai.com/v1/chat/completions", {
      method: "POST",
      headers: {
        "Authorization": `Bearer ${openaiApiKey}`,
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        model: "gpt-4o-mini", // More capable and cost-effective than GPT-3.5
        messages: [
          {
            role: "system",
            content: `You are an expert video content analyzer specializing in creating comprehensive, actionable summaries. 

            Your summaries should:
            - Extract the most valuable and actionable insights
            - Identify key timestamps and moments
            - Highlight practical takeaways and implementable advice
            - Maintain the creator's tone and expertise level
            - Use clear, engaging HTML formatting
            - Focus on what viewers can learn and apply`
          },
          {
            role: "user",
            content: prompt
          }
        ],
        max_tokens: 3000, // Increased for more detailed summaries
        temperature: 0.2, // Lower for more consistent, focused output
      }),
    });

    const data = await response.json();

    if (!response.ok) {
      throw new Error(`OpenAI API error: ${data.error?.message || 'Unknown error'}`);
    }

    let content = data.choices[0].message.content;
    
    // Clean up any markdown code block markers
    content = content.replace(/```html\n?/g, '').replace(/```\n?/g, '');
    
    return content;
  } catch (error) {
    console.error("Error generating AI summary:", error);
    throw new Error("Failed to generate video summary");
  }
}

async function generateSummaryFromContent(videoId: string, videoTitle: string, description: string, transcript: string): Promise<string> {
  const openaiApiKey = process.env.OPENAI_API_KEY;
  if (!openaiApiKey) {
    throw new Error("OpenAI API key not configured");
  }

  try {
    const hasTranscript = transcript && transcript.trim().length > 0;
    
    let prompt: string;
    if (hasTranscript) {
      prompt = `
      Analyze this YouTube video and create a comprehensive summary in HTML format.

      Video Title: ${videoTitle}
      Description: ${description}
      Transcript: ${transcript}

      Create a detailed summary with these EXACT sections:

      1. **Title Explained** - One sentence about what the video title means, whether claims are likely true/false, and if title claims are backed up in the video content
      2. **Who?** - Very briefly list who is in the video (host, guests, speakers)
      3. **Key Takeaways** - List 3-5 main points from the video
      4. **Important Sections with Timestamps** - List 3-7 key moments with clickable timestamps that link to those exact spots in the video
      5. **Detailed Analysis** - Deep dive into the main concepts and methodology 
      6. **Tools or Services Mentioned** - List specific tools, products, or services discussed
      7. **Important Quotes** - Direct quotes or key statements from the video
      8. **Direct Implications** - Practical applications and what this means for viewers

      Format the response as clean HTML using:
      - <div class="bg-slate-50 p-4 rounded-lg mb-4"> for each section
      - <h4 class="font-semibold text-secondary mb-3"><i class="fas fa-icon mr-2"></i>Section Name:</h4> for headers
      - <ul class="space-y-2 text-slate-700"> for lists
      - <li>• content</li> for list items
      - <p class="text-slate-700 mb-2"> for paragraphs
      - For timestamps, use: <a href="https://youtu.be/${videoId}?t=XXs" target="_blank" class="text-blue-600 hover:text-blue-800 underline">MM:SS</a> (replace XX with seconds)

      Use these icons for sections:
      - fa-info-circle for Title Explained
      - fa-users for Who?
      - fa-lightbulb for Key Takeaways
      - fa-clock for Important Sections
      - fa-search for Detailed Analysis  
      - fa-tools for Tools/Services
      - fa-quote-left for Important Quotes
      - fa-arrow-right for Direct Implications

      Make it specific to the actual video content, not generic. Include all 8 sections even if some are brief.
      `;
    } else {
      prompt = `
      Analyze this YouTube video based on title and description and create a comprehensive summary in HTML format.

      Video Title: ${videoTitle}
      Description: ${description}

      Since no transcript is available, create a summary based on the title and description with these sections:

      1. **Title Explained** - What the video title suggests and likely claims
      2. **Expected Content** - What viewers can likely expect based on title/description
      3. **Key Topics** - Main topics that would be covered based on the information
      4. **Target Audience** - Who this video is likely made for
      5. **Potential Value** - What viewers might learn or gain
      6. **Context Analysis** - Analysis of the video's place in its niche/topic

      Format the response as clean HTML using:
      - <div class="bg-slate-50 p-4 rounded-lg mb-4"> for each section
      - <h4 class="font-semibold text-secondary mb-3"><i class="fas fa-icon mr-2"></i>Section Name:</h4> for headers
      - <ul class="space-y-2 text-slate-700"> for lists
      - <li>• content</li> for list items
      - <p class="text-slate-700 mb-2"> for paragraphs

      Add a note at the end: <p class="text-amber-600 text-sm italic mt-4">Note: This summary is based on the video title and description as no transcript was available.</p>
      `;
    }

    const response = await fetch("https://api.openai.com/v1/chat/completions", {
      method: "POST",
      headers: {
        "Authorization": `Bearer ${openaiApiKey}`,
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        model: "gpt-4o-mini",
        messages: [
          {
            role: "system",
            content: `You are an expert video content analyzer specializing in creating comprehensive, actionable summaries. 

            Your summaries should:
            - Extract the most valuable and actionable insights
            - Identify key information and learning opportunities
            - Highlight practical takeaways
            - Use clear, engaging HTML formatting
            - Focus on what viewers can learn and apply`
          },
          {
            role: "user",
            content: prompt
          }
        ],
        max_tokens: hasTranscript ? 3000 : 1500,
        temperature: 0.2,
      }),
    });

    const data = await response.json();

    if (!response.ok) {
      throw new Error(`OpenAI API error: ${data.error?.message || 'Unknown error'}`);
    }

    let content = data.choices[0].message.content;
    
    // Clean up any markdown code block markers
    content = content.replace(/```html\n?/g, '').replace(/```\n?/g, '');
    
    return content;
  } catch (error) {
    console.error("Error generating summary from content:", error);
    throw new Error("Failed to generate video summary");
  }
}

// Clean up transcript formatting using AI
async function formatTranscript(rawTranscript: string): Promise<string> {
  if (!rawTranscript || rawTranscript.trim().length === 0) {
    return rawTranscript;
  }

  const openaiApiKey = process.env.OPENAI_API_KEY;
  if (!openaiApiKey) {
    console.log("OpenAI API key not available, returning raw transcript");
    return rawTranscript;
  }

  try {
    console.log(`Formatting transcript of ${rawTranscript.length} characters`);
    
    const prompt = `Format this YouTube video transcript into readable paragraphs. 
    
Rules:
- Break text into logical paragraphs based on topic changes or natural speaking pauses
- Fix obvious grammar issues and sentence structure
- Remove filler words (um, uh, you know) but keep the conversational tone
- Maintain all technical terms and specific information exactly as spoken
- Don't add or remove substantial content, just improve readability
- Keep paragraphs reasonably sized (3-6 sentences each)
- Preserve the speaker's natural style and voice

Raw transcript:
${rawTranscript}`;

    const response = await fetch("https://api.openai.com/v1/chat/completions", {
      method: "POST",
      headers: {
        "Authorization": `Bearer ${openaiApiKey}`,
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        model: "gpt-4o", // the newest OpenAI model is "gpt-4o" which was released May 13, 2024. do not change this unless explicitly requested by the user
        messages: [
          {
            role: "system",
            content: "You are an expert transcript formatter. Your job is to take raw YouTube transcripts and format them into readable, well-structured paragraphs while preserving the original content and speaker's voice. Focus on readability and natural flow."
          },
          {
            role: "user",
            content: prompt
          }
        ],
        max_tokens: 4000,
        temperature: 0.1, // Low temperature for consistent formatting
      }),
    });

    const data = await response.json();

    if (!response.ok) {
      console.error(`OpenAI API error: ${data.error?.message || 'Unknown error'}`);
      return rawTranscript;
    }

    const formattedTranscript = data.choices[0].message.content.trim();
    console.log(`Transcript formatted: ${formattedTranscript.length} characters`);
    
    return formattedTranscript;

  } catch (error) {
    console.error("Error formatting transcript:", error);
    return rawTranscript; // Return original if formatting fails
  }
}

// Get video transcript with multiple fallback methods
async function getVideoTranscript(videoId: string): Promise<string> {
  console.log(`Attempting to fetch transcript for video ID: ${videoId}`);
  
  // Try multiple methods to get transcript
  const methods = [
    () => getTranscriptFromYtdl(videoId),
    () => getTranscriptFromYoutubeTranscript(videoId)
  ];
  
  for (const method of methods) {
    try {
      const transcript = await method();
      if (transcript && transcript.length > 0) {
        console.log(`Successfully fetched transcript: ${transcript.length} characters`);
        return await formatTranscript(transcript);
      }
    } catch (error) {
      console.log(`Transcript method failed:`, error.message);
      continue;
    }
  }
  
  console.log(`No transcript available for video: ${videoId}`);
  return "";
}

// Method 1: Try ytdl-core
async function getTranscriptFromYtdl(videoId: string): Promise<string> {
  const ytdl = await import('@distube/ytdl-core');
  const videoUrl = `https://www.youtube.com/watch?v=${videoId}`;
  const info = await ytdl.default.getInfo(videoUrl);
  
  const tracks = info.player_response?.captions?.playerCaptionsTracklistRenderer?.captionTracks || [];
  if (tracks.length === 0) return "";
  
  const englishTrack = tracks.find((track: any) => track.languageCode === 'en') || tracks[0];
  if (!englishTrack?.baseUrl) return "";
  
  const fetch = (await import('node-fetch')).default;
  const response = await fetch(englishTrack.baseUrl);
  const xml = await response.text();
  
  if (!xml || xml.length === 0) return "";
  
  const textRegex = /<text[^>]*>([^<]*)<\/text>/g;
  const texts = [];
  let match;
  
  while ((match = textRegex.exec(xml)) !== null) {
    if (match[1] && match[1].trim()) {
      texts.push(match[1].trim());
    }
  }
  
  return texts.join(' ')
    .replace(/&amp;/g, '&')
    .replace(/&lt;/g, '<')
    .replace(/&gt;/g, '>')
    .replace(/&quot;/g, '"')
    .replace(/&#39;/g, "'")
    .replace(/\s+/g, ' ')
    .trim();
}

// Method 2: Fallback to youtube-transcript if available
async function getTranscriptFromYoutubeTranscript(videoId: string): Promise<string> {
  try {
    const { YoutubeTranscript } = await import('youtube-transcript');
    const transcript = await YoutubeTranscript.fetchTranscript(videoId);
    
    if (!transcript || transcript.length === 0) return "";
    
    return transcript
      .map((segment: any) => segment.text)
      .join(' ')
      .replace(/\s+/g, ' ')
      .trim();
  } catch (error) {
    return "";
  }
}

// Function to get recent videos from a channel and create summaries
async function processRecentVideos(channelId: string, userId: string, channelDbId: number) {
  try {
    console.log(`Processing recent videos for channel: ${channelId}`);
    
    // Get 3 most recent videos from the channel
    const apiKey = process.env.YOUTUBE_API_KEY;
    if (!apiKey) {
      console.error("YouTube API key not configured");
      return;
    }

    console.log(`Using YouTube API key: ${apiKey.substring(0, 10)}...`);

    const response = await fetch(
      `https://www.googleapis.com/youtube/v3/search?key=${apiKey}&channelId=${channelId}&part=snippet&order=date&maxResults=3&type=video`
    );

    if (!response.ok) {
      const errorText = await response.text();
      console.error("Failed to fetch recent videos:", response.status, errorText);
      return;
    }

    const data = await response.json();
    const videos = data.items || [];

    console.log(`Found ${videos.length} recent videos to process:`, videos.map(v => v.snippet.title));

    // Process each video
    for (const video of videos) {
      try {
        const videoId = video.id.videoId;
        const videoTitle = video.snippet.title;
        const videoDescription = video.snippet.description;
        const videoThumbnail = video.snippet.thumbnails?.high?.url || video.snippet.thumbnails?.default?.url;
        const publishedAt = video.snippet.publishedAt;

        // Check if this video is already summarized
        const existingSummary = await storage.getSummaryByVideoId(userId, videoId);
        if (existingSummary) {
          console.log(`Skipping video ${videoId} - already summarized`);
          continue;
        }

        console.log(`Processing video: ${videoTitle}`);

        // Get detailed video metadata
        const videoMetadata = await getVideoMetadata(videoId);

        // Get transcript if available, otherwise use description
        let transcript = "";
        try {
          transcript = await getVideoTranscript(videoId);
        } catch (transcriptError) {
          console.log(`No transcript available for video ${videoId}, using description instead`);
        }

        // Generate AI summary using transcript if available, otherwise description
        const aiSummary = await generateSummaryFromContent(videoId, videoTitle, videoDescription, transcript);

        // Calculate read time (approximate words per minute)
        const readTime = calculateReadTime(aiSummary);

        // Create summary in database
        await storage.createSummary({
          userId,
          channelId: channelDbId,
          videoId,
          videoTitle,
          videoChannel: videoMetadata.channel,
          videoThumbnail,
          videoDuration: videoMetadata.duration,
          videoViews: videoMetadata.views,
          videoPublishedAt: new Date(publishedAt),
          videoDescription,
          summaryContent: aiSummary,
          readTime,
          timeSaved: calculateTimeSaved(videoMetadata.duration),
          isMonitored: true,
        });

        console.log(`Successfully created summary for video: ${videoTitle}`);
      } catch (videoError) {
        console.error(`Error processing video ${video.id.videoId}:`, videoError);
        // Continue with next video even if one fails
      }
    }
  } catch (error) {
    console.error("Error processing recent videos:", error);
  }
}

// Function to calculate read time based on content length
function calculateReadTime(content: string): string {
  const wordsPerMinute = 200;
  const wordCount = content.split(/\s+/).length;
  const minutes = Math.ceil(wordCount / wordsPerMinute);
  return `${minutes} min read`;
}

// Function to calculate time saved by reading summary vs watching video
function calculateTimeSaved(duration: string): string {
  // Parse duration (e.g., "10:30" or "1:23:45")
  const parts = duration.split(':').reverse();
  let totalSeconds = 0;
  
  for (let i = 0; i < parts.length; i++) {
    totalSeconds += parseInt(parts[i]) * Math.pow(60, i);
  }
  
  const videoMinutes = Math.ceil(totalSeconds / 60);
  const readMinutes = 3; // Assume average 3 minutes to read summary
  const savedMinutes = Math.max(0, videoMinutes - readMinutes);
  
  if (savedMinutes === 0) return "0 min saved";
  if (savedMinutes < 60) return `${savedMinutes} min saved`;
  
  const hours = Math.floor(savedMinutes / 60);
  const minutes = savedMinutes % 60;
  return minutes > 0 ? `${hours}h ${minutes}m saved` : `${hours}h saved`;
}

// Initialize Stripe if keys are available
let stripe: Stripe | null = null;
if (process.env.STRIPE_SECRET_KEY) {
  stripe = new Stripe(process.env.STRIPE_SECRET_KEY);
}

export async function registerRoutes(app: Express): Promise<Server> {
  // Auth middleware
  await setupAuth(app);

  // Auth routes
  app.get('/api/auth/user', isAuthenticated, async (req: any, res) => {
    try {
      const userId = req.user.claims.sub;
      const user = await storage.getUser(userId);
      res.json(user);
    } catch (error) {
      console.error("Error fetching user:", error);
      res.status(500).json({ message: "Failed to fetch user" });
    }
  });

  // Get user summaries
  app.get('/api/summaries', isAuthenticated, async (req: any, res) => {
    try {
      const userId = req.user.claims.sub;
      const summaries = await storage.getUserSummaries(userId);
      res.json(summaries);
    } catch (error) {
      console.error("Error fetching summaries:", error);
      res.status(500).json({ message: "Failed to fetch summaries" });
    }
  });

  // Create new summary
  app.post('/api/summaries', isAuthenticated, async (req: any, res) => {
    try {
      const userId = req.user.claims.sub;
      const { url } = z.object({ url: z.string().url() }).parse(req.body);

      // Get user to check usage limits
      const user = await storage.getUser(userId);
      if (!user) {
        return res.status(404).json({ message: "User not found" });
      }

      // Check usage limits based on subscription type
      let usageLimit = 5; // Free tier default
      if (user.subscriptionType === 'pro') {
        usageLimit = 100;
      } else if (user.subscriptionType === 'enterprise') {
        usageLimit = 400;
      }

      if ((user.usageCount ?? 0) >= usageLimit) {
        let upgradeMessage = "Usage limit exceeded. Please upgrade to Pro for 100 summaries per month.";
        if (user.subscriptionType === 'pro') {
          upgradeMessage = "Usage limit exceeded. Please upgrade to Enterprise for 400 summaries per month.";
        } else if (user.subscriptionType === 'enterprise') {
          upgradeMessage = "Usage limit exceeded. You've reached the maximum allowed summaries for this month.";
        }

        return res.status(429).json({ 
          message: upgradeMessage,
          code: "USAGE_LIMIT_EXCEEDED"
        });
      }

      // Extract video ID
      const videoId = extractVideoId(url);
      if (!videoId) {
        return res.status(400).json({ message: "Invalid YouTube URL" });
      }

      // Check if summary already exists for this user and video
      const existingSummary = await storage.getSummaryByVideoId(userId, videoId);
      if (existingSummary) {
        return res.json(existingSummary);
      }

      // Get video metadata
      console.log(`Fetching metadata for video ID: ${videoId}`);
      const metadata = await getVideoMetadata(videoId);
      console.log(`Video metadata:`, { title: metadata.title, channel: metadata.channel });

      // Get video transcript
      const transcript = await getVideoTranscript(videoId);

      // Generate AI summary
      console.log(`Generating AI summary for: ${metadata.title}`);
      const summaryContent = await generateSummary(videoId, metadata.title, metadata.description);
      console.log(`Summary generated, length: ${summaryContent.length} characters`);

      // Calculate read time and time saved
      const readTime = "3 min read";
      const timeSaved = "9 minutes";

      // Create summary
      const summaryData = insertSummarySchema.parse({
        userId,
        videoId,
        videoTitle: metadata.title,
        videoChannel: metadata.channel,
        videoThumbnail: metadata.thumbnail,
        videoDuration: metadata.duration,
        videoViews: metadata.views,
        videoTranscript: transcript,
        summaryContent,
        readTime,
        timeSaved,
      });

      const summary = await storage.createSummary(summaryData);

      // Update user usage count
      await storage.incrementUserUsage(userId);

      res.json(summary);
    } catch (error) {
      console.error("Error creating summary:", error);
      if (error instanceof z.ZodError) {
        res.status(400).json({ message: "Invalid request data" });
      } else {
        res.status(500).json({ message: "Failed to create summary" });
      }
    }
  });

  // Get specific summary
  app.get('/api/summaries/:id', isAuthenticated, async (req: any, res) => {
    try {
      const userId = req.user.claims.sub;
      const summaryId = parseInt(req.params.id);

      const summary = await storage.getSummary(summaryId, userId);
      if (!summary) {
        return res.status(404).json({ message: "Summary not found" });
      }

      res.json(summary);
    } catch (error) {
      console.error("Error fetching summary:", error);
      res.status(500).json({ message: "Failed to fetch summary" });
    }
  });

  // Submit feedback for a summary
  app.post('/api/summaries/:id/feedback', isAuthenticated, async (req: any, res) => {
    try {
      const summaryId = parseInt(req.params.id);
      const userId = req.user.claims.sub;
      const { rating } = req.body;
      
      if (!rating || !['helpful', 'not_helpful'].includes(rating)) {
        return res.status(400).json({ message: "Invalid rating. Must be 'helpful' or 'not_helpful'" });
      }
      
      // Verify summary exists and belongs to user
      const summary = await storage.getSummary(summaryId, userId);
      if (!summary) {
        return res.status(404).json({ message: "Summary not found" });
      }
      
      await storage.createFeedback(summaryId, userId, rating);
      
      res.json({ message: "Feedback submitted successfully" });
    } catch (error) {
      console.error("Error submitting feedback:", error);
      res.status(500).json({ message: "Failed to submit feedback" });
    }
  });

  // Get feedback stats (for admin dashboard)
  app.get('/api/feedback/stats', isAuthenticated, async (req: any, res) => {
    try {
      const summaryId = req.query.summaryId ? parseInt(req.query.summaryId as string) : undefined;
      const stats = await storage.getFeedbackStats(summaryId);
      
      res.json(stats);
    } catch (error) {
      console.error("Error fetching feedback stats:", error);
      res.status(500).json({ message: "Failed to fetch feedback stats" });
    }
  });



  // Search summaries
  app.get('/api/search', isAuthenticated, async (req: any, res) => {
    try {
      const userId = req.user.claims.sub;
      const { q: query } = req.query;

      if (!query) {
        return res.status(400).json({ message: "Search query required" });
      }

      const results = await storage.searchSummariesByText(userId, query as string);
      res.json(results);
    } catch (error) {
      console.error("Error searching summaries:", error);
      res.status(500).json({ message: "Failed to search summaries" });
    }
  });

  // Channel monitoring routes
  app.get('/api/channels', isAuthenticated, async (req: any, res) => {
    try {
      const userId = req.user.claims.sub;
      const channels = await storage.getUserChannels(userId);
      res.json(channels);
    } catch (error) {
      console.error("Error fetching channels:", error);
      res.status(500).json({ message: "Failed to fetch channels" });
    }
  });

  app.post('/api/channels', isAuthenticated, async (req: any, res) => {
    try {
      const userId = req.user.claims.sub;
      const { channelUrl, channelId } = req.body;

      if (!channelUrl && !channelId) {
        return res.status(400).json({ message: "Channel URL or ID required" });
      }

      // Check subscription limits
      const user = await storage.getUser(userId);
      const existingChannels = await storage.getUserChannels(userId);
      
      const limits = { free: 5, pro: 25, enterprise: 100 };
      const userLimit = limits[user?.subscriptionType as keyof typeof limits] || 5;
      
      if (existingChannels.length >= userLimit) {
        return res.status(403).json({ 
          message: `Channel limit reached. ${user?.subscriptionType || 'free'} plan allows ${userLimit} channels.` 
        });
      }

      // Extract channel ID from URL
      let extractedChannelId = channelId || await extractChannelIdFromUrl(channelUrl);
      if (!extractedChannelId) {
        return res.status(400).json({ message: "Invalid YouTube channel URL" });
      }

      // Check if channel already exists
      const existingChannel = await storage.getChannelByChannelId(userId, extractedChannelId);
      if (existingChannel) {
        return res.status(400).json({ message: "Channel already being monitored" });
      }

      // Fetch channel metadata
      const channelMetadata = await getChannelMetadata(extractedChannelId);
      
      const newChannel = await storage.createChannel({
        userId,
        channelId: extractedChannelId,
        channelName: channelMetadata.title,
        channelHandle: channelMetadata.customUrl || null,
        channelThumbnail: channelMetadata.thumbnail,
        subscriberCount: channelMetadata.subscriberCount,
        lastChecked: null,
        videoCount: 0,
      });

      // Process the 3 most recent videos in the background
      processRecentVideos(extractedChannelId, userId, newChannel.id).catch(error => {
        console.error(`Error processing recent videos for channel ${extractedChannelId}:`, error);
      });

      res.json(newChannel);
    } catch (error) {
      console.error("Error adding channel:", error);
      res.status(500).json({ message: "Failed to add channel" });
    }
  });

  // Bulk import channels
  app.post('/api/channels/bulk', isAuthenticated, async (req: any, res) => {
    try {
      const userId = req.user.claims.sub;
      const { channelUrls } = req.body;

      if (!Array.isArray(channelUrls) || channelUrls.length === 0) {
        return res.status(400).json({ message: "Channel URLs array required" });
      }

      // Check subscription limits
      const user = await storage.getUser(userId);
      const existingChannels = await storage.getUserChannels(userId);
      
      const limits = { free: 5, pro: 25, enterprise: 100 };
      const userLimit = limits[user?.subscriptionType as keyof typeof limits] || 5;
      
      if (existingChannels.length >= userLimit) {
        return res.status(403).json({ 
          message: `Channel limit reached. ${user?.subscriptionType || 'free'} plan allows ${userLimit} channels.` 
        });
      }

      const results = {
        successful: 0,
        failed: 0,
        errors: [] as string[]
      };

      // Process each channel URL
      for (const channelUrl of channelUrls) {
        try {
          // Extract channel ID from URL
          let extractedChannelId = await extractChannelIdFromUrl(channelUrl);
          if (!extractedChannelId) {
            results.failed++;
            results.errors.push(`Invalid URL: ${channelUrl}`);
            continue;
          }

          // Check if channel already exists
          const existingChannel = await storage.getChannelByChannelId(userId, extractedChannelId);
          if (existingChannel) {
            results.failed++;
            results.errors.push(`Channel already monitored: ${channelUrl}`);
            continue;
          }

          // Check if we've hit the limit during bulk import
          const currentChannels = await storage.getUserChannels(userId);
          if (currentChannels.length >= userLimit) {
            results.failed++;
            results.errors.push(`Channel limit reached during import: ${channelUrl}`);
            continue;
          }

          // Fetch channel metadata
          const channelMetadata = await getChannelMetadata(extractedChannelId);
          
          const newChannel = await storage.createChannel({
            userId,
            channelId: extractedChannelId,
            channelName: channelMetadata.title,
            channelHandle: channelMetadata.customUrl || null,
            channelThumbnail: channelMetadata.thumbnail,
            subscriberCount: channelMetadata.subscriberCount,
            lastChecked: null,
            videoCount: 0,
          });

          // Process the 3 most recent videos in the background
          processRecentVideos(extractedChannelId, userId, newChannel.id).catch(error => {
            console.error(`Error processing recent videos for channel ${extractedChannelId}:`, error);
          });

          results.successful++;
        } catch (error) {
          results.failed++;
          console.error(`Error processing ${channelUrl}:`, error);
          results.errors.push(`Failed to add ${channelUrl}: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
      }

      res.json(results);
    } catch (error) {
      console.error("Error bulk importing channels:", error);
      res.status(500).json({ message: "Failed to bulk import channels" });
    }
  });

  app.get('/api/monitored-summaries', isAuthenticated, async (req: any, res) => {
    try {
      const userId = req.user.claims.sub;
      const limit = parseInt(req.query.limit as string) || 20;
      const channelId = req.query.channelId ? parseInt(req.query.channelId as string) : undefined;

      let summaries;
      if (channelId) {
        summaries = await storage.getChannelSummaries(channelId, userId);
      } else {
        summaries = await storage.getRecentMonitoredSummaries(userId, limit);
      }
      
      res.json(summaries);
    } catch (error) {
      console.error("Error fetching monitored summaries:", error);
      res.status(500).json({ message: "Failed to fetch monitored summaries" });
    }
  });

  app.patch('/api/channels/:id', isAuthenticated, async (req: any, res) => {
    try {
      const userId = req.user.claims.sub;
      const channelId = parseInt(req.params.id);
      const { isActive } = req.body;

      const updatedChannel = await storage.updateChannel(channelId, userId, { isActive });
      res.json(updatedChannel);
    } catch (error) {
      console.error("Error updating channel:", error);
      res.status(500).json({ message: "Failed to update channel" });
    }
  });

  app.delete('/api/channels/:id', isAuthenticated, async (req: any, res) => {
    try {
      const userId = req.user.claims.sub;
      const channelId = parseInt(req.params.id);

      await storage.deleteChannel(channelId, userId);
      res.json({ message: "Channel removed from monitoring" });
    } catch (error) {
      console.error("Error deleting channel:", error);
      res.status(500).json({ message: "Failed to delete channel" });
    }
  });

  // Process recent videos for existing channel
  app.post('/api/channels/:id/process-recent', isAuthenticated, async (req: any, res) => {
    try {
      const userId = req.user.claims.sub;
      const channelId = parseInt(req.params.id);

      // Get the channel
      const channel = await storage.getChannel(channelId, userId);
      if (!channel) {
        return res.status(404).json({ message: "Channel not found" });
      }

      // Process the 3 most recent videos in the background
      processRecentVideos(channel.channelId, userId, channelId).catch(error => {
        console.error(`Error processing recent videos for channel ${channel.channelId}:`, error);
      });

      res.json({ message: "Processing recent videos in background" });
    } catch (error) {
      console.error("Error triggering video processing:", error);
      res.status(500).json({ message: "Failed to process recent videos" });
    }
  });

  // Process recent videos for all user channels
  app.post('/api/channels/process-all-recent', isAuthenticated, async (req: any, res) => {
    try {
      const userId = req.user.claims.sub;
      console.log(`Processing all recent videos for user: ${userId}`);

      // Get all user channels
      const channels = await storage.getUserChannels(userId);
      console.log(`Found ${channels.length} channels for user`);
      
      if (channels.length === 0) {
        return res.json({ message: "No channels found" });
      }

      // Process recent videos for each channel
      let processedCount = 0;
      for (const channel of channels) {
        try {
          console.log(`Starting processing for channel: ${channel.channelName} (${channel.channelId})`);
          processRecentVideos(channel.channelId, userId, channel.id).catch(error => {
            console.error(`Error processing recent videos for channel ${channel.channelId}:`, error);
          });
          processedCount++;
        } catch (error) {
          console.error(`Failed to start processing for channel ${channel.channelId}:`, error);
        }
      }

      console.log(`Started processing for ${processedCount} channels`);
      res.json({ 
        message: `Processing recent videos for ${processedCount} channels in background`,
        channelsProcessed: processedCount,
        totalChannels: channels.length
      });
    } catch (error) {
      console.error("Error processing all channels:", error);
      res.status(500).json({ message: "Failed to process channels" });
    }
  });

  // Vector search endpoint
  app.get('/api/vector-search', isAuthenticated, async (req: any, res) => {
    try {
      const userId = req.user.claims.sub;
      const { q: query, channels } = req.query;

      if (!query) {
        return res.status(400).json({ message: "Search query required" });
      }

      const channelIds = channels ? channels.split(',').map((id: string) => parseInt(id)).filter((id: number) => !isNaN(id)) : undefined;
      
      const results = await storage.searchSummariesByVector(userId, query as string, {
        channelIds,
        limit: 20
      });
      
      res.json(results);
    } catch (error) {
      console.error("Error performing vector search:", error);
      res.status(500).json({ message: "Failed to perform vector search" });
    }
  });

  // Topic management endpoints
  app.get('/api/topics', isAuthenticated, async (req: any, res) => {
    try {
      const userId = req.user.claims.sub;
      const topics = await storage.getTopics(userId);
      res.json(topics);
    } catch (error) {
      console.error("Error fetching topics:", error);
      res.status(500).json({ message: "Failed to fetch topics" });
    }
  });

  app.post('/api/topics', isAuthenticated, async (req: any, res) => {
    try {
      const { name, description, parentTopicId, colorHex, topicType } = req.body;
      const topic = await storage.createTopic({
        name,
        description,
        parentTopicId,
        colorHex,
        topicType: topicType || 'user_defined'
      });
      res.json(topic);
    } catch (error) {
      console.error("Error creating topic:", error);
      res.status(500).json({ message: "Failed to create topic" });
    }
  });

  app.patch('/api/topics/:id', isAuthenticated, async (req: any, res) => {
    try {
      const topicId = parseInt(req.params.id);
      const { name, description, parentTopicId, colorHex, topicType } = req.body;
      
      // Only pass valid update fields, excluding timestamps and auto-generated fields
      const updates: any = {};
      if (name !== undefined) updates.name = name;
      if (description !== undefined) updates.description = description;
      if (parentTopicId !== undefined) updates.parentTopicId = parentTopicId;
      if (colorHex !== undefined) updates.colorHex = colorHex;
      if (topicType !== undefined) updates.topicType = topicType;
      
      const topic = await storage.updateTopic(topicId, updates);
      res.json(topic);
    } catch (error) {
      console.error("Error updating topic:", error);
      res.status(500).json({ message: "Failed to update topic" });
    }
  });

  app.put('/api/topics/:id', isAuthenticated, async (req: any, res) => {
    try {
      const topicId = parseInt(req.params.id);
      const { name, description, parentTopicId, colorHex, topicType } = req.body;
      
      // Only pass valid update fields, excluding timestamps and auto-generated fields
      const updates: any = {};
      if (name !== undefined) updates.name = name;
      if (description !== undefined) updates.description = description;
      if (parentTopicId !== undefined) updates.parentTopicId = parentTopicId;
      if (colorHex !== undefined) updates.colorHex = colorHex;
      if (topicType !== undefined) updates.topicType = topicType;
      
      const topic = await storage.updateTopic(topicId, updates);
      res.json(topic);
    } catch (error) {
      console.error("Error updating topic:", error);
      res.status(500).json({ message: "Failed to update topic" });
    }
  });

  app.delete('/api/topics/:id', isAuthenticated, async (req: any, res) => {
    try {
      const topicId = parseInt(req.params.id);
      await storage.deleteTopic(topicId);
      res.json({ message: "Topic deleted successfully" });
    } catch (error) {
      console.error("Error deleting topic:", error);
      res.status(500).json({ message: "Failed to delete topic" });
    }
  });

  // Channel-Topic assignment endpoints
  app.get('/api/channels/:channelId/topics', isAuthenticated, async (req: any, res) => {
    try {
      const userId = req.user.claims.sub;
      const channelId = parseInt(req.params.channelId);

      // Verify channel ownership
      const channel = await storage.getChannel(channelId, userId);
      if (!channel) {
        return res.status(404).json({ message: "Channel not found" });
      }

      const topics = await storage.getTopicsByChannel(channelId);
      res.json(topics);
    } catch (error) {
      console.error("Error fetching channel topics:", error);
      res.status(500).json({ message: "Failed to fetch channel topics" });
    }
  });

  app.post('/api/channels/:channelId/topics', isAuthenticated, async (req: any, res) => {
    try {
      const userId = req.user.claims.sub;
      const channelId = parseInt(req.params.channelId);
      const { topicId, topicIds } = req.body;

      // Verify channel ownership
      const channel = await storage.getChannel(channelId, userId);
      if (!channel) {
        return res.status(404).json({ message: "Channel not found" });
      }

      // Handle both single topicId and multiple topicIds
      const idsToAssign = topicIds || (topicId ? [topicId] : []);
      
      if (idsToAssign.length === 0) {
        return res.status(400).json({ message: "topicId or topicIds required" });
      }

      const assignments = [];
      for (const id of idsToAssign) {
        const assignment = await storage.assignChannelToTopic({
          channelId,
          topicId: id,
          assignmentType: 'manual'
        });
        assignments.push(assignment);
      }

      res.json(assignments.length === 1 ? assignments[0] : assignments);
    } catch (error) {
      console.error("Error assigning channel to topics:", error);
      res.status(500).json({ message: "Failed to assign channel to topics" });
    }
  });

  app.delete('/api/channels/:channelId/topics/:topicId', isAuthenticated, async (req: any, res) => {
    try {
      const userId = req.user.claims.sub;
      const channelId = parseInt(req.params.channelId);
      const topicId = parseInt(req.params.topicId);

      // Verify channel ownership
      const channel = await storage.getChannel(channelId, userId);
      if (!channel) {
        return res.status(404).json({ message: "Channel not found" });
      }

      await storage.removeChannelFromTopic(channelId, topicId);
      res.json({ message: "Channel removed from topic" });
    } catch (error) {
      console.error("Error removing channel from topic:", error);
      res.status(500).json({ message: "Failed to remove channel from topic" });
    }
  });

  app.get('/api/channels/:channelId/topics', isAuthenticated, async (req: any, res) => {
    try {
      const userId = req.user.claims.sub;
      const channelId = parseInt(req.params.channelId);

      // Verify channel ownership
      const channel = await storage.getChannel(channelId, userId);
      if (!channel) {
        return res.status(404).json({ message: "Channel not found" });
      }

      const topics = await storage.getTopicsByChannel(channelId);
      res.json(topics);
    } catch (error) {
      console.error("Error fetching channel topics:", error);
      res.status(500).json({ message: "Failed to fetch channel topics" });
    }
  });

  app.get('/api/topics/:topicId/channels', isAuthenticated, async (req: any, res) => {
    try {
      const userId = req.user.claims.sub;
      const topicId = parseInt(req.params.topicId);
      
      const channels = await storage.getChannelsByTopic(topicId, userId);
      res.json(channels);
    } catch (error) {
      console.error("Error fetching topic channels:", error);
      res.status(500).json({ message: "Failed to fetch topic channels" });
    }
  });

  app.get('/api/topics/:topicId/summaries', isAuthenticated, async (req: any, res) => {
    try {
      const userId = req.user.claims.sub;
      const topicId = parseInt(req.params.topicId);
      
      const summaries = await storage.getSummariesByTopic(topicId, userId);
      res.json(summaries);
    } catch (error) {
      console.error("Error fetching topic summaries:", error);
      res.status(500).json({ message: "Failed to fetch topic summaries" });
    }
  });

  // RSS Feed endpoints
  app.get('/rss/library', isAuthenticated, async (req: any, res) => {
    try {
      const userId = req.user.claims.sub;
      const summaries = await storage.getUserSummaries(userId);
      const baseUrl = `${req.protocol}://${req.get('host')}`;
      
      const feed = createAllLibraryFeed(summaries, baseUrl);
      const rssXml = generateRSSFeed(feed);
      
      res.set('Content-Type', 'application/rss+xml');
      res.send(rssXml);
    } catch (error) {
      console.error("Error generating library RSS feed:", error);
      res.status(500).json({ message: "Failed to generate RSS feed" });
    }
  });

  app.get('/rss/channel/:channelId', isAuthenticated, async (req: any, res) => {
    try {
      const userId = req.user.claims.sub;
      const channelId = parseInt(req.params.channelId);
      
      const channel = await storage.getChannel(channelId, userId);
      if (!channel) {
        return res.status(404).json({ message: "Channel not found" });
      }
      
      const summaries = await storage.getChannelSummaries(channelId, userId);
      const baseUrl = `${req.protocol}://${req.get('host')}`;
      
      const feed = createChannelFeed(summaries, channel, baseUrl);
      const rssXml = generateRSSFeed(feed);
      
      res.set('Content-Type', 'application/rss+xml');
      res.send(rssXml);
    } catch (error) {
      console.error("Error generating channel RSS feed:", error);
      res.status(500).json({ message: "Failed to generate RSS feed" });
    }
  });

  app.get('/rss/topic/:topicId', isAuthenticated, async (req: any, res) => {
    try {
      const userId = req.user.claims.sub;
      const topicId = parseInt(req.params.topicId);
      
      const topic = await storage.getTopic(topicId);
      if (!topic) {
        return res.status(404).json({ message: "Topic not found" });
      }
      
      const summaries = await storage.getSummariesByTopic(topicId, userId);
      const baseUrl = `${req.protocol}://${req.get('host')}`;
      
      const feed = createTopicFeed(summaries, topic, baseUrl);
      const rssXml = generateRSSFeed(feed);
      
      res.set('Content-Type', 'application/rss+xml');
      res.send(rssXml);
    } catch (error) {
      console.error("Error generating topic RSS feed:", error);
      res.status(500).json({ message: "Failed to generate RSS feed" });
    }
  });

  // Stripe subscription endpoints (if Stripe is configured)
  if (stripe) {
    app.post("/api/create-payment-intent", isAuthenticated, async (req: any, res) => {
      try {
        const { amount } = req.body;
        const paymentIntent = await stripe!.paymentIntents.create({
          amount: Math.round(amount * 100), // Convert to cents
          currency: "usd",
        });
        res.json({ clientSecret: paymentIntent.client_secret });
      } catch (error: any) {
        res.status(500).json({ message: "Error creating payment intent: " + error.message });
      }
    });
  }

  const httpServer = createServer(app);
  return httpServer;
}