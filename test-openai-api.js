// Test OpenAI API directly to troubleshoot cost tracking
import fetch from 'node-fetch';

async function testOpenAI() {
  const apiKey = process.env.OPENAI_API_KEY;
  
  if (!apiKey) {
    console.log('❌ OPENAI_API_KEY not found');
    return;
  }
  
  console.log('✅ OPENAI_API_KEY found');
  console.log('🔄 Testing OpenAI API...');
  
  try {
    const response = await fetch('https://api.openai.com/v1/chat/completions', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${apiKey}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        model: 'gpt-4o-mini',
        messages: [
          {
            role: 'system',
            content: 'You are a helpful assistant.'
          },
          {
            role: 'user', 
            content: 'Say "Hello world" and nothing else.'
          }
        ],
        max_tokens: 10,
        temperature: 0.1,
      }),
    });
    
    console.log(`📊 Response Status: ${response.status}`);
    
    const data = await response.json();
    
    if (!response.ok) {
      console.log('❌ API Error:', data);
      return;
    }
    
    console.log('✅ API call successful!');
    console.log('📝 Response content:', data.choices[0]?.message?.content);
    
    // Check for usage data
    if (data.usage) {
      console.log('💰 Usage data found:');
      console.log(`  - Input tokens: ${data.usage.prompt_tokens}`);
      console.log(`  - Output tokens: ${data.usage.completion_tokens}`);
      console.log(`  - Total tokens: ${data.usage.total_tokens}`);
      
      // Calculate costs
      const inputCost = (data.usage.prompt_tokens / 1000000) * 2.50;
      const outputCost = (data.usage.completion_tokens / 1000000) * 10.00;
      const totalCost = inputCost + outputCost;
      
      console.log(`💵 Calculated costs:`);
      console.log(`  - Input cost: $${inputCost.toFixed(6)}`);
      console.log(`  - Output cost: $${outputCost.toFixed(6)}`);
      console.log(`  - Total cost: $${totalCost.toFixed(6)}`);
      console.log(`  - Cost in hundredths of cents: ${Math.round(totalCost * 10000)}`);
    } else {
      console.log('❌ No usage data in response');
    }
    
  } catch (error) {
    console.log('❌ Test failed:', error.message);
  }
}

testOpenAI();