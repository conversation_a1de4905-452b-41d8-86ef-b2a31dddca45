import React, { useState, useMemo, useEffect } from "react";
import { useAuth } from "@/hooks/useAuth";
import { useQuery } from "@tanstack/react-query";
import { useToast } from "@/hooks/use-toast";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { 
  Search, 
  Grid3X3, 
  List, 
  ChevronDown, 
  ChevronRight, 
  Calendar, 
  Clock, 
  <PERSON>,
  Play,
  Filter,
  ArrowUpDown,
  Users,
  Tag,
  Video
} from "lucide-react";
import { <PERSON> } from "wouter";
import { Summary, Channel, Topic } from "@shared/schema";

interface UnifiedLibraryProps {
  [key: string]: any;
}

type ViewMode = "table" | "card";
type TabType = "all" | "by-channel" | "by-topic";
type SortField = "title" | "date" | "channel" | "duration" | "views";
type SortDirection = "asc" | "desc";

interface ExpandedChannels {
  [channelId: number]: boolean;
}

interface ExpandedTopics {
  [topicId: number]: boolean;
}

export default function UnifiedLibrary() {
  const { user: authUser } = useAuth();
  const { toast } = useToast();
  
  // State management
  const [activeTab, setActiveTab] = useState<TabType>("all");
  const [viewMode, setViewMode] = useState<ViewMode>("card");
  
  // Force card view on mobile
  useEffect(() => {
    const handleResize = () => {
      if (window.innerWidth < 640) {
        setViewMode("card");
      }
    };
    
    handleResize();
    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);
  const [searchQuery, setSearchQuery] = useState("");
  const [sortField, setSortField] = useState<SortField>("date");
  const [sortDirection, setSortDirection] = useState<SortDirection>("desc");
  const [expandedChannels, setExpandedChannels] = useState<ExpandedChannels>({});
  const [expandedTopics, setExpandedTopics] = useState<ExpandedTopics>({});
  
  // Tab-specific filters
  const [channelFilter, setChannelFilter] = useState<string>("");
  const [topicFilter, setTopicFilter] = useState<string>("");
  const [dateFilter, setDateFilter] = useState<string>("");

  // Fetch data
  const { data: summaries = [], isLoading: summariesLoading } = useQuery<Summary[]>({
    queryKey: ["/api/summaries"],
    enabled: !!authUser,
  });

  const { data: channels = [], isLoading: channelsLoading } = useQuery<Channel[]>({
    queryKey: ["/api/channels"],
    enabled: !!authUser,
  });

  const { data: topics = [], isLoading: topicsLoading } = useQuery<Topic[]>({
    queryKey: ["/api/topics"],
    enabled: !!authUser,
  });

  const { data: summariesByTopicData = [], isLoading: summariesByTopicLoading } = useQuery<{ topic: Topic; summaries: Summary[] }[]>({
    queryKey: ["/api/summaries-by-topics"],
    enabled: !!authUser,
  });

  // Computed data
  const filteredSummaries = useMemo(() => {
    let filtered = summaries;

    // Apply search filter
    if (searchQuery.trim()) {
      const query = searchQuery.toLowerCase();
      filtered = filtered.filter(summary => 
        summary.videoTitle.toLowerCase().includes(query) ||
        summary.videoChannel.toLowerCase().includes(query) ||
        summary.summaryContent.toLowerCase().includes(query)
      );
    }

    // Apply tab-specific filters
    if (activeTab === "by-channel" && channelFilter && channelFilter !== "all") {
      filtered = filtered.filter(summary => summary.channelId?.toString() === channelFilter);
    }

    if (activeTab === "by-topic" && topicFilter && topicFilter !== "all") {
      filtered = filtered.filter(summary => 
        summary.topics && summary.topics.includes(topicFilter)
      );
    }

    // Apply date filter
    if (dateFilter && dateFilter !== "all") {
      const now = new Date();
      const filterDate = new Date();
      
      switch (dateFilter) {
        case "today":
          filterDate.setHours(0, 0, 0, 0);
          break;
        case "week":
          filterDate.setDate(now.getDate() - 7);
          break;
        case "month":
          filterDate.setMonth(now.getMonth() - 1);
          break;
        case "year":
          filterDate.setFullYear(now.getFullYear() - 1);
          break;
      }
      
      if (dateFilter !== "all") {
        filtered = filtered.filter(summary => 
          summary.createdAt && new Date(summary.createdAt) >= filterDate
        );
      }
    }

    // Apply sorting
    return filtered.sort((a, b) => {
      let aValue: any, bValue: any;
      
      switch (sortField) {
        case "title":
          aValue = a.videoTitle.toLowerCase();
          bValue = b.videoTitle.toLowerCase();
          break;
        case "channel":
          aValue = a.videoChannel.toLowerCase();
          bValue = b.videoChannel.toLowerCase();
          break;
        case "date":
          aValue = new Date(a.createdAt || 0);
          bValue = new Date(b.createdAt || 0);
          break;
        case "duration":
          aValue = a.videoDuration;
          bValue = b.videoDuration;
          break;
        case "views":
          aValue = parseInt(a.videoViews?.replace(/[^\d]/g, '') || '0');
          bValue = parseInt(b.videoViews?.replace(/[^\d]/g, '') || '0');
          break;
        default:
          return 0;
      }
      
      if (sortDirection === "asc") {
        return aValue > bValue ? 1 : -1;
      } else {
        return aValue < bValue ? 1 : -1;
      }
    });
  }, [summaries, searchQuery, activeTab, channelFilter, topicFilter, dateFilter, sortField, sortDirection]);

  // Group data by channel
  const summariesByChannel = useMemo(() => {
    const grouped: Record<number, { channel: Channel; summaries: Summary[] }> = {};
    
    filteredSummaries.forEach(summary => {
      if (summary.channelId) {
        const channel = channels.find(c => c.id === summary.channelId);
        if (channel) {
          if (!grouped[summary.channelId]) {
            grouped[summary.channelId] = { channel, summaries: [] };
          }
          grouped[summary.channelId].summaries.push(summary);
        }
      }
    });
    
    return Object.values(grouped);
  }, [filteredSummaries, channels]);

  // Use the new data structure for summaries by topic
  const summariesByTopic = useMemo(() => {
    if (!summariesByTopicData) return [];
    
    // Apply filtering to the topic summaries
    return summariesByTopicData.map(({ topic, summaries: topicSummaries }) => {
      let filtered = topicSummaries;
      
      // Apply search filter
      if (searchQuery.trim()) {
        const query = searchQuery.toLowerCase();
        filtered = filtered.filter(summary => 
          summary.videoTitle.toLowerCase().includes(query) ||
          summary.videoChannel.toLowerCase().includes(query) ||
          summary.summaryContent.toLowerCase().includes(query)
        );
      }
      
      // Apply topic filter
      if (activeTab === "by-topic" && topicFilter && topicFilter !== "all" && topicFilter !== topic.name) {
        filtered = [];
      }
      
      // Apply date filter
      if (dateFilter && dateFilter !== "all") {
        const now = new Date();
        const filterDate = new Date();
        
        switch (dateFilter) {
          case "today":
            filterDate.setHours(0, 0, 0, 0);
            break;
          case "week":
            filterDate.setDate(now.getDate() - 7);
            break;
          case "month":
            filterDate.setMonth(now.getMonth() - 1);
            break;
          case "year":
            filterDate.setFullYear(now.getFullYear() - 1);
            break;
        }
        
        if (dateFilter !== "all") {
          filtered = filtered.filter(summary => 
            summary.createdAt && new Date(summary.createdAt) >= filterDate
          );
        }
      }
      
      return { topic, summaries: filtered };
    }).filter(item => item.summaries.length > 0); // Only return topics with summaries
  }, [summariesByTopicData, searchQuery, activeTab, topicFilter, dateFilter]);

  const toggleChannelExpansion = (channelId: number) => {
    setExpandedChannels(prev => ({
      ...prev,
      [channelId]: !prev[channelId]
    }));
  };

  const toggleTopicExpansion = (topicId: number) => {
    setExpandedTopics(prev => ({
      ...prev,
      [topicId]: !prev[topicId]
    }));
  };

  const handleSort = (field: SortField) => {
    if (sortField === field) {
      setSortDirection(prev => prev === "asc" ? "desc" : "asc");
    } else {
      setSortField(field);
      setSortDirection("desc");
    }
  };

  const SummaryCard = ({ summary }: { summary: Summary }) => (
    <Card className="group hover:shadow-lg transition-all duration-200 cursor-pointer">
      <Link href={`/summary/${summary.id}`}>
        <CardHeader className="p-3 sm:p-4">
          <div className="flex gap-3">
            <div className="relative flex-shrink-0">
              <img 
                src={summary.videoThumbnail} 
                alt={summary.videoTitle}
                className="w-20 h-12 sm:w-24 sm:h-16 object-cover rounded-md"
              />
              <div className="absolute inset-0 bg-black/20 rounded-md flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity">
                <Play className="h-4 w-4 sm:h-5 sm:w-5 text-white" />
              </div>
            </div>
            <div className="flex-1 min-w-0">
              <h3 className="font-medium text-sm sm:text-base line-clamp-2 mb-1">{summary.videoTitle}</h3>
              <p className="text-xs sm:text-sm text-muted-foreground mb-2 truncate">{summary.videoChannel}</p>
              <div className="flex items-center gap-2 sm:gap-3 text-xs text-muted-foreground flex-wrap">
                <span className="flex items-center gap-1">
                  <Clock className="h-3 w-3" />
                  {summary.videoDuration}
                </span>
                <span className="flex items-center gap-1">
                  <Eye className="h-3 w-3" />
                  {summary.videoViews}
                </span>
                <span className="flex items-center gap-1 hidden sm:flex">
                  <Calendar className="h-3 w-3" />
                  {summary.videoPublishedAt ? new Date(summary.videoPublishedAt).toLocaleDateString() : 'N/A'}
                </span>
              </div>
            </div>
          </div>
        </CardHeader>
      </Link>
    </Card>
  );

  const SummaryTableRow = ({ summary }: { summary: Summary }) => (
    <TableRow className="hover:bg-muted/50 cursor-pointer">
      <TableCell className="p-4">
        <Link href={`/summary/${summary.id}`} className="flex items-center gap-3">
          <img 
            src={summary.videoThumbnail} 
            alt={summary.videoTitle}
            className="w-12 h-8 object-cover rounded"
          />
          <div className="min-w-0 flex-1">
            <p className="font-medium text-sm line-clamp-1">{summary.videoTitle}</p>
            <p className="text-xs text-muted-foreground">{summary.readTime}</p>
          </div>
        </Link>
      </TableCell>
      <TableCell>{summary.videoChannel}</TableCell>
      <TableCell>{summary.videoDuration}</TableCell>
      <TableCell>{summary.videoViews}</TableCell>
      <TableCell>
        {summary.videoPublishedAt ? new Date(summary.videoPublishedAt).toLocaleDateString() : 'N/A'}
      </TableCell>
      <TableCell>
        <Badge variant="outline">{summary.readTime}</Badge>
      </TableCell>
    </TableRow>
  );

  if (summariesLoading || channelsLoading || topicsLoading || summariesByTopicLoading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-3 sm:px-6 pt-4 pb-6 max-w-6xl">
      {/* Stats Header */}
      <div className="flex items-center justify-between mb-6">
        <div className="text-muted-foreground text-sm">
          {summaries.length} summaries across {channels.length} channels
        </div>
        
        <div className="hidden sm:flex items-center gap-2">
          <Button
            variant={viewMode === "card" ? "default" : "outline"}
            size="sm"
            onClick={() => setViewMode("card")}
          >
            <Grid3X3 className="h-4 w-4" />
          </Button>
          <Button
            variant={viewMode === "table" ? "default" : "outline"}
            size="sm"
            onClick={() => setViewMode("table")}
          >
            <List className="h-4 w-4" />
          </Button>
        </div>
      </div>

      {/* Search and Filters - Mobile Optimized */}
      <div className="flex flex-col gap-3 mb-4">
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Search summaries..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="pl-10 h-10 sm:h-9"
          />
        </div>
        
        <div className="flex gap-2 overflow-x-auto">
          <Select value={dateFilter} onValueChange={setDateFilter}>
            <SelectTrigger className="min-w-28 h-10 sm:h-9">
              <SelectValue placeholder="All time" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All time</SelectItem>
              <SelectItem value="today">Today</SelectItem>
              <SelectItem value="week">This week</SelectItem>
              <SelectItem value="month">This month</SelectItem>
              <SelectItem value="year">This year</SelectItem>
            </SelectContent>
          </Select>
          
          <Select value={`${sortField}-${sortDirection}`} onValueChange={(value) => {
            const [field, direction] = value.split('-') as [SortField, SortDirection];
            setSortField(field);
            setSortDirection(direction);
          }}>
            <SelectTrigger className="min-w-32 h-10 sm:h-9">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="date-desc">Newest first</SelectItem>
              <SelectItem value="date-asc">Oldest first</SelectItem>
              <SelectItem value="title-asc">Title A-Z</SelectItem>
              <SelectItem value="title-desc">Title Z-A</SelectItem>
              <SelectItem value="channel-asc">Channel A-Z</SelectItem>
              <SelectItem value="views-desc">Most views</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>

      {/* Tabs with Enhanced Visual Demarcation - Mobile Optimized */}
      <Tabs value={activeTab} onValueChange={(value) => setActiveTab(value as TabType)} className="space-y-4 sm:space-y-6">
        <TabsList className="grid w-full grid-cols-3 bg-muted/50 p-1 h-12 sm:h-10">
          <TabsTrigger 
            value="all" 
            className="flex items-center gap-1 sm:gap-2 data-[state=active]:bg-primary data-[state=active]:text-primary-foreground data-[state=active]:shadow-md data-[state=active]:border data-[state=active]:border-primary/20 font-medium transition-all duration-200 text-xs sm:text-sm px-2 sm:px-3"
          >
            <Video className="h-3 w-3 sm:h-4 sm:w-4" />
            <span className="hidden sm:inline">All Videos</span>
            <span className="sm:hidden">All</span>
            <span>({filteredSummaries.length})</span>
          </TabsTrigger>
          <TabsTrigger 
            value="by-channel" 
            className="flex items-center gap-1 sm:gap-2 data-[state=active]:bg-primary data-[state=active]:text-primary-foreground data-[state=active]:shadow-md data-[state=active]:border data-[state=active]:border-primary/20 font-medium transition-all duration-200 text-xs sm:text-sm px-2 sm:px-3"
          >
            <Users className="h-3 w-3 sm:h-4 sm:w-4" />
            <span className="hidden sm:inline">By Channel</span>
            <span className="sm:hidden">Channels</span>
            <span>({summariesByChannel.length})</span>
          </TabsTrigger>
          <TabsTrigger 
            value="by-topic" 
            className="flex items-center gap-1 sm:gap-2 data-[state=active]:bg-primary data-[state=active]:text-primary-foreground data-[state=active]:shadow-md data-[state=active]:border data-[state=active]:border-primary/20 font-medium transition-all duration-200 text-xs sm:text-sm px-2 sm:px-3"
          >
            <Tag className="h-3 w-3 sm:h-4 sm:w-4" />
            <span className="hidden sm:inline">By Topic</span>
            <span className="sm:hidden">Topics</span>
            <span>({summariesByTopic.length})</span>
          </TabsTrigger>
        </TabsList>

        {/* All Videos Tab */}
        <TabsContent value="all" className="mt-0">
          <div className="bg-card border border-border rounded-lg shadow-lg">
            {/* Content Header - Mobile Optimized */}
            <div className="px-3 sm:px-6 py-3 sm:py-4 border-b border-border bg-muted/30">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <Video className="h-5 w-5 text-primary" />
                  <h3 className="font-semibold">All Videos</h3>
                  <Badge variant="secondary" className="ml-2">
                    {filteredSummaries.length} videos
                  </Badge>
                </div>
                <div className="text-sm text-muted-foreground">
                  Sorted by {sortField} ({sortDirection === "desc" ? "newest first" : "oldest first"})
                </div>
              </div>
            </div>
            
            {/* Content Area - Mobile Optimized */}
            <div className="p-3 sm:p-6">
              {viewMode === "card" ? (
                <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3 sm:gap-4">
                  {filteredSummaries.map((summary) => (
                    <SummaryCard key={summary.id} summary={summary} />
                  ))}
                </div>
              ) : (
                <div className="border rounded-lg">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead className="cursor-pointer" onClick={() => handleSort("title")}>
                          <div className="flex items-center gap-1">
                            Video <ArrowUpDown className="h-4 w-4" />
                          </div>
                        </TableHead>
                        <TableHead className="cursor-pointer" onClick={() => handleSort("channel")}>
                          <div className="flex items-center gap-1">
                            Channel <ArrowUpDown className="h-4 w-4" />
                          </div>
                        </TableHead>
                        <TableHead className="cursor-pointer" onClick={() => handleSort("duration")}>
                          <div className="flex items-center gap-1">
                            Duration <ArrowUpDown className="h-4 w-4" />
                          </div>
                        </TableHead>
                        <TableHead className="cursor-pointer" onClick={() => handleSort("views")}>
                          <div className="flex items-center gap-1">
                            Views <ArrowUpDown className="h-4 w-4" />
                          </div>
                        </TableHead>
                        <TableHead className="cursor-pointer" onClick={() => handleSort("date")}>
                          <div className="flex items-center gap-1">
                            Published <ArrowUpDown className="h-4 w-4" />
                          </div>
                        </TableHead>
                        <TableHead>Read Time</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {filteredSummaries.map((summary) => (
                        <SummaryTableRow key={summary.id} summary={summary} />
                      ))}
                    </TableBody>
                  </Table>
                </div>
              )}
            </div>
          </div>
        </TabsContent>

        {/* By Channel Tab */}
        <TabsContent value="by-channel" className="mt-0">
          <div className="bg-card border border-border rounded-lg shadow-lg">
            {/* Content Header */}
            <div className="px-6 py-4 border-b border-border bg-muted/30">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <Users className="h-5 w-5 text-primary" />
                  <h3 className="font-semibold">By Channel</h3>
                  <Badge variant="secondary" className="ml-2">
                    {summariesByChannel.length} channels
                  </Badge>
                </div>
                <div className="flex gap-2">
                  <Select value={channelFilter} onValueChange={setChannelFilter}>
                    <SelectTrigger className="w-64">
                      <SelectValue placeholder="Filter by channel..." />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All channels</SelectItem>
                      {channels.map((channel) => (
                        <SelectItem key={channel.id} value={channel.id.toString()}>
                          {channel.channelName}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </div>
            
            {/* Content Area */}
            <div className="p-6">
              {viewMode === "table" ? (
                <div className="border rounded-lg">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>Channel</TableHead>
                        <TableHead>Videos</TableHead>
                        <TableHead>Subscribers</TableHead>
                        <TableHead>Status</TableHead>
                        <TableHead>Last Updated</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {summariesByChannel.map(({ channel, summaries: channelSummaries }) => (
                        <React.Fragment key={channel.id}>
                          <TableRow 
                            className="cursor-pointer hover:bg-muted/50"
                            onClick={() => toggleChannelExpansion(channel.id)}
                          >
                            <TableCell>
                              <div className="flex items-center gap-3">
                                {expandedChannels[channel.id] ? (
                                  <ChevronDown className="h-4 w-4" />
                                ) : (
                                  <ChevronRight className="h-4 w-4" />
                                )}
                                <Avatar className="h-8 w-8">
                                  <AvatarImage src={channel.channelThumbnail || undefined} />
                                  <AvatarFallback>{channel.channelName.charAt(0)}</AvatarFallback>
                                </Avatar>
                                <div>
                                  <p className="font-medium">{channel.channelName}</p>
                                  <p className="text-xs text-muted-foreground">{channel.channelHandle}</p>
                                </div>
                              </div>
                            </TableCell>
                            <TableCell>
                              <Badge variant="outline">{channelSummaries.length}</Badge>
                            </TableCell>
                            <TableCell>{channel.subscriberCount}</TableCell>
                            <TableCell>
                              <Badge variant={channel.isActive ? "default" : "secondary"}>
                                {channel.isActive ? "Active" : "Inactive"}
                              </Badge>
                            </TableCell>
                            <TableCell>
                              {channel.lastChecked ? new Date(channel.lastChecked).toLocaleDateString() : 'Never'}
                            </TableCell>
                          </TableRow>
                          
                          {expandedChannels[channel.id] && channelSummaries.map((summary) => (
                            <TableRow key={`${channel.id}-${summary.id}`} className="bg-muted/25">
                              <TableCell className="pl-12">
                                <Link href={`/summary/${summary.id}`} className="flex items-center gap-3">
                                  <img 
                                    src={summary.videoThumbnail} 
                                    alt={summary.videoTitle}
                                    className="w-12 h-8 object-cover rounded"
                                  />
                                  <div className="min-w-0 flex-1">
                                    <p className="font-medium text-sm line-clamp-1">{summary.videoTitle}</p>
                                    <p className="text-xs text-muted-foreground">{summary.readTime}</p>
                                  </div>
                                </Link>
                              </TableCell>
                              <TableCell>{summary.videoDuration}</TableCell>
                              <TableCell>{summary.videoViews}</TableCell>
                              <TableCell>
                                {summary.videoPublishedAt ? new Date(summary.videoPublishedAt).toLocaleDateString() : 'N/A'}
                              </TableCell>
                              <TableCell>
                                <Badge variant="outline">{summary.readTime}</Badge>
                              </TableCell>
                            </TableRow>
                          ))}
                        </React.Fragment>
                      ))}
                    </TableBody>
                  </Table>
                </div>
              ) : (
                <div className="space-y-6">
                  {summariesByChannel.map(({ channel, summaries: channelSummaries }) => (
                    <Card key={channel.id}>
                      <CardHeader 
                        className="cursor-pointer hover:bg-muted/50"
                        onClick={() => toggleChannelExpansion(channel.id)}
                      >
                        <div className="flex items-center justify-between">
                          <div className="flex items-center gap-3">
                            {expandedChannels[channel.id] ? (
                              <ChevronDown className="h-5 w-5" />
                            ) : (
                              <ChevronRight className="h-5 w-5" />
                            )}
                            <Avatar className="h-10 w-10">
                              <AvatarImage src={channel.channelThumbnail || undefined} />
                              <AvatarFallback>{channel.channelName.charAt(0)}</AvatarFallback>
                            </Avatar>
                            <div>
                              <CardTitle className="text-lg">{channel.channelName}</CardTitle>
                              <p className="text-sm text-muted-foreground">{channel.channelHandle}</p>
                            </div>
                          </div>
                          <div className="flex items-center gap-2">
                            <Badge variant="outline">{channelSummaries.length} videos</Badge>
                            <Badge variant={channel.isActive ? "default" : "secondary"}>
                              {channel.isActive ? "Active" : "Inactive"}
                            </Badge>
                          </div>
                        </div>
                      </CardHeader>
                      
                      {expandedChannels[channel.id] && (
                        <CardContent className="pt-0">
                          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                            {channelSummaries.map((summary) => (
                              <SummaryCard key={summary.id} summary={summary} />
                            ))}
                          </div>
                        </CardContent>
                      )}
                    </Card>
                  ))}
                </div>
              )}
            </div>
          </div>
        </TabsContent>

        {/* By Topic Tab */}
        <TabsContent value="by-topic" className="mt-0">
          <div className="bg-card border border-border rounded-lg shadow-lg">
            {/* Content Header */}
            <div className="px-6 py-4 border-b border-border bg-muted/30">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <Tag className="h-5 w-5 text-primary" />
                  <h3 className="font-semibold">By Topic</h3>
                  <Badge variant="secondary" className="ml-2">
                    {summariesByTopic.length} topics
                  </Badge>
                </div>
                <div className="flex gap-2">
                  <Select value={topicFilter} onValueChange={setTopicFilter}>
                    <SelectTrigger className="w-64">
                      <SelectValue placeholder="Filter by topic..." />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All topics</SelectItem>
                      {topics.map((topic) => (
                        <SelectItem key={topic.id} value={topic.name}>
                          {topic.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </div>
            
            {/* Content Area */}
            <div className="p-6">
              <div className="space-y-4">
                {summariesByTopic.map(({ topic, summaries: topicSummaries }) => (
                  <Card key={topic.id}>
                    <CardHeader 
                      className="cursor-pointer hover:bg-muted/50"
                      onClick={() => toggleTopicExpansion(topic.id)}
                    >
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-3">
                          {expandedTopics[topic.id] ? (
                            <ChevronDown className="h-5 w-5" />
                          ) : (
                            <ChevronRight className="h-5 w-5" />
                          )}
                          <div className="flex items-center gap-2">
                            <div 
                              className="w-4 h-4 rounded-full"
                              style={{ backgroundColor: topic.colorHex || '#6b7280' }}
                            />
                            <CardTitle className="text-lg">{topic.name}</CardTitle>
                          </div>
                        </div>
                        <div className="flex items-center gap-2">
                          <Badge variant="outline">{topicSummaries.length} videos</Badge>
                          <Badge variant={topic.isActive ? "default" : "secondary"}>
                            {topic.topicType}
                          </Badge>
                        </div>
                      </div>
                      {topic.description && (
                        <p className="text-sm text-muted-foreground mt-2">{topic.description}</p>
                      )}
                    </CardHeader>
                    
                    {expandedTopics[topic.id] && (
                      <CardContent className="pt-0">
                        {viewMode === "card" ? (
                          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                            {topicSummaries.map((summary) => (
                              <SummaryCard key={summary.id} summary={summary} />
                            ))}
                          </div>
                        ) : (
                          <div className="border rounded-lg">
                            <Table>
                              <TableHeader>
                                <TableRow>
                                  <TableHead>Video</TableHead>
                                  <TableHead>Channel</TableHead>
                                  <TableHead>Duration</TableHead>
                                  <TableHead>Published</TableHead>
                                  <TableHead>Read Time</TableHead>
                                </TableRow>
                              </TableHeader>
                              <TableBody>
                                {topicSummaries.map((summary) => (
                                  <SummaryTableRow key={summary.id} summary={summary} />
                                ))}
                              </TableBody>
                            </Table>
                          </div>
                        )}
                      </CardContent>
                    )}
                  </Card>
                ))}
              </div>
            </div>
          </div>
        </TabsContent>
      </Tabs>

      {/* Empty State */}
      {filteredSummaries.length === 0 && (
        <div className="text-center py-12">
          <Video className="mx-auto h-12 w-12 text-muted-foreground mb-4" />
          <h3 className="text-lg font-semibold mb-2">No summaries found</h3>
          <p className="text-muted-foreground mb-4">
            {searchQuery ? "Try adjusting your search or filters" : "Start by creating your first summary"}
          </p>
          <Link href="/">
            <Button>Create Summary</Button>
          </Link>
        </div>
      )}
    </div>
  );
}