import { useEffect, useState } from "react";
import { useLocation, useRoute } from "wouter";
import { useQuery } from "@tanstack/react-query";
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { ArrowLeft, Clock, Eye, ExternalLink, Share, Copy, ThumbsUp, ThumbsDown } from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import { apiRequest } from "@/lib/queryClient";
import type { Summary } from "@shared/schema";

export default function VideoSummary() {
  const [, params] = useRoute("/summary/:id");
  const [, setLocation] = useLocation();
  const { toast } = useToast();
  const summaryId = params?.id;

  const { data: summary, isLoading } = useQuery<Summary>({
    queryKey: [`/api/summaries/${summaryId}`],
    enabled: !!summaryId,
  });

  const handleBack = () => {
    setLocation("/library");
  };

  const handleCopy = async () => {
    if (!summary) return;
    
    try {
      await navigator.clipboard.writeText(summary.summaryContent);
      toast({
        title: "Summary Copied",
        description: "Video summary copied to clipboard",
      });
    } catch (error) {
      toast({
        title: "Copy Failed",
        description: "Failed to copy summary to clipboard",
        variant: "destructive",
      });
    }
  };

  const handleShare = async () => {
    if (!summary) return;
    
    const shareUrl = `${window.location.origin}/summary/${summary.id}`;
    
    if (navigator.share) {
      try {
        await navigator.share({
          title: summary.videoTitle,
          text: `Check out this video summary: ${summary.videoTitle}`,
          url: shareUrl,
        });
      } catch (error) {
        // User cancelled sharing
      }
    } else {
      // Fallback to copying URL
      try {
        await navigator.clipboard.writeText(shareUrl);
        toast({
          title: "Link Copied",
          description: "Summary link copied to clipboard",
        });
      } catch (error) {
        toast({
          title: "Share Failed",
          description: "Failed to copy share link",
          variant: "destructive",
        });
      }
    }
  };

  const handleFeedback = async (rating: 'helpful' | 'not_helpful') => {
    if (!summary) return;
    
    try {
      await apiRequest("POST", `/api/summaries/${summary.id}/feedback`, { rating });
      toast({
        title: "Feedback Recorded",
        description: "Thank you for your feedback!",
      });
    } catch (error) {
      toast({
        title: "Feedback Failed",
        description: "Failed to record feedback",
        variant: "destructive",
      });
    }
  };

  if (isLoading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="max-w-4xl mx-auto">
          <div className="animate-pulse space-y-4">
            <div className="h-8 bg-gray-200 rounded w-1/4"></div>
            <div className="h-32 bg-gray-200 rounded"></div>
            <div className="h-64 bg-gray-200 rounded"></div>
          </div>
        </div>
      </div>
    );
  }

  if (!summary) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="max-w-4xl mx-auto text-center">
          <h1 className="text-2xl font-bold mb-4">Summary Not Found</h1>
          <p className="text-muted-foreground mb-6">
            The requested video summary could not be found.
          </p>
          <Button onClick={handleBack}>
            <ArrowLeft className="w-4 h-4 mr-2" />
            Back to Library
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="max-w-4xl mx-auto">
        {/* Header */}
        <div className="flex items-center justify-between mb-6">
          <Button variant="ghost" onClick={handleBack}>
            <ArrowLeft className="w-4 h-4 mr-2" />
            Back to Library
          </Button>
          <div className="flex items-center gap-2">
            <Button variant="outline" size="sm" onClick={handleCopy}>
              <Copy className="w-4 h-4 mr-2" />
              Copy
            </Button>
            <Button variant="outline" size="sm" onClick={handleShare}>
              <Share className="w-4 h-4 mr-2" />
              Share
            </Button>
          </div>
        </div>

        {/* Video Info */}
        <Card className="mb-6">
          <CardContent className="p-6">
            <div className="flex items-start gap-4">
              <img 
                src={summary.videoThumbnail} 
                alt={summary.videoTitle}
                className="w-32 h-24 object-cover rounded flex-shrink-0"
              />
              <div className="flex-1 min-w-0">
                <h1 className="text-2xl font-bold mb-2 line-clamp-2">
                  {summary.videoTitle}
                </h1>
                <p className="text-muted-foreground mb-3">
                  {summary.videoChannel} • {summary.videoDuration} • {summary.videoViews}
                  {summary.videoPublishedAt && (
                    <> • Published {new Date(summary.videoPublishedAt).toLocaleDateString()}</>
                  )}
                </p>
                <div className="flex items-center gap-3 mb-3">
                  <Badge variant="secondary">
                    <Clock className="w-3 h-3 mr-1" />
                    {summary.readTime}
                  </Badge>
                  <Badge variant="outline">
                    <Eye className="w-3 h-3 mr-1" />
                    {summary.timeSaved} saved
                  </Badge>
                  {summary.isMonitored && (
                    <Badge variant="default">Auto-generated</Badge>
                  )}
                </div>
                <div className="flex items-center gap-2">
                  <Button 
                    variant="outline" 
                    size="sm"
                    onClick={() => window.open(`https://youtu.be/${summary.videoId}`, '_blank')}
                  >
                    <ExternalLink className="w-4 h-4 mr-2" />
                    Watch Video
                  </Button>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Summary Content */}
        <Card className="mb-6">
          <CardContent className="p-6">
            <Tabs defaultValue="summary" className="w-full">
              <TabsList className="grid w-full grid-cols-2">
                <TabsTrigger value="summary">AI Summary</TabsTrigger>
                <TabsTrigger value="transcript">Transcript</TabsTrigger>
              </TabsList>
              
              <TabsContent value="summary" className="mt-6">
                <div 
                  className="prose prose-slate max-w-none"
                  dangerouslySetInnerHTML={{ __html: summary.summaryContent }}
                />
              </TabsContent>
              
              <TabsContent value="transcript" className="mt-6">
                {summary.videoTranscript ? (
                  <div className="whitespace-pre-wrap text-sm text-muted-foreground">
                    {summary.videoTranscript}
                  </div>
                ) : (
                  <div className="text-center py-8 text-muted-foreground">
                    <p>No transcript available for this video.</p>
                  </div>
                )}
              </TabsContent>
            </Tabs>
          </CardContent>
        </Card>

        {/* Feedback */}
        <Card>
          <CardContent className="p-6">
            <h3 className="font-semibold mb-4">Was this summary helpful?</h3>
            <div className="flex items-center gap-2">
              <Button 
                variant="outline" 
                size="sm"
                onClick={() => handleFeedback('helpful')}
              >
                <ThumbsUp className="w-4 h-4 mr-2" />
                Helpful
              </Button>
              <Button 
                variant="outline" 
                size="sm"
                onClick={() => handleFeedback('not_helpful')}
              >
                <ThumbsDown className="w-4 h-4 mr-2" />
                Not Helpful
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}