import { useState, useEffect } from "react";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { useAuth } from "@/hooks/useAuth";
import { useToast } from "@/hooks/use-toast";
import { apiRequest } from "@/lib/queryClient";
import { isUnauthorizedError } from "@/lib/authUtils";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Textarea } from "@/components/ui/textarea";
import { 
  Plus, 
  Edit, 
  Trash2, 
  Tag, 
  Folder, 
  <PERSON>h,
  <PERSON>,
  Filter,
  TreePine,
  Loader2,
  Rss
} from "lucide-react";

interface Topic {
  id: number;
  name: string;
  description: string | null;
  parentTopicId: number | null;
  topicType: 'auto_extracted' | 'user_defined' | 'playlist';
  colorHex: string | null;
  isActive: boolean;
  summaryCount: number;
  createdAt: string;
}

interface Channel {
  id: number;
  channelName: string;
  channelThumbnail: string;
}

interface Summary {
  id: number;
  videoTitle: string;
  videoThumbnail: string;
  createdAt: string;
}

export default function Topics() {
  const { user: authUser, isLoading: authLoading } = useAuth();
  const { toast } = useToast();
  const queryClient = useQueryClient();
  
  const [isCreatingTopic, setIsCreatingTopic] = useState(false);
  const [editingTopic, setEditingTopic] = useState<Topic | null>(null);
  const [selectedTopic, setSelectedTopic] = useState<Topic | null>(null);
  const [searchQuery, setSearchQuery] = useState("");
  const [filterType, setFilterType] = useState<string>("all");
  
  const [newTopic, setNewTopic] = useState({
    name: "",
    description: "",
    parentTopicId: null as number | null,
    colorHex: "#3b82f6",
    topicType: "user_defined" as const
  });

  // Redirect to login if not authenticated
  useEffect(() => {
    if (!authLoading && !authUser) {
      toast({
        title: "Unauthorized",
        description: "You are logged out. Logging in again...",
        variant: "destructive",
      });
      setTimeout(() => {
        window.location.href = "/api/login";
      }, 500);
      return;
    }
  }, [authUser, authLoading, toast]);

  // Fetch topics
  const { data: topics = [], isLoading: topicsLoading } = useQuery<Topic[]>({
    queryKey: ["/api/topics"],
    enabled: !!authUser,
  });

  // Create topic mutation
  const createTopicMutation = useMutation({
    mutationFn: async (topic: typeof newTopic) => {
      return await apiRequest("POST", "/api/topics", topic);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["/api/topics"] });
      setIsCreatingTopic(false);
      setNewTopic({
        name: "",
        description: "",
        parentTopicId: null,
        colorHex: "#3b82f6",
        topicType: "user_defined"
      });
      toast({
        title: "Topic Created",
        description: "Topic created successfully",
      });
    },
    onError: (error) => {
      if (isUnauthorizedError(error)) {
        toast({
          title: "Unauthorized",
          description: "You are logged out. Logging in again...",
          variant: "destructive",
        });
        setTimeout(() => {
          window.location.href = "/api/login";
        }, 500);
        return;
      }
      toast({
        title: "Error",
        description: "Failed to create topic",
        variant: "destructive",
      });
    },
  });

  // Update topic mutation
  const updateTopicMutation = useMutation({
    mutationFn: async ({ id, updates }: { id: number; updates: Partial<Topic> }) => {
      return await apiRequest("PUT", `/api/topics/${id}`, updates);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["/api/topics"] });
      setEditingTopic(null);
      toast({
        title: "Topic Updated",
        description: "Topic updated successfully",
      });
    },
    onError: (error) => {
      if (isUnauthorizedError(error)) {
        toast({
          title: "Unauthorized",
          description: "You are logged out. Logging in again...",
          variant: "destructive",
        });
        setTimeout(() => {
          window.location.href = "/api/login";
        }, 500);
        return;
      }
      toast({
        title: "Error",
        description: "Failed to update topic",
        variant: "destructive",
      });
    },
  });

  // Delete topic mutation
  const deleteTopicMutation = useMutation({
    mutationFn: async (topicId: number) => {
      return await apiRequest("DELETE", `/api/topics/${topicId}`);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["/api/topics"] });
      toast({
        title: "Topic Deleted",
        description: "Topic deleted successfully",
      });
    },
    onError: (error) => {
      if (isUnauthorizedError(error)) {
        toast({
          title: "Unauthorized",
          description: "You are logged out. Logging in again...",
          variant: "destructive",
        });
        setTimeout(() => {
          window.location.href = "/api/login";
        }, 500);
        return;
      }
      toast({
        title: "Error",
        description: "Failed to delete topic",
        variant: "destructive",
      });
    },
  });

  // Fetch topic details when selected
  const { data: topicChannels = [] } = useQuery<Channel[]>({
    queryKey: [`/api/topics/${selectedTopic?.id}/channels`],
    enabled: !!selectedTopic,
  });

  const { data: topicSummaries = [] } = useQuery<Summary[]>({
    queryKey: [`/api/topics/${selectedTopic?.id}/summaries`],
    enabled: !!selectedTopic,
  });

  // Filter and organize topics
  const filteredTopics = topics.filter(topic => {
    const matchesSearch = topic.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         (topic.description?.toLowerCase().includes(searchQuery.toLowerCase()) ?? false);
    const matchesFilter = filterType === "all" || topic.topicType === filterType;
    return matchesSearch && matchesFilter;
  });

  const topLevelTopics = filteredTopics.filter(topic => !topic.parentTopicId);
  const getChildTopics = (parentId: number) => 
    filteredTopics.filter(topic => topic.parentTopicId === parentId);

  const handleCreateTopic = () => {
    if (!newTopic.name.trim()) return;
    createTopicMutation.mutate(newTopic);
  };

  const handleUpdateTopic = () => {
    if (!editingTopic) return;
    updateTopicMutation.mutate({ id: editingTopic.id, updates: editingTopic });
  };

  const getTopicIcon = (topicType: string) => {
    switch (topicType) {
      case 'playlist': return <Folder className="w-4 h-4" />;
      case 'auto_extracted': return <Hash className="w-4 h-4" />;
      default: return <Tag className="w-4 h-4" />;
    }
  };

  if (authLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <Loader2 className="w-8 h-8 animate-spin" />
      </div>
    );
  }

  return (
    <div className="container mx-auto p-6 max-w-6xl">
      
      {/* Action Bar */}
      <div className="flex items-center justify-end mb-6">
        <Dialog open={isCreatingTopic} onOpenChange={setIsCreatingTopic}>
            <DialogTrigger asChild>
              <Button>
                <Plus className="w-4 h-4 mr-2" />
                Create Topic
              </Button>
            </DialogTrigger>
            <DialogContent>
              <DialogHeader>
                <DialogTitle>Create New Topic</DialogTitle>
                <DialogDescription>
                  Create a topic to organize your channels and summaries. Categories are general organizational groups, while Collections are curated playlists with specific themes.
                </DialogDescription>
              </DialogHeader>
              <div className="space-y-4">
                <Input
                  placeholder="Topic name"
                  value={newTopic.name}
                  onChange={(e) => setNewTopic({ ...newTopic, name: e.target.value })}
                />
                <Textarea
                  placeholder="Description (optional)"
                  value={newTopic.description}
                  onChange={(e) => setNewTopic({ ...newTopic, description: e.target.value })}
                />
                <Select 
                  value={newTopic.topicType} 
                  onValueChange={(value: any) => setNewTopic({ ...newTopic, topicType: value })}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="user_defined">Category</SelectItem>
                    <SelectItem value="playlist">Curated Collection</SelectItem>
                  </SelectContent>
                </Select>
                <div className="flex items-center space-x-2">
                  <label className="text-sm">Color:</label>
                  <input
                    type="color"
                    value={newTopic.colorHex}
                    onChange={(e) => setNewTopic({ ...newTopic, colorHex: e.target.value })}
                    className="w-12 h-8 rounded border"
                  />
                </div>
                <div className="flex justify-end gap-2">
                  <Button variant="outline" onClick={() => setIsCreatingTopic(false)}>
                    Cancel
                  </Button>
                  <Button 
                    onClick={handleCreateTopic}
                    disabled={!newTopic.name.trim() || createTopicMutation.isPending}
                  >
                    {createTopicMutation.isPending ? "Creating..." : "Create Topic"}
                  </Button>
                </div>
              </div>
            </DialogContent>
          </Dialog>
        </div>

        {/* Search and Filters */}
        <div className="flex flex-col sm:flex-row gap-4">
          <div className="flex-1 relative">
            <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Search topics..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-10"
            />
          </div>
          
          <Select value={filterType} onValueChange={setFilterType}>
            <SelectTrigger className="w-48">
              <SelectValue placeholder="Filter by type" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Types</SelectItem>
              <SelectItem value="user_defined">Categories</SelectItem>
              <SelectItem value="playlist">Curated Collections</SelectItem>
              <SelectItem value="auto_extracted">Auto-extracted</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>

      {/* Topics Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        
        {/* Topics List */}
        <div className="lg:col-span-2 space-y-4">
          {topicsLoading ? (
            Array.from({ length: 6 }).map((_, i) => (
              <Card key={i} className="animate-pulse">
                <CardContent className="p-6">
                  <div className="h-4 bg-gray-200 rounded mb-2"></div>
                  <div className="h-3 bg-gray-200 rounded w-2/3"></div>
                </CardContent>
              </Card>
            ))
          ) : topLevelTopics.length === 0 ? (
            <Card className="p-12 text-center">
              <TreePine className="w-12 h-12 mx-auto mb-4 text-muted-foreground" />
              <div className="text-muted-foreground mb-4">
                No topics found. Create your first topic to start organizing your content.
              </div>
              <Button onClick={() => setIsCreatingTopic(true)}>
                <Plus className="w-4 h-4 mr-2" />
                Create Your First Topic
              </Button>
            </Card>
          ) : (
            topLevelTopics.map((topic) => (
              <div key={topic.id} className="space-y-2">
                <Card 
                  className={`cursor-pointer transition-all hover:shadow-md ${
                    selectedTopic?.id === topic.id ? 'ring-2 ring-primary' : ''
                  }`}
                  onClick={() => setSelectedTopic(topic)}
                >
                  <CardContent className="p-6">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-3 flex-1">
                        <div 
                          className="w-4 h-4 rounded-full flex-shrink-0" 
                          style={{ backgroundColor: topic.colorHex || '#3b82f6' }}
                        />
                        {getTopicIcon(topic.topicType)}
                        <div className="flex-1 min-w-0">
                          <h3 className="font-medium truncate">{topic.name}</h3>
                          {topic.description && (
                            <p className="text-sm text-muted-foreground truncate">{topic.description}</p>
                          )}
                          <div className="flex items-center gap-2 mt-2">
                            <Badge variant="outline" className="text-xs">
                              {topic.topicType === 'user_defined' ? 'Category' : 
                               topic.topicType === 'playlist' ? 'Collection' : 
                               'Auto-extracted'}
                            </Badge>
                            <Badge variant="secondary" className="text-xs">
                              {topic.summaryCount} summaries
                            </Badge>
                          </div>
                        </div>
                      </div>
                      
                      <div className="flex items-center gap-2 group-hover:opacity-100 transition-opacity">
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={(e) => {
                            e.stopPropagation();
                            window.open(`/rss/topic/${topic.id}`, '_blank');
                          }}
                          className="text-blue-600 hover:text-blue-800"
                          title="RSS Feed"
                        >
                          <Rss className="w-4 h-4" />
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={(e) => {
                            e.stopPropagation();
                            setEditingTopic(topic);
                          }}
                        >
                          <Edit className="w-4 h-4" />
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={(e) => {
                            e.stopPropagation();
                            deleteTopicMutation.mutate(topic.id);
                          }}
                          className="text-red-500 hover:text-red-700"
                        >
                          <Trash2 className="w-4 h-4" />
                        </Button>
                      </div>
                    </div>
                  </CardContent>
                </Card>
                
                {/* Child Topics */}
                {getChildTopics(topic.id).map((childTopic) => (
                  <Card 
                    key={childTopic.id}
                    className={`ml-8 cursor-pointer transition-all hover:shadow-md ${
                      selectedTopic?.id === childTopic.id ? 'ring-2 ring-primary' : ''
                    }`}
                    onClick={() => setSelectedTopic(childTopic)}
                  >
                    <CardContent className="p-4">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-3 flex-1">
                          <div 
                            className="w-3 h-3 rounded-full flex-shrink-0" 
                            style={{ backgroundColor: childTopic.colorHex || '#3b82f6' }}
                          />
                          {getTopicIcon(childTopic.topicType)}
                          <div className="flex-1 min-w-0">
                            <h4 className="text-sm font-medium truncate">{childTopic.name}</h4>
                            <Badge variant="secondary" className="text-xs mt-1">
                              {childTopic.summaryCount} summaries
                            </Badge>
                          </div>
                        </div>
                        
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={(e) => {
                            e.stopPropagation();
                            window.open(`/rss/topic/${childTopic.id}`, '_blank');
                          }}
                          className="text-blue-600 hover:text-blue-800"
                          title="RSS Feed"
                        >
                          <Rss className="w-3 h-3" />
                        </Button>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            ))
          )}
        </div>

        {/* Topic Details Panel */}
        <div className="space-y-6">
          {selectedTopic ? (
            <>
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <div 
                      className="w-4 h-4 rounded-full" 
                      style={{ backgroundColor: selectedTopic.colorHex || '#3b82f6' }}
                    />
                    {selectedTopic.name}
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  {selectedTopic.description && (
                    <p className="text-sm text-muted-foreground">{selectedTopic.description}</p>
                  )}
                  <div className="flex items-center gap-2">
                    <Badge variant="outline">{selectedTopic.topicType.replace('_', ' ')}</Badge>
                    <Badge variant="secondary">{selectedTopic.summaryCount} summaries</Badge>
                  </div>
                  
                  {/* RSS Feed Section */}
                  <div className="pt-4 border-t">
                    <div className="flex items-center justify-between">
                      <span className="text-sm font-medium">RSS Feed</span>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => window.open(`/rss/topic/${selectedTopic.id}`, '_blank')}
                        className="text-blue-600 hover:text-blue-800"
                      >
                        <Rss className="w-4 h-4 mr-2" />
                        Subscribe
                      </Button>
                    </div>
                    <p className="text-xs text-muted-foreground mt-1">
                      Get latest summaries from all channels in this topic
                    </p>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Channels ({topicChannels.length})</CardTitle>
                </CardHeader>
                <CardContent>
                  {topicChannels.length === 0 ? (
                    <p className="text-sm text-muted-foreground">No channels assigned to this topic</p>
                  ) : (
                    <div className="space-y-2">
                      {topicChannels.slice(0, 5).map((channel) => (
                        <div key={channel.id} className="flex items-center space-x-2 text-sm">
                          <img 
                            src={channel.channelThumbnail} 
                            alt={channel.channelName}
                            className="w-6 h-6 rounded-full"
                          />
                          <span className="truncate">{channel.channelName}</span>
                        </div>
                      ))}
                      {topicChannels.length > 5 && (
                        <p className="text-xs text-muted-foreground">
                          +{topicChannels.length - 5} more channels
                        </p>
                      )}
                    </div>
                  )}
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Recent Summaries ({topicSummaries.length})</CardTitle>
                </CardHeader>
                <CardContent>
                  {topicSummaries.length === 0 ? (
                    <p className="text-sm text-muted-foreground">No summaries in this topic</p>
                  ) : (
                    <div className="space-y-2">
                      {topicSummaries.slice(0, 3).map((summary) => (
                        <div key={summary.id} className="flex items-center space-x-2 text-sm">
                          <img 
                            src={summary.videoThumbnail} 
                            alt={summary.videoTitle}
                            className="w-8 h-6 object-cover rounded"
                          />
                          <span className="truncate flex-1">{summary.videoTitle}</span>
                        </div>
                      ))}
                      {topicSummaries.length > 3 && (
                        <p className="text-xs text-muted-foreground">
                          +{topicSummaries.length - 3} more summaries
                        </p>
                      )}
                    </div>
                  )}
                </CardContent>
              </Card>
            </>
          ) : (
            <Card>
              <CardContent className="p-12 text-center">
                <TreePine className="w-12 h-12 mx-auto mb-4 text-muted-foreground" />
                <p className="text-muted-foreground">Select a topic to view details</p>
              </CardContent>
            </Card>
          )}
        </div>
      </div>

      {/* Edit Topic Dialog */}
      {editingTopic && (
        <Dialog open={!!editingTopic} onOpenChange={() => setEditingTopic(null)}>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Edit Topic</DialogTitle>
              <DialogDescription>
                Update topic information and settings
              </DialogDescription>
            </DialogHeader>
            <div className="space-y-4">
              <Input
                placeholder="Topic name"
                value={editingTopic.name}
                onChange={(e) => setEditingTopic({ ...editingTopic, name: e.target.value })}
              />
              <Textarea
                placeholder="Description (optional)"
                value={editingTopic.description || ""}
                onChange={(e) => setEditingTopic({ ...editingTopic, description: e.target.value })}
              />
              <div className="flex items-center space-x-2">
                <label className="text-sm">Color:</label>
                <input
                  type="color"
                  value={editingTopic.colorHex || "#3b82f6"}
                  onChange={(e) => setEditingTopic({ ...editingTopic, colorHex: e.target.value })}
                  className="w-12 h-8 rounded border"
                />
              </div>
              <div className="flex justify-end gap-2">
                <Button variant="outline" onClick={() => setEditingTopic(null)}>
                  Cancel
                </Button>
                <Button 
                  onClick={handleUpdateTopic}
                  disabled={updateTopicMutation.isPending}
                >
                  {updateTopicMutation.isPending ? "Updating..." : "Update Topic"}
                </Button>
              </div>
            </div>
          </DialogContent>
        </Dialog>
      )}
    </div>
  );
}