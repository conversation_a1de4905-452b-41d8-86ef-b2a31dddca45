import { useState, useEffect } from "react";
import { useAuth } from "@/hooks/useAuth";
import { useToast } from "@/hooks/use-toast";
import { useQuery } from "@tanstack/react-query";
import { useLocation } from "wouter";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import ContentCard from "@/components/ui/content-card";
import { 
  Search, 
  Filter, 
  Calendar, 
  Clock, 
  Eye, 
  Share, 
  Copy,
  Download,
  Star,
  Grid3X3,
  List,
  SortAsc,
  SortDesc,
  Loader2,
  PlayCircle,
  Rss,
  BookOpen,
  TrendingUp,
  BarChart3
} from "lucide-react";
import { Link } from "wouter";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";

interface Summary {
  id: number;
  videoId: string;
  videoTitle: string;
  videoChannel: string;
  videoThumbnail: string;
  videoDuration: string;
  videoViews: string;
  summaryContent: string;
  videoTranscript: string;
  readTime: string;
  timeSaved: string;
  createdAt: string;
  channelId?: number;
  isMonitored?: boolean;
}

interface Channel {
  id: number;
  channelName: string;
  channelThumbnail: string;
}

export default function EnhancedLibrary() {
  const { user: authUser, isLoading: authLoading } = useAuth();
  const { toast } = useToast();
  const [, setLocation] = useLocation();
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedChannel, setSelectedChannel] = useState<string>("");
  const [sortBy, setSortBy] = useState<string>("recent");
  const [viewMode, setViewMode] = useState<"grid" | "list">("grid");
  const [selectedSummary, setSelectedSummary] = useState<Summary | null>(null);
  const [activeFilter, setActiveFilter] = useState<"all" | "manual" | "monitored">("all");

  // Fetch summaries
  const { data: summaries = [], isLoading: summariesLoading } = useQuery<Summary[]>({
    queryKey: ["/api/summaries"],
    enabled: !!authUser,
  });

  // Fetch channels for filtering
  const { data: channels = [] } = useQuery<Channel[]>({
    queryKey: ["/api/channels"],
    enabled: !!authUser,
  });

  // Vector search when search query exists
  const { data: searchResults = [], isLoading: searchLoading } = useQuery<Summary[]>({
    queryKey: ["/api/vector-search", { q: searchQuery, channels: selectedChannel }],
    enabled: !!authUser && searchQuery.length > 2
  });

  // Filter and sort summaries
  const filteredSummaries = (() => {
    let filtered = searchQuery.length > 2 ? searchResults : summaries;
    
    // Filter by type (manual vs monitored)
    if (activeFilter === "manual") {
      filtered = filtered.filter(s => !s.isMonitored);
    } else if (activeFilter === "monitored") {
      filtered = filtered.filter(s => s.isMonitored);
    }
    
    // Filter by channel
    if (selectedChannel && selectedChannel !== "all" && selectedChannel !== "") {
      filtered = filtered.filter(s => s.channelId?.toString() === selectedChannel);
    }

    // Sort summaries
    switch (sortBy) {
      case "recent":
        return [...filtered].sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime());
      case "oldest":
        return [...filtered].sort((a, b) => new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime());
      case "title":
        return [...filtered].sort((a, b) => a.videoTitle.localeCompare(b.videoTitle));
      case "channel":
        return [...filtered].sort((a, b) => a.videoChannel.localeCompare(b.videoChannel));
      default:
        return filtered;
    }
  })();

  const handleCopySummary = async (summary: Summary) => {
    try {
      const tempDiv = document.createElement('div');
      tempDiv.innerHTML = summary.summaryContent;
      const textContent = tempDiv.textContent || tempDiv.innerText || '';
      
      await navigator.clipboard.writeText(textContent);
      toast({
        title: "Copied!",
        description: "Summary copied to clipboard",
      });
    } catch (error) {
      toast({
        title: "Failed to copy",
        description: "Could not copy summary to clipboard",
        variant: "destructive",
      });
    }
  };

  const handleShareSummary = (summary: Summary) => {
    const shareUrl = `${window.location.origin}/summary/${summary.id}`;
    navigator.clipboard.writeText(shareUrl);
    toast({
      title: "Link copied!",
      description: "Share link copied to clipboard",
    });
  };

  const handleExportSummary = (summary: Summary) => {
    const exportData = {
      title: summary.videoTitle,
      channel: summary.videoChannel,
      duration: summary.videoDuration,
      views: summary.videoViews,
      createdAt: summary.createdAt,
      readTime: summary.readTime,
      timeSaved: summary.timeSaved,
      content: summary.summaryContent,
      videoUrl: `https://youtu.be/${summary.videoId}`,
      transcript: summary.videoTranscript
    };

    const blob = new Blob([JSON.stringify(exportData, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `${summary.videoTitle.replace(/[^a-zA-Z0-9]/g, '_')}_summary.json`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);

    toast({
      title: "Export complete",
      description: `Summary exported as JSON file`,
    });
  };

  const handleBulkExport = () => {
    const exportData = filteredSummaries.map(summary => ({
      title: summary.videoTitle,
      channel: summary.videoChannel,
      duration: summary.videoDuration,
      views: summary.videoViews,
      createdAt: summary.createdAt,
      readTime: summary.readTime,
      timeSaved: summary.timeSaved,
      content: summary.summaryContent,
      videoUrl: `https://youtu.be/${summary.videoId}`,
      transcript: summary.videoTranscript
    }));

    const blob = new Blob([JSON.stringify(exportData, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `video_summaries_export_${new Date().toISOString().split('T')[0]}.json`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);

    toast({
      title: "Bulk export complete",
      description: `${filteredSummaries.length} summaries exported`,
    });
  };

  // Library statistics
  const libraryStats = {
    total: summaries.length,
    manual: summaries.filter(s => !s.isMonitored).length,
    monitored: summaries.filter(s => s.isMonitored).length,
    thisWeek: summaries.filter(s => {
      const weekAgo = new Date();
      weekAgo.setDate(weekAgo.getDate() - 7);
      return new Date(s.createdAt) > weekAgo;
    }).length,
    totalTimeSaved: summaries.reduce((total, s) => {
      const minutes = parseInt(s.timeSaved?.match(/\d+/)?.[0] || "0");
      return total + minutes;
    }, 0)
  };

  if (authLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <Loader2 className="h-8 w-8 animate-spin" />
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Library Header with Statistics */}
      <div className="grid lg:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">Total Summaries</p>
                <p className="text-2xl font-bold">{libraryStats.total}</p>
              </div>
              <BookOpen className="h-8 w-8 text-blue-500" />
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">This Week</p>
                <p className="text-2xl font-bold">{libraryStats.thisWeek}</p>
              </div>
              <TrendingUp className="h-8 w-8 text-green-500" />
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">Time Saved</p>
                <p className="text-2xl font-bold">{libraryStats.totalTimeSaved}m</p>
              </div>
              <Clock className="h-8 w-8 text-purple-500" />
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">Monitored</p>
                <p className="text-2xl font-bold">{libraryStats.monitored}</p>
              </div>
              <BarChart3 className="h-8 w-8 text-orange-500" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Search and Filters */}
      <Card>
        <CardContent className="p-6">
          <div className="flex flex-col lg:flex-row gap-4 items-start lg:items-center justify-between">
            <div className="flex flex-1 gap-4 items-center">
              <div className="relative flex-1 max-w-md">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Search summaries..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="pl-10"
                />
              </div>
              
              <Select value={selectedChannel} onValueChange={setSelectedChannel}>
                <SelectTrigger className="w-48">
                  <SelectValue placeholder="All channels" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All channels</SelectItem>
                  {channels.map((channel) => (
                    <SelectItem key={channel.id} value={channel.id.toString()}>
                      {channel.channelName}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="flex gap-2 items-center">
              <Tabs value={activeFilter} onValueChange={(v) => setActiveFilter(v as any)}>
                <TabsList>
                  <TabsTrigger value="all">All</TabsTrigger>
                  <TabsTrigger value="manual">Manual</TabsTrigger>
                  <TabsTrigger value="monitored">Auto</TabsTrigger>
                </TabsList>
              </Tabs>

              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="outline" size="sm">
                    <SortAsc className="h-4 w-4 mr-2" />
                    Sort
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent>
                  <DropdownMenuItem onClick={() => setSortBy("recent")}>
                    Most Recent
                  </DropdownMenuItem>
                  <DropdownMenuItem onClick={() => setSortBy("oldest")}>
                    Oldest First
                  </DropdownMenuItem>
                  <DropdownMenuItem onClick={() => setSortBy("title")}>
                    Title A-Z
                  </DropdownMenuItem>
                  <DropdownMenuItem onClick={() => setSortBy("channel")}>
                    Channel Name
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>

              <div className="flex rounded-md border">
                <Button
                  variant={viewMode === "grid" ? "default" : "ghost"}
                  size="sm"
                  onClick={() => setViewMode("grid")}
                  className="rounded-r-none"
                >
                  <Grid3X3 className="h-4 w-4" />
                </Button>
                <Button
                  variant={viewMode === "list" ? "default" : "ghost"}
                  size="sm"
                  onClick={() => setViewMode("list")}
                  className="rounded-l-none"
                >
                  <List className="h-4 w-4" />
                </Button>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Loading State */}
      {(summariesLoading || searchLoading) && (
        <div className="flex items-center justify-center h-32">
          <Loader2 className="h-8 w-8 animate-spin" />
        </div>
      )}

      {/* Content Grid/List */}
      {!summariesLoading && !searchLoading && (
        <div className={viewMode === "grid" 
          ? "grid md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4" 
          : "space-y-4"
        }>
          {filteredSummaries.map((summary) => (
            <ContentCard
              key={summary.id}
              variant="summary"
              title={summary.videoTitle}
              subtitle={summary.videoChannel}
              thumbnail={summary.videoThumbnail}
              duration={summary.videoDuration}
              metadata={{
                readTime: summary.readTime,
                timeSaved: summary.timeSaved,
                views: summary.videoViews
              }}
              status={summary.isMonitored ? "monitored" : undefined}
              actions={{
                primary: {
                  label: "View Summary",
                  onClick: () => {
                    setLocation(`/summary/${summary.id}`);
                  },
                  icon: Eye
                },
                secondary: [
                  { 
                    label: "Copy", 
                    onClick: () => handleCopySummary(summary),
                    icon: Copy 
                  },
                  { 
                    label: "Share", 
                    onClick: () => handleShareSummary(summary),
                    icon: Share 
                  },
                  { 
                    label: "Download", 
                    onClick: () => handleExportSummary(summary),
                    icon: Download 
                  }
                ]
              }}
              className={viewMode === "list" ? "flex-row" : ""}
            />
          ))}
        </div>
      )}

      {/* Empty State */}
      {!summariesLoading && !searchLoading && filteredSummaries.length === 0 && (
        <Card className="border-dashed">
          <CardContent className="p-12 text-center">
            <BookOpen className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
            <h3 className="text-lg font-semibold mb-2">
              {searchQuery ? "No summaries found" : "Your library is empty"}
            </h3>
            <p className="text-muted-foreground mb-6">
              {searchQuery 
                ? "Try adjusting your search terms or filters"
                : "Start by creating your first video summary or setting up channel monitoring"
              }
            </p>
            {!searchQuery && (
              <div className="flex justify-center gap-4">
                <Link href="/">
                  <Button>Create Summary</Button>
                </Link>
                <Link href="/channels">
                  <Button variant="outline">Add Channels</Button>
                </Link>
              </div>
            )}
          </CardContent>
        </Card>
      )}

      {/* RSS Feed Info */}
      <Card className="bg-blue-50 dark:bg-blue-950/20 border-blue-200 dark:border-blue-800">
        <CardContent className="p-4">
          <div className="flex items-center gap-3">
            <Rss className="h-5 w-5 text-blue-600" />
            <div className="flex-1">
              <h4 className="font-medium text-blue-900 dark:text-blue-100">RSS Feed Available</h4>
              <p className="text-sm text-blue-700 dark:text-blue-300">
                Access your complete library via RSS at: <code className="bg-blue-100 dark:bg-blue-900 px-1 rounded">/rss/library</code>
              </p>
            </div>
            <Button 
              variant="outline" 
              size="sm"
              onClick={() => navigator.clipboard.writeText(`${window.location.origin}/rss/library`)}
            >
              Copy RSS URL
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}