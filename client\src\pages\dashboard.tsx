import { useEffect, useState } from "react";
import { useAuth } from "@/hooks/useAuth";
import { useToast } from "@/hooks/use-toast";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { apiRequest } from "@/lib/queryClient";
import { isUnauthorizedError } from "@/lib/authUtils";
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Progress } from "@/components/ui/progress";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { 
  Video, 
  Plus, 
  Copy, 
  Share, 
  Bookmark, 
  Clock, 
  Lightbulb, 
  Star,
  ThumbsUp,
  ThumbsDown,
  Sparkles,
  LogOut,
  AlertCircle,
  Loader2,
  ChevronDown,
  Download,
  MonitorSpeaker
} from "lucide-react";
import { Link } from "wouter";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";

interface Summary {
  id: number;
  videoId: string;
  videoTitle: string;
  videoChannel: string;
  videoThumbnail: string;
  videoDuration: string;
  videoViews: string;
  summaryContent: string;
  videoTranscript: string;
  readTime: string;
  timeSaved: string;
  createdAt: string;
}

interface User {
  id: string;
  email: string;
  firstName: string;
  lastName: string;
  profileImageUrl: string;
  usageCount: number;
  usageLimit: number;
  subscriptionType: string;
}

export default function Dashboard() {
  const { user: authUser, isLoading: authLoading } = useAuth();
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const [videoUrl, setVideoUrl] = useState("");
  const [urlError, setUrlError] = useState("");
  const [currentSummary, setCurrentSummary] = useState<Summary | null>(null);

  // Redirect to login if not authenticated
  useEffect(() => {
    if (!authLoading && !authUser) {
      toast({
        title: "Unauthorized",
        description: "You are logged out. Logging in again...",
        variant: "destructive",
      });
      setTimeout(() => {
        window.location.href = "/api/login";
      }, 500);
      return;
    }
  }, [authUser, authLoading, toast]);

  // Fetch user data
  const { data: user, isLoading: userLoading } = useQuery<User>({
    queryKey: ["/api/auth/user"],
    enabled: !!authUser,
  });

  // Fetch summaries
  const { data: summaries = [], isLoading: summariesLoading } = useQuery<Summary[]>({
    queryKey: ["/api/summaries"],
    enabled: !!authUser,
  });

  // Check for summaryId in URL parameters and auto-select summary
  useEffect(() => {
    const urlParams = new URLSearchParams(window.location.search);
    const summaryId = urlParams.get('summaryId');
    if (summaryId && summaries && summaries.length > 0) {
      const foundSummary = summaries.find(s => s.id === parseInt(summaryId));
      if (foundSummary) {
        setCurrentSummary(foundSummary);
      }
      // Clear the URL parameter
      window.history.replaceState({}, '', window.location.pathname);
    }
  }, [summaries]);

  // Create summary mutation
  const createSummaryMutation = useMutation({
    mutationFn: async (url: string) => {
      const response = await apiRequest("POST", "/api/summaries", { url });
      return await response.json();
    },
    onSuccess: (summary) => {
      setCurrentSummary(summary);
      setVideoUrl("");
      setUrlError("");
      queryClient.invalidateQueries({ queryKey: ["/api/summaries"] });
      queryClient.invalidateQueries({ queryKey: ["/api/auth/user"] });
      toast({
        title: "Summary Generated",
        description: "Your video summary is ready!",
      });
    },
    onError: (error) => {
      if (isUnauthorizedError(error)) {
        toast({
          title: "Unauthorized",
          description: "You are logged out. Logging in again...",
          variant: "destructive",
        });
        setTimeout(() => {
          window.location.href = "/api/login";
        }, 500);
        return;
      }
      
      const errorMessage = error.message;
      if (errorMessage.includes("USAGE_LIMIT_EXCEEDED")) {
        toast({
          title: "Usage Limit Exceeded",
          description: "You've reached your monthly limit. Please upgrade to Pro for unlimited summaries.",
          variant: "destructive",
        });
      } else {
        toast({
          title: "Error",
          description: "Failed to generate summary. Please try again.",
          variant: "destructive",
        });
      }
    },
  });

  const handleSummarize = () => {
    if (!videoUrl.trim()) {
      setUrlError("Please enter a YouTube URL");
      return;
    }

    const youtubePattern = /^(https?:\/\/)?(www\.)?(youtube\.com|youtu\.be)\/.+/;
    if (!youtubePattern.test(videoUrl)) {
      setUrlError("Please enter a valid YouTube URL");
      return;
    }

    if (!user) return;
    
    const currentLimit = user.subscriptionType === 'pro' ? 100 : 
                      user.subscriptionType === 'enterprise' ? 400 :
                      user.subscriptionType === 'super-premium' ? 999999 : 5;
    
    if ((user.usageCount ?? 0) >= currentLimit) {
      toast({
        title: "Usage Limit Exceeded",
        description: "You've reached your monthly limit. Please upgrade to Pro for unlimited summaries.",
        variant: "destructive",
      });
      return;
    }

    setUrlError("");
    createSummaryMutation.mutate(videoUrl);
  };

  const handleCopySummary = async () => {
    if (!currentSummary) return;
    
    try {
      // Extract text content from HTML
      const tempDiv = document.createElement('div');
      tempDiv.innerHTML = currentSummary.summaryContent;
      const textContent = tempDiv.textContent || tempDiv.innerText || '';
      
      await navigator.clipboard.writeText(textContent);
      toast({
        title: "Copied!",
        description: "Summary copied to clipboard",
      });
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to copy summary",
        variant: "destructive",
      });
    }
  };

  const handleSaveSummary = async (format: 'pdf' | 'csv' | 'markdown' | 'txt' = 'txt') => {
    if (!currentSummary) return;
    
    try {
      const tempDiv = document.createElement('div');
      tempDiv.innerHTML = currentSummary.summaryContent;
      const textContent = tempDiv.textContent || tempDiv.innerText || '';
      
      const fileName = currentSummary.videoTitle.replace(/[^a-z0-9]/gi, '_').toLowerCase();
      const timestamp = new Date(currentSummary.createdAt).toLocaleDateString();
      
      let content: string;
      let mimeType: string;
      let extension: string;
      
      switch (format) {
        case 'markdown':
          content = `# ${currentSummary.videoTitle}

**Channel:** ${currentSummary.videoChannel}  
**Generated:** ${timestamp}  
**Duration:** ${currentSummary.videoDuration}  
**Views:** ${currentSummary.videoViews}

---

${currentSummary.summaryContent.replace(/<[^>]*>/g, '').replace(/&nbsp;/g, ' ')}
`;
          mimeType = 'text/markdown';
          extension = 'md';
          break;
          
        case 'csv':
          content = `"Field","Value"
"Video Title","${currentSummary.videoTitle.replace(/"/g, '""')}"
"Channel","${currentSummary.videoChannel.replace(/"/g, '""')}"
"Duration","${currentSummary.videoDuration}"
"Views","${currentSummary.videoViews}"
"Generated","${timestamp}"
"Summary","${textContent.replace(/"/g, '""').replace(/\n/g, ' ')}"
`;
          mimeType = 'text/csv';
          extension = 'csv';
          break;
          
        case 'pdf':
          // For PDF, we'll use the browser's print functionality
          const printWindow = window.open('', '_blank');
          if (printWindow) {
            printWindow.document.write(`
              <html>
                <head>
                  <title>${currentSummary.videoTitle}</title>
                  <style>
                    body { font-family: Arial, sans-serif; margin: 20px; }
                    h1 { color: #333; border-bottom: 2px solid #007bff; padding-bottom: 10px; }
                    .meta { color: #666; margin-bottom: 20px; }
                    .content { line-height: 1.6; }
                  </style>
                </head>
                <body>
                  <h1>${currentSummary.videoTitle}</h1>
                  <div class="meta">
                    <strong>Channel:</strong> ${currentSummary.videoChannel}<br>
                    <strong>Duration:</strong> ${currentSummary.videoDuration}<br>
                    <strong>Views:</strong> ${currentSummary.videoViews}<br>
                    <strong>Generated:</strong> ${timestamp}
                  </div>
                  <div class="content">
                    ${currentSummary.summaryContent}
                  </div>
                </body>
              </html>
            `);
            printWindow.document.close();
            setTimeout(() => {
              printWindow.print();
              printWindow.close();
            }, 250);
          }
          
          toast({
            title: "PDF Export",
            description: "Print dialog opened for PDF export",
          });
          return;
          
        default:
          content = `${currentSummary.videoTitle}
${currentSummary.videoChannel}
Generated on: ${timestamp}

${textContent}`;
          mimeType = 'text/plain';
          extension = 'txt';
      }
      
      const blob = new Blob([content], { type: mimeType });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `${fileName}_summary.${extension}`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);
      
      toast({
        title: "Summary Saved",
        description: `Summary exported as ${format.toUpperCase()}`,
      });
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to save summary",
        variant: "destructive",
      });
    }
  };

  const handleShareSummary = async () => {
    if (!currentSummary) return;
    
    const shareUrl = `${window.location.origin}?share=${currentSummary.id}`;
    const shareText = `Check out this AI summary of "${currentSummary.videoTitle}" on VideoSummarize AI`;
    
    if (navigator.share) {
      // Use native sharing if available
      try {
        await navigator.share({
          title: currentSummary.videoTitle,
          text: shareText,
          url: shareUrl,
        });
      } catch (error) {
        // User cancelled sharing
      }
    } else {
      // Fallback to copying link
      try {
        await navigator.clipboard.writeText(shareUrl);
        toast({
          title: "Link Copied",
          description: "Share link copied to clipboard",
        });
      } catch (error) {
        toast({
          title: "Error",
          description: "Failed to copy share link",
          variant: "destructive",
        });
      }
    }
  };

  const handleFeedback = async (rating: 'helpful' | 'not_helpful') => {
    if (!currentSummary) return;
    
    try {
      await apiRequest("POST", `/api/summaries/${currentSummary.id}/feedback`, { rating });
      
      toast({
        title: "Feedback Submitted",
        description: `Thank you for rating this summary as ${rating.replace('_', ' ')}`,
      });
    } catch (error) {
      if (error instanceof Error && isUnauthorizedError(error)) {
        toast({
          title: "Unauthorized",
          description: "You are logged out. Logging in again...",
          variant: "destructive",
        });
        setTimeout(() => {
          window.location.href = "/api/login";
        }, 500);
        return;
      }
      
      toast({
        title: "Error",
        description: "Failed to submit feedback",
        variant: "destructive",
      });
    }
  };

  const handleLogout = () => {
    window.location.href = "/api/logout";
  };

  if (authLoading || userLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <Loader2 className="w-8 h-8 animate-spin" />
      </div>
    );
  }

  if (!user) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <Card className="w-full max-w-md mx-4">
          <CardContent className="pt-6">
            <div className="flex mb-4 gap-2">
              <AlertCircle className="h-8 w-8 text-red-500" />
              <h1 className="text-2xl font-bold text-gray-900">Error Loading User</h1>
            </div>
            <p className="mt-4 text-sm text-gray-600">
              Unable to load user data. Please try refreshing the page.
            </p>
          </CardContent>
        </Card>
      </div>
    );
  }

  const currentLimit = user.subscriptionType === 'pro' ? 100 : 
                   user.subscriptionType === 'enterprise' ? 400 : 5;
  const usagePercentage = ((user.usageCount ?? 0) / currentLimit) * 100;

  return (
    <div className="bg-slate-50 min-h-screen">
      {/* Main Content */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        
        {/* Video URL Input Section */}
        <Card className="mb-8">
          <CardContent className="p-8">
            <h2 className="text-2xl font-bold text-secondary mb-6 text-center flex items-center justify-center">
              <Plus className="w-6 h-6 text-primary mr-2" />
              Summarize a New Video
            </h2>
            
            <div className="max-w-2xl mx-auto">
              <div className="flex space-x-4">
                <div className="flex-1">
                  <Input
                    type="url"
                    placeholder="Paste YouTube video URL here..."
                    value={videoUrl}
                    onChange={(e) => {
                      setVideoUrl(e.target.value);
                      if (urlError) setUrlError("");
                    }}
                    className="text-lg h-12"
                  />
                </div>
                <Button 
                  onClick={handleSummarize}
                  disabled={createSummaryMutation.isPending}
                  className="px-8 h-12 flex items-center"
                >
                  {createSummaryMutation.isPending ? (
                    <Loader2 className="w-5 h-5 mr-2 animate-spin" />
                  ) : (
                    <Sparkles className="w-5 h-5 mr-2" />
                  )}
                  Summarize
                </Button>
              </div>
              
              {urlError && (
                <div className="mt-2 text-red-600 text-sm flex items-center">
                  <AlertCircle className="w-4 h-4 mr-1" />
                  {urlError}
                </div>
              )}
              
              {/* Loading State */}
              {createSummaryMutation.isPending && (
                <div className="mt-6 text-center">
                  <div className="inline-flex items-center px-4 py-2 bg-blue-50 rounded-lg">
                    <Loader2 className="w-4 h-4 animate-spin mr-3 text-primary" />
                    <span className="text-primary font-medium">Analyzing video and generating summary...</span>
                  </div>
                </div>
              )}
            </div>
          </CardContent>
        </Card>

        {/* Summary Display */}
        {currentSummary && (
          <Card className="mb-8">
            <CardContent className="p-8">
              <div className="flex items-start space-x-4 mb-6">
                <img 
                  src={currentSummary.videoThumbnail} 
                  alt="Video thumbnail" 
                  className="w-24 h-16 rounded object-cover"
                />
                <div className="flex-1">
                  <h3 className="font-semibold text-secondary mb-1">{currentSummary.videoTitle}</h3>
                  <p className="text-sm text-slate-500">
                    {currentSummary.videoChannel} • {currentSummary.videoViews} • {currentSummary.videoDuration}
                  </p>
                </div>
                <div className="text-sm text-slate-500 flex items-center">
                  <Clock className="w-4 h-4 mr-1" />
                  {currentSummary.readTime}
                </div>
              </div>
              
              {/* Summary and Transcript Tabs */}
              <Tabs defaultValue="summary" className="w-full mb-6">
                <TabsList className="grid w-full grid-cols-2">
                  <TabsTrigger value="summary" className="flex items-center gap-2">
                    <Sparkles className="w-4 h-4" />
                    AI Summary
                  </TabsTrigger>
                  <TabsTrigger value="transcript" className="flex items-center gap-2">
                    <Clock className="w-4 h-4" />
                    Original Transcript
                  </TabsTrigger>
                </TabsList>
                
                <TabsContent value="summary" className="mt-6">
                  <div 
                    className="prose prose-slate max-w-none"
                    dangerouslySetInnerHTML={{ __html: currentSummary.summaryContent }}
                  />
                </TabsContent>
                
                <TabsContent value="transcript" className="mt-6">
                  <div className="bg-slate-50 p-6 rounded-lg">
                    {currentSummary.videoTranscript && currentSummary.videoTranscript.trim() ? (
                      <div className="max-w-none">
                        <p className="text-sm text-muted-foreground mb-6 flex items-center gap-2">
                          <Clock className="w-4 h-4" />
                          Original video transcript - formatted for readability
                        </p>
                        <div className="prose prose-slate prose-sm max-w-none">
                          <div className="space-y-4 text-slate-700 leading-relaxed">
                            {currentSummary.videoTranscript.split('\n\n').map((paragraph, index) => (
                              <p key={index} className="text-base leading-7">
                                {paragraph.trim()}
                              </p>
                            ))}
                          </div>
                        </div>
                      </div>
                    ) : (
                      <div className="text-center py-8 text-muted-foreground">
                        <Clock className="w-12 h-12 mx-auto mb-4 opacity-50" />
                        <p>No transcript available for this video.</p>
                        <p className="text-xs mt-2">
                          This video may not have auto-generated captions or transcripts disabled.
                        </p>
                      </div>
                    )}
                  </div>
                </TabsContent>
              </Tabs>
              
              <div className="flex items-center justify-between pt-6 border-t border-slate-200">
                <div className="text-sm text-slate-500 flex items-center">
                  <Clock className="w-4 h-4 mr-1" />
                  Time saved: {currentSummary.timeSaved}
                </div>
                <div className="flex space-x-3">
                  <Button variant="outline" size="sm" onClick={handleCopySummary}>
                    <Copy className="w-4 h-4 mr-2" />
                    Copy
                  </Button>
                  
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="outline" size="sm">
                        <Download className="w-4 h-4 mr-2" />
                        Save
                        <ChevronDown className="w-4 h-4 ml-1" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent>
                      <DropdownMenuItem onClick={() => handleSaveSummary('pdf')}>
                        Save as PDF
                      </DropdownMenuItem>
                      <DropdownMenuItem onClick={() => handleSaveSummary('markdown')}>
                        Save as Markdown
                      </DropdownMenuItem>
                      <DropdownMenuItem onClick={() => handleSaveSummary('csv')}>
                        Save as CSV
                      </DropdownMenuItem>
                      <DropdownMenuItem onClick={() => handleSaveSummary('txt')}>
                        Save as Text
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                  
                  <Button variant="outline" size="sm" onClick={handleShareSummary}>
                    <Share className="w-4 h-4 mr-2" />
                    Share
                  </Button>
                </div>
              </div>

              {/* Feedback Section */}
              <div className="mt-6 pt-6 border-t border-slate-200">
                <p className="text-sm text-slate-600 mb-3">How was this summary?</p>
                <div className="flex space-x-2">
                  <Button 
                    variant="ghost" 
                    size="sm" 
                    className="text-slate-600 hover:text-accent hover:bg-green-50"
                    onClick={() => handleFeedback('helpful')}
                  >
                    <ThumbsUp className="w-4 h-4 mr-1" />
                    Helpful
                  </Button>
                  <Button 
                    variant="ghost" 
                    size="sm" 
                    className="text-slate-600 hover:text-red-600 hover:bg-red-50"
                    onClick={() => handleFeedback('not_helpful')}
                  >
                    <ThumbsDown className="w-4 h-4 mr-1" />
                    Not helpful
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Recent Summaries and Stats */}
        <div className="grid lg:grid-cols-3 gap-8">
          
          {/* Recent Summaries List */}
          <div className="lg:col-span-2">
            <Card>
              <CardContent className="p-6">
                <h3 className="text-xl font-semibold text-secondary mb-6">Recent Summaries</h3>
                
                {summariesLoading ? (
                  <div className="flex items-center justify-center py-8">
                    <Loader2 className="w-6 h-6 animate-spin" />
                  </div>
                ) : summaries.length === 0 ? (
                  <div className="text-center py-8 text-slate-500">
                    No summaries yet. Create your first summary above!
                  </div>
                ) : (
                  <div className="space-y-4">
                    {summaries.slice(0, 5).map((summary) => (
                      <div 
                        key={summary.id}
                        className="flex items-start space-x-4 p-4 border border-slate-200 rounded-lg hover:border-primary/30 transition-colors cursor-pointer"
                        onClick={() => setCurrentSummary(summary)}
                      >
                        <img 
                          src={summary.videoThumbnail} 
                          alt="Video thumbnail" 
                          className="w-16 h-12 rounded object-cover"
                        />
                        <div className="flex-1">
                          <h4 className="font-medium text-secondary mb-1">{summary.videoTitle}</h4>
                          <p className="text-sm text-slate-500 mb-2">
                            {summary.videoChannel} • {summary.videoViews} • {summary.videoDuration}
                          </p>
                          <p className="text-sm text-slate-600 line-clamp-2">
                            Key insights and takeaways from this video...
                          </p>
                        </div>
                        <div className="text-xs text-slate-400">
                          {new Date(summary.createdAt).toLocaleDateString()}
                        </div>
                      </div>
                    ))}
                  </div>
                )}
                
                {summaries.length > 5 && (
                  <div className="mt-6 text-center">
                    <Button variant="ghost">
                      View All Summaries
                    </Button>
                  </div>
                )}
              </CardContent>
            </Card>
          </div>
          
          {/* Usage Stats & Upgrade */}
          <div className="space-y-6">
            
            {/* Usage Statistics */}
            <Card>
              <CardContent className="p-6">
                <h3 className="text-lg font-semibold text-secondary mb-4">Usage This Month</h3>
                
                <div className="space-y-4">
                  <div>
                    <div className="flex justify-between items-center mb-2">
                      <span className="text-sm text-slate-600">Summaries</span>
                      <span className="text-sm font-medium">{user.usageCount} / {currentLimit}</span>
                    </div>
                    <Progress value={usagePercentage} className="h-2" />
                  </div>
                  
                  <div className="pt-4 border-t border-slate-200">
                    <div className="text-sm text-slate-600 mb-2">Time Saved</div>
                    <div className="text-2xl font-bold text-accent">
                      {summaries.length * 2.5} hours
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
            
            {/* Upgrade Card */}
            {user.subscriptionType === 'free' && (
              <Card className="bg-gradient-to-br from-primary to-blue-700 text-white">
                <CardContent className="p-6">
                  <h3 className="text-lg font-semibold mb-2">Upgrade to Pro</h3>
                  <p className="text-blue-100 text-sm mb-4">
                    Get 100 summaries per month, advanced features, and priority processing for $19.99/month.
                  </p>
                  
                  <ul className="text-sm text-blue-100 space-y-1 mb-4">
                    <li className="flex items-center">
                      <Star className="w-4 h-4 mr-2" />
                      100 summaries per month
                    </li>
                    <li className="flex items-center">
                      <Star className="w-4 h-4 mr-2" />
                      Batch processing
                    </li>
                    <li className="flex items-center">
                      <Star className="w-4 h-4 mr-2" />
                      Export to PDF/Word
                    </li>
                  </ul>
                  
                  <Button 
                    className="w-full bg-white text-primary hover:bg-slate-50"
                    onClick={() => window.location.href = "/subscribe"}
                  >
                    Upgrade Now
                  </Button>
                </CardContent>
              </Card>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
