import { useState, useEffect } from "react";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { useLocation } from "wouter";
import { apiRequest } from "@/lib/queryClient";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { useToast } from "@/hooks/use-toast";
import { Switch } from "@/components/ui/switch";
import { Textarea } from "@/components/ui/textarea";
import { Plus, Search, Trash2, Rss, Copy, Tag, Settings, Upload } from "lucide-react";
import { isUnauthorizedError } from "@/lib/authUtils";

interface Channel {
  id: number;
  userId: string;
  channelId: string;
  channelName: string;
  channelHandle: string;
  channelThumbnail: string;
  subscriberCount: string;
  isActive: boolean;
  lastChecked: string;
  videoCount: number;
  createdAt: string;
}

interface Summary {
  id: number;
  channelId: number;
  videoId: string;
  videoTitle: string;
  videoChannel: string;
  videoThumbnail: string;
  videoDuration: string;
  videoViews: string;
  videoPublishedAt: string;
  summaryContent: string;
  videoTranscript: string;
  readTime: string;
  timeSaved: string;
  isMonitored: boolean;
  createdAt: string;
}

interface Topic {
  id: number;
  name: string;
  description: string | null;
  colorHex: string | null;
  topicType: 'auto_extracted' | 'user_defined' | 'playlist';
}

export default function Channels() {
  const [isAddingChannel, setIsAddingChannel] = useState(false);
  const [isBulkImporting, setIsBulkImporting] = useState(false);
  const [channelUrl, setChannelUrl] = useState("");
  const [bulkChannels, setBulkChannels] = useState("");
  const [searchQuery, setSearchQuery] = useState("");
  const [managingChannel, setManagingChannel] = useState<Channel | null>(null);
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const [, setLocation] = useLocation();

  // Helper function to check if a channel has any summaries (has been processed)
  const channelHasSummaries = (channelId: number): boolean => {
    return allSummaries.some(summary => summary.channelId === channelId);
  };

  // Fetch user's channels
  const { data: channels = [], isLoading: channelsLoading } = useQuery<Channel[]>({
    queryKey: ["/api/channels"]
  });

  // Fetch monitored summaries
  const { data: monitoredSummaries = [] } = useQuery<Summary[]>({
    queryKey: ["/api/monitored-summaries", { limit: 50 }]
  });

  // Fetch all summaries to check which channels have been processed
  const { data: allSummaries = [] } = useQuery<Summary[]>({
    queryKey: ["/api/summaries"]
  });

  // Fetch available topics
  const { data: topics = [] } = useQuery<Topic[]>({
    queryKey: ["/api/topics"]
  });

  // Add channel mutation
  const addChannelMutation = useMutation({
    mutationFn: async ({ channelUrl }: { channelUrl: string }) => {
      return await apiRequest("POST", "/api/channels", { channelUrl });
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["/api/channels"] });
      setIsAddingChannel(false);
      setChannelUrl("");
      toast({
        title: "Channel Added",
        description: "Channel monitoring started successfully",
      });
    },
    onError: (error) => {
      if (isUnauthorizedError(error)) {
        toast({
          title: "Unauthorized",
          description: "You are logged out. Logging in again...",
          variant: "destructive",
        });
        setTimeout(() => {
          window.location.href = "/api/login";
        }, 500);
        return;
      }
      toast({
        title: "Error",
        description: error.message || "Failed to add channel",
        variant: "destructive",
      });
    },
  });

  // Bulk import channels mutation
  const bulkImportMutation = useMutation({
    mutationFn: async ({ channelUrls }: { channelUrls: string[] }) => {
      return await apiRequest("POST", "/api/channels/bulk", { channelUrls });
    },
    onSuccess: (data: any) => {
      queryClient.invalidateQueries({ queryKey: ["/api/channels"] });
      setIsBulkImporting(false);
      setBulkChannels("");
      toast({
        title: "Bulk Import Complete",
        description: `${data.succeeded || 0} channels added successfully. ${data.failed || 0} failed.`,
      });
    },
    onError: (error) => {
      if (isUnauthorizedError(error)) {
        toast({
          title: "Unauthorized",
          description: "You are logged out. Logging in again...",
          variant: "destructive",
        });
        setTimeout(() => {
          window.location.href = "/api/login";
        }, 500);
        return;
      }
      toast({
        title: "Error",
        description: error.message || "Failed to import channels",
        variant: "destructive",
      });
    },
  });



  // Toggle channel active status mutation
  const toggleChannelMutation = useMutation({
    mutationFn: async ({ channelId, isActive }: { channelId: number; isActive: boolean }) => {
      return await apiRequest("PATCH", `/api/channels/${channelId}`, { isActive });
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["/api/channels"] });
      toast({
        title: "Channel Updated",
        description: "Channel monitoring status updated",
      });
    },
    onError: (error) => {
      if (isUnauthorizedError(error)) {
        toast({
          title: "Unauthorized",
          description: "You are logged out. Logging in again...",
          variant: "destructive",
        });
        setTimeout(() => {
          window.location.href = "/api/login";
        }, 500);
        return;
      }
      toast({
        title: "Error",
        description: "Failed to update channel",
        variant: "destructive",
      });
    },
  });

  // Delete channel mutation
  const deleteChannelMutation = useMutation({
    mutationFn: async (channelId: number) => {
      return await apiRequest("DELETE", `/api/channels/${channelId}`);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["/api/channels"] });
      toast({
        title: "Channel Deleted",
        description: "Channel removed from monitoring",
      });
    },
    onError: (error) => {
      if (isUnauthorizedError(error)) {
        toast({
          title: "Unauthorized",
          description: "You are logged out. Logging in again...",
          variant: "destructive",
        });
        setTimeout(() => {
          window.location.href = "/api/login";
        }, 500);
        return;
      }
      toast({
        title: "Error",
        description: "Failed to delete channel",
        variant: "destructive",
      });
    },
  });

  // Assign channel to topic mutation
  const assignTopicMutation = useMutation({
    mutationFn: async ({ channelId, topicId }: { channelId: number; topicId: number }) => {
      return await apiRequest("POST", `/api/channels/${channelId}/topics`, { topicId });
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [`/api/channels/${managingChannel?.id}/topics`] });
      toast({
        title: "Topic Assigned",
        description: "Channel assigned to topic successfully",
      });
    },
    onError: (error) => {
      if (isUnauthorizedError(error)) {
        toast({
          title: "Unauthorized",
          description: "You are logged out. Logging in again...",
          variant: "destructive",
        });
        setTimeout(() => {
          window.location.href = "/api/login";
        }, 500);
        return;
      }
      toast({
        title: "Error",
        description: "Failed to assign topic",
        variant: "destructive",
      });
    },
  });

  // Remove topic assignment mutation
  const removeTopicMutation = useMutation({
    mutationFn: async ({ channelId, topicId }: { channelId: number; topicId: number }) => {
      return await apiRequest("DELETE", `/api/channels/${channelId}/topics/${topicId}`);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [`/api/channels/${managingChannel?.id}/topics`] });
      toast({
        title: "Topic Removed",
        description: "Channel removed from topic successfully",
      });
    },
    onError: (error) => {
      if (isUnauthorizedError(error)) {
        toast({
          title: "Unauthorized",
          description: "You are logged out. Logging in again...",
          variant: "destructive",
        });
        setTimeout(() => {
          window.location.href = "/api/login";
        }, 500);
        return;
      }
      toast({
        title: "Error",
        description: "Failed to remove topic",
        variant: "destructive",
      });
    },
  });

  const handleAddChannel = () => {
    if (!channelUrl.trim()) return;
    addChannelMutation.mutate({ channelUrl: channelUrl.trim() });
  };

  const handleBulkImport = () => {
    if (!bulkChannels.trim()) return;
    
    // Parse URLs from text - support multiple formats
    const urls = bulkChannels
      .split(/[\n,;]/)
      .map(url => url.trim())
      .filter(url => url.length > 0)
      .filter(url => 
        url.includes('youtube.com') || 
        url.includes('youtu.be') || 
        url.startsWith('@') || 
        url.startsWith('UC') ||
        url.startsWith('HC') ||
        url.startsWith('UU')
      );

    if (urls.length === 0) {
      toast({
        title: "No Valid URLs",
        description: "Please enter valid YouTube channel URLs, handles, or IDs",
        variant: "destructive",
      });
      return;
    }

    bulkImportMutation.mutate({ channelUrls: urls });
  };

  const handleVideoClick = (summary: Summary) => {
    setLocation(`/?summaryId=${summary.id}`);
  };

  return (
    <div className="container mx-auto p-6 max-w-6xl">
      {/* Primary Action Bar */}
      <div className="flex items-center justify-end mb-6 gap-2">
        <Dialog open={isAddingChannel} onOpenChange={setIsAddingChannel}>
          <DialogTrigger asChild>
            <Button>
              <Plus className="w-4 h-4 mr-2" />
              Add Channel
            </Button>
          </DialogTrigger>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Add YouTube Channel</DialogTitle>
              <DialogDescription>
                Enter a YouTube channel URL, handle (@channel), or channel ID to start monitoring
              </DialogDescription>
            </DialogHeader>
            <div className="space-y-4">
              <Input
                placeholder="https://youtube.com/@channel or @channel or UCChannelID"
                value={channelUrl}
                onChange={(e) => setChannelUrl(e.target.value)}
              />
              <div className="flex justify-end gap-2">
                <Button variant="outline" onClick={() => setIsAddingChannel(false)}>
                  Cancel
                </Button>
                <Button 
                  onClick={handleAddChannel}
                  disabled={!channelUrl.trim() || addChannelMutation.isPending}
                >
                  {addChannelMutation.isPending ? "Adding..." : "Add Channel"}
                </Button>
              </div>
            </div>
          </DialogContent>
        </Dialog>

        <Dialog open={isBulkImporting} onOpenChange={setIsBulkImporting}>
          <DialogTrigger asChild>
            <Button variant="outline">
              <Upload className="w-4 h-4 mr-2" />
              Bulk Import
            </Button>
          </DialogTrigger>
          <DialogContent className="max-w-lg">
            <DialogHeader>
              <DialogTitle>Bulk Import Channels</DialogTitle>
              <DialogDescription>
                Add multiple YouTube channels at once. Enter channel URLs, handles (@channel), or IDs - one per line.
              </DialogDescription>
            </DialogHeader>
            <div className="space-y-4">
              <div className="space-y-2">
                <label className="text-sm font-medium">Channel URLs/Handles/IDs</label>
                <Textarea
                  placeholder={`Examples:
https://www.youtube.com/@channel
@channelhandle
UCChannelID123
https://youtube.com/c/channelname

Separate multiple entries with new lines, commas, or semicolons.`}
                  value={bulkChannels}
                  onChange={(e) => setBulkChannels(e.target.value)}
                  className="min-h-32"
                />
              </div>
              <div className="flex justify-end gap-2">
                <Button variant="outline" onClick={() => setIsBulkImporting(false)}>
                  Cancel
                </Button>
                <Button 
                  onClick={handleBulkImport}
                  disabled={!bulkChannels.trim() || bulkImportMutation.isPending}
                >
                  {bulkImportMutation.isPending ? "Importing..." : "Import Channels"}
                </Button>
              </div>
            </div>
          </DialogContent>
        </Dialog>
      </div>

      {/* Search Control & RSS Feeds */}
      <div className="flex items-center justify-between space-x-4 mb-6">
        <div className="flex-1 relative max-w-md">
          <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Search channels..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="pl-10"
          />
        </div>
        
        {/* RSS Feeds Section */}
        <Dialog>
          <DialogTrigger asChild>
            <Button variant="outline" size="sm">
              <Rss className="w-4 h-4 mr-2" />
              RSS Feeds
            </Button>
          </DialogTrigger>
          <DialogContent className="max-w-lg">
            <DialogHeader>
              <DialogTitle>RSS Feed URLs</DialogTitle>
              <DialogDescription>
                Access your summaries via RSS feeds for your favorite reader app
              </DialogDescription>
            </DialogHeader>
            <div className="space-y-4">
              <div className="space-y-2">
                <label className="text-sm font-medium">All Summaries Feed</label>
                <div className="flex items-center space-x-2">
                  <Input 
                    value={`${window.location.origin}/api/rss/library`}
                    readOnly
                    className="font-mono text-xs"
                  />
                  <Button 
                    size="sm" 
                    variant="outline"
                    onClick={() => {
                      navigator.clipboard.writeText(`${window.location.origin}/api/rss/library`);
                      toast({ title: "Copied", description: "RSS URL copied to clipboard" });
                    }}
                  >
                    <Copy className="w-4 h-4" />
                  </Button>
                </div>
              </div>
              
              {channels.length > 0 && (
                <div className="space-y-2">
                  <label className="text-sm font-medium">Channel-Specific Feeds</label>
                  <div className="max-h-40 overflow-y-auto space-y-2">
                    {channels.slice(0, 5).map((channel) => (
                      <div key={channel.id} className="flex items-center space-x-2">
                        <div className="flex-1">
                          <div className="text-xs font-medium truncate">{channel.channelName}</div>
                          <Input 
                            value={`${window.location.origin}/api/rss/channels/${channel.id}`}
                            readOnly
                            className="font-mono text-xs h-8"
                          />
                        </div>
                        <Button 
                          size="sm" 
                          variant="outline"
                          onClick={() => {
                            navigator.clipboard.writeText(`${window.location.origin}/api/rss/channels/${channel.id}`);
                            toast({ title: "Copied", description: "RSS URL copied to clipboard" });
                          }}
                        >
                          <Copy className="w-4 h-4" />
                        </Button>
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>
          </DialogContent>
        </Dialog>
      </div>

      {/* Monitoring Overview Stats */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Monitored Channels</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{channels.length}</div>
            <p className="text-xs text-muted-foreground">
              {channels.filter((c: Channel) => c.isActive).length} active
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Videos Analyzed</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{monitoredSummaries.length}</div>
            <p className="text-xs text-muted-foreground">From monitored channels</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Time Saved</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {Math.round(monitoredSummaries.length * 2.5)}h
            </div>
            <p className="text-xs text-muted-foreground">Estimated</p>
          </CardContent>
        </Card>
      </div>

      {/* Channels Management Section */}
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <h2 className="text-xl font-semibold">Your Channels</h2>
          <div className="text-sm text-muted-foreground">
            {channels.filter((c: Channel) => c.isActive).length} of {channels.length} active
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {channelsLoading ? (
            Array.from({ length: 6 }).map((_, i) => (
              <Card key={i} className="animate-pulse">
                <CardContent className="p-6">
                  <div className="flex items-center space-x-3">
                    <div className="w-12 h-12 bg-gray-200 rounded-full"></div>
                    <div className="flex-1">
                      <div className="h-4 bg-gray-200 rounded mb-2"></div>
                      <div className="h-3 bg-gray-200 rounded w-2/3"></div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))
          ) : channels.length === 0 ? (
            <Card className="col-span-full p-12 text-center">
              <div className="text-muted-foreground mb-4">
                No channels monitored yet. Add your first channel to start building your knowledge base.
              </div>
              <Button onClick={() => setIsAddingChannel(true)}>
                <Plus className="w-4 h-4 mr-2" />
                Add Your First Channel
              </Button>
            </Card>
          ) : (
            channels
              .filter((channel: Channel) => 
                !searchQuery || 
                channel.channelName.toLowerCase().includes(searchQuery.toLowerCase())
              )
              .map((channel: Channel) => (
                <Card key={channel.id} className="group hover:shadow-md transition-shadow">
                  <CardContent className="p-6">
                    <div className="flex items-start space-x-3">
                      <Avatar className="w-12 h-12">
                        <AvatarImage src={channel.channelThumbnail} alt={channel.channelName} />
                        <AvatarFallback>{channel.channelName.charAt(0)}</AvatarFallback>
                      </Avatar>
                      
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center justify-between mb-2">
                          <h3 className="font-medium text-sm truncate pr-2">{channel.channelName}</h3>
                          <Switch
                            checked={channel.isActive}
                            onCheckedChange={(checked) => 
                              toggleChannelMutation.mutate({ channelId: channel.id, isActive: checked })
                            }
                            disabled={toggleChannelMutation.isPending}
                          />
                        </div>
                        
                        <div className="space-y-1">
                          <p className="text-xs text-muted-foreground truncate">{channel.channelHandle}</p>
                          <p className="text-xs text-muted-foreground">{channel.subscriberCount} subscribers</p>
                          
                          <div className="flex items-center justify-between pt-2">
                            <Badge variant={channel.isActive ? "default" : "secondary"} className="text-xs">
                              {channel.isActive ? "Active" : "Inactive"}
                            </Badge>
                            
                            <div className="flex items-center space-x-1 opacity-0 group-hover:opacity-100 transition-opacity">
                              <Button
                                size="sm"
                                variant="ghost"
                                onClick={() => setManagingChannel(channel)}
                                className="h-6 w-6 p-0"
                              >
                                <Settings className="w-3 h-3" />
                              </Button>
                              <Button
                                size="sm"
                                variant="ghost"
                                onClick={() => deleteChannelMutation.mutate(channel.id)}
                                disabled={deleteChannelMutation.isPending}
                                className="h-6 w-6 p-0 text-destructive hover:text-destructive"
                              >
                                <Trash2 className="w-3 h-3" />
                              </Button>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                    
                    {/* Channel Stats */}
                    <div className="mt-4 pt-4 border-t grid grid-cols-2 gap-4 text-center">
                      <div>
                        <div className="text-lg font-semibold">{channel.videoCount}</div>
                        <div className="text-xs text-muted-foreground">Videos</div>
                      </div>
                      <div>
                        <div className="text-lg font-semibold">
                          {allSummaries.filter(s => s.channelId === channel.id).length}
                        </div>
                        <div className="text-xs text-muted-foreground">Summaries</div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))
          )}
        </div>
      </div>

      {/* Recent Monitored Activity */}
      {monitoredSummaries.length > 0 && (
        <div className="space-y-6 mt-8">
          <div className="flex items-center justify-between">
            <h2 className="text-xl font-semibold">Recent Activity</h2>
            <div className="text-sm text-muted-foreground">
              Latest summaries from monitored channels
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {monitoredSummaries.slice(0, 6).map((summary: Summary) => (
              <Card 
                key={summary.id} 
                className="cursor-pointer hover:shadow-md transition-shadow"
                onClick={() => handleVideoClick(summary)}
              >
                <CardContent className="p-4">
                  <div className="flex space-x-3">
                    <img 
                      src={summary.videoThumbnail} 
                      alt={summary.videoTitle}
                      className="w-16 h-12 object-cover rounded flex-shrink-0"
                    />
                    <div className="flex-1 min-w-0">
                      <h3 className="font-medium text-sm line-clamp-2 mb-1">{summary.videoTitle}</h3>
                      <p className="text-xs text-muted-foreground mb-2">{summary.videoChannel}</p>
                      <div className="flex items-center justify-between text-xs text-muted-foreground">
                        <span>{summary.readTime}</span>
                        <Badge variant="secondary" className="text-xs">
                          {summary.timeSaved} saved
                        </Badge>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      )}

      {/* Channel Management Dialog */}
      {managingChannel && (
        <Dialog open={!!managingChannel} onOpenChange={() => setManagingChannel(null)}>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Manage Channel</DialogTitle>
              <DialogDescription>
                Configure settings for {managingChannel.channelName}
              </DialogDescription>
            </DialogHeader>
            <div className="space-y-4">
              <div className="flex items-center space-x-4">
                <Avatar>
                  <AvatarImage src={managingChannel.channelThumbnail} alt={managingChannel.channelName} />
                  <AvatarFallback>{managingChannel.channelName.charAt(0)}</AvatarFallback>
                </Avatar>
                <div>
                  <h3 className="font-medium">{managingChannel.channelName}</h3>
                  <p className="text-sm text-muted-foreground">{managingChannel.channelHandle}</p>
                </div>
              </div>
              
              <div className="space-y-2">
                <label className="text-sm font-medium">Topics</label>
                <div className="grid grid-cols-2 gap-2">
                  {topics.map((topic) => (
                    <Button
                      key={topic.id}
                      variant="outline"
                      size="sm"
                      onClick={() => assignTopicMutation.mutate({ 
                        channelId: managingChannel.id, 
                        topicId: topic.id 
                      })}
                      disabled={assignTopicMutation.isPending}
                    >
                      <Tag className="w-3 h-3 mr-1" />
                      {topic.name}
                    </Button>
                  ))}
                </div>
              </div>
            </div>
          </DialogContent>
        </Dialog>
      )}
    </div>
  );
}