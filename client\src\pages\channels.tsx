import { useState, useEffect } from "react";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { useAuth } from "@/hooks/useAuth";
import { useLocation } from "wouter";
import { apiRequest } from "@/lib/queryClient";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { useToast } from "@/hooks/use-toast";
import { Switch } from "@/components/ui/switch";
import { Textarea } from "@/components/ui/textarea";
import { Plus, Search, Trash2, Rss, Copy, Tag, Settings, Upload, FileText } from "lucide-react";
import { isUnauthorizedError } from "@/lib/authUtils";

interface Channel {
  id: number;
  userId: string;
  channelId: string;
  channelName: string;
  channelHandle: string;
  channelThumbnail: string;
  subscriberCount: string;
  isActive: boolean;
  lastChecked: string;
  videoCount: number;
  createdAt: string;
}

interface Summary {
  id: number;
  channelId: number;
  videoId: string;
  videoTitle: string;
  videoChannel: string;
  videoThumbnail: string;
  videoDuration: string;
  videoViews: string;
  videoPublishedAt: string;
  summaryContent: string;
  videoTranscript: string;
  readTime: string;
  timeSaved: string;
  isMonitored: boolean;
  createdAt: string;
}

interface Topic {
  id: number;
  name: string;
  description: string | null;
  colorHex: string | null;
  topicType: 'auto_extracted' | 'user_defined' | 'playlist';
}

export default function Channels() {
  const [isAddingChannel, setIsAddingChannel] = useState(false);
  const [isBulkImporting, setIsBulkImporting] = useState(false);
  const [channelUrl, setChannelUrl] = useState("");
  const [bulkChannels, setBulkChannels] = useState("");
  const [searchQuery, setSearchQuery] = useState("");
  const [managingChannel, setManagingChannel] = useState<Channel | null>(null);
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const [, setLocation] = useLocation();

  // Fetch user's channels
  const { data: channels = [], isLoading: channelsLoading } = useQuery<Channel[]>({
    queryKey: ["/api/channels"]
  });

  // Fetch monitored summaries
  const { data: monitoredSummaries = [] } = useQuery<Summary[]>({
    queryKey: ["/api/monitored-summaries", { limit: 50 }]
  });

  // Fetch available topics
  const { data: topics = [] } = useQuery<Topic[]>({
    queryKey: ["/api/topics"]
  });

  // Fetch channel topics for managing channel
  const { data: channelTopics = [] } = useQuery<Topic[]>({
    queryKey: [`/api/channels/${managingChannel?.id}/topics`],
    enabled: !!managingChannel
  });

  // Add channel mutation
  const addChannelMutation = useMutation({
    mutationFn: async ({ channelUrl }: { channelUrl: string }) => {
      return await apiRequest("POST", "/api/channels", { channelUrl });
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["/api/channels"] });
      setIsAddingChannel(false);
      setChannelUrl("");
      toast({
        title: "Channel Added",
        description: "Channel monitoring started successfully",
      });
    },
    onError: (error) => {
      if (isUnauthorizedError(error)) {
        toast({
          title: "Unauthorized",
          description: "You are logged out. Logging in again...",
          variant: "destructive",
        });
        setTimeout(() => {
          window.location.href = "/api/login";
        }, 500);
        return;
      }
      toast({
        title: "Error",
        description: error.message || "Failed to add channel",
        variant: "destructive",
      });
    },
  });

  // Bulk import channels mutation
  const bulkImportMutation = useMutation({
    mutationFn: async ({ channelUrls }: { channelUrls: string[] }) => {
      return await apiRequest("POST", "/api/channels/bulk", { channelUrls });
    },
    onSuccess: (data: any) => {
      queryClient.invalidateQueries({ queryKey: ["/api/channels"] });
      setIsBulkImporting(false);
      setBulkChannels("");
      const successful = data?.successful || 0;
      const failed = data?.failed || 0;
      toast({
        title: "Bulk Import Complete",
        description: `${successful} channels added successfully${failed > 0 ? `, ${failed} failed` : ''}`,
      });
    },
    onError: (error) => {
      if (isUnauthorizedError(error)) {
        toast({
          title: "Unauthorized",
          description: "You are logged out. Logging in again...",
          variant: "destructive",
        });
        setTimeout(() => {
          window.location.href = "/api/login";
        }, 500);
        return;
      }
      toast({
        title: "Error",
        description: error.message || "Failed to import channels",
        variant: "destructive",
      });
    },
  });

  // Toggle channel status
  const toggleChannelMutation = useMutation({
    mutationFn: async ({ channelId, isActive }: { channelId: number; isActive: boolean }) => {
      return await apiRequest("PATCH", `/api/channels/${channelId}`, { isActive });
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["/api/channels"] });
    },
    onError: (error) => {
      if (isUnauthorizedError(error)) {
        toast({
          title: "Unauthorized",
          description: "You are logged out. Logging in again...",
          variant: "destructive",
        });
        setTimeout(() => {
          window.location.href = "/api/login";
        }, 500);
        return;
      }
      toast({
        title: "Error",
        description: "Failed to update channel",
        variant: "destructive",
      });
    },
  });

  // Process recent videos for a single channel
  const processRecentVideosMutation = useMutation({
    mutationFn: async (channelId: number) => {
      return await apiRequest("POST", `/api/channels/${channelId}/process-recent`);
    },
    onSuccess: () => {
      toast({
        title: "Processing Started",
        description: "Summarizing the 3 most recent videos in background",
      });
      // Refresh summaries after a delay to show new content
      setTimeout(() => {
        queryClient.invalidateQueries({ queryKey: ["/api/summaries"] });
        queryClient.invalidateQueries({ queryKey: ["/api/monitored-summaries"] });
      }, 5000);
    },
    onError: (error) => {
      if (isUnauthorizedError(error)) {
        toast({
          title: "Unauthorized",
          description: "You are logged out. Logging in again...",
          variant: "destructive",
        });
        setTimeout(() => {
          window.location.href = "/api/login";
        }, 500);
        return;
      }
      toast({
        title: "Error",
        description: "Failed to process recent videos",
        variant: "destructive",
      });
    },
  });

  // Process recent videos for all channels
  const processAllRecentVideosMutation = useMutation({
    mutationFn: async () => {
      return await apiRequest("POST", "/api/channels/process-all-recent");
    },
    onSuccess: (data: any) => {
      toast({
        title: "Processing Started",
        description: `Processing recent videos for ${data.channelsProcessed} channels`,
      });
      // Refresh summaries after a delay to show new content
      setTimeout(() => {
        queryClient.invalidateQueries({ queryKey: ["/api/summaries"] });
        queryClient.invalidateQueries({ queryKey: ["/api/monitored-summaries"] });
      }, 10000);
    },
    onError: (error) => {
      if (isUnauthorizedError(error)) {
        toast({
          title: "Unauthorized",
          description: "You are logged out. Logging in again...",
          variant: "destructive",
        });
        setTimeout(() => {
          window.location.href = "/api/login";
        }, 500);
        return;
      }
      toast({
        title: "Error",
        description: "Failed to process channels",
        variant: "destructive",
      });
    },
  });

  // Delete channel mutation
  const deleteChannelMutation = useMutation({
    mutationFn: async (channelId: number) => {
      return await apiRequest("DELETE", `/api/channels/${channelId}`);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["/api/channels"] });
      toast({
        title: "Channel Deleted",
        description: "Channel removed from monitoring",
      });
    },
    onError: (error) => {
      if (isUnauthorizedError(error)) {
        toast({
          title: "Unauthorized",
          description: "You are logged out. Logging in again...",
          variant: "destructive",
        });
        setTimeout(() => {
          window.location.href = "/api/login";
        }, 500);
        return;
      }
      toast({
        title: "Error",
        description: "Failed to delete channel",
        variant: "destructive",
      });
    },
  });

  // Assign channel to topic mutation
  const assignTopicMutation = useMutation({
    mutationFn: async ({ channelId, topicId }: { channelId: number; topicId: number }) => {
      return await apiRequest("POST", `/api/channels/${channelId}/topics`, { topicId });
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [`/api/channels/${managingChannel?.id}/topics`] });
      toast({
        title: "Topic Assigned",
        description: "Channel assigned to topic successfully",
      });
    },
    onError: (error) => {
      if (isUnauthorizedError(error)) {
        toast({
          title: "Unauthorized",
          description: "You are logged out. Logging in again...",
          variant: "destructive",
        });
        setTimeout(() => {
          window.location.href = "/api/login";
        }, 500);
        return;
      }
      toast({
        title: "Error",
        description: "Failed to assign topic",
        variant: "destructive",
      });
    },
  });

  // Remove channel from topic mutation
  const removeTopicMutation = useMutation({
    mutationFn: async ({ channelId, topicId }: { channelId: number; topicId: number }) => {
      return await apiRequest("DELETE", `/api/channels/${channelId}/topics/${topicId}`);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [`/api/channels/${managingChannel?.id}/topics`] });
      toast({
        title: "Topic Removed",
        description: "Channel removed from topic successfully",
      });
    },
    onError: (error) => {
      if (isUnauthorizedError(error)) {
        toast({
          title: "Unauthorized",
          description: "You are logged out. Logging in again...",
          variant: "destructive",
        });
        setTimeout(() => {
          window.location.href = "/api/login";
        }, 500);
        return;
      }
      toast({
        title: "Error",
        description: "Failed to remove topic",
        variant: "destructive",
      });
    },
  });

  const handleAddChannel = () => {
    if (!channelUrl.trim()) return;
    addChannelMutation.mutate({ channelUrl: channelUrl.trim() });
  };

  const handleBulkImport = () => {
    if (!bulkChannels.trim()) return;
    
    // Parse URLs from text - support multiple formats
    const urls = bulkChannels
      .split(/[\n,;]/)
      .map(url => url.trim())
      .filter(url => url.length > 0)
      .filter(url => 
        url.includes('youtube.com') || 
        url.includes('youtu.be') || 
        url.startsWith('@') ||
        url.startsWith('UC') ||
        url.startsWith('c/')
      );

    if (urls.length === 0) {
      toast({
        title: "No Valid URLs",
        description: "Please enter valid YouTube channel URLs, handles, or IDs",
        variant: "destructive",
      });
      return;
    }

    bulkImportMutation.mutate({ channelUrls: urls });
  };

  const handleVideoClick = (summary: Summary) => {
    setLocation(`/?summaryId=${summary.id}`);
  };

  return (
    <div className="container mx-auto p-6 max-w-7xl">
      
      {/* Page Header with Primary Action */}
      <div className="flex flex-col space-y-4 mb-8">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold">Channel Monitoring</h1>
            <p className="text-muted-foreground">Monitor YouTube channels for automatic summaries</p>
          </div>
          
          <div className="flex gap-2">
            <Button
              variant="outline"
              onClick={() => processAllRecentVideosMutation.mutate()}
              disabled={processAllRecentVideosMutation.isPending || channels.length === 0}
            >
              <FileText className="w-4 h-4 mr-2" />
              {processAllRecentVideosMutation.isPending ? "Processing..." : "Summarize Recent Videos"}
            </Button>
            
            <Dialog open={isAddingChannel} onOpenChange={setIsAddingChannel}>
              <DialogTrigger asChild>
                <Button>
                  <Plus className="w-4 h-4 mr-2" />
                  Add Channel
                </Button>
              </DialogTrigger>
              <DialogContent>
                <DialogHeader>
                  <DialogTitle>Add YouTube Channel</DialogTitle>
                  <DialogDescription>
                    Enter a YouTube channel URL, handle (@channel), or channel ID to start monitoring
                  </DialogDescription>
                </DialogHeader>
                <div className="space-y-4">
                  <Input
                    placeholder="https://youtube.com/@channel or @channel or UCChannelID"
                    value={channelUrl}
                    onChange={(e) => setChannelUrl(e.target.value)}
                  />
                  <div className="flex justify-end gap-2">
                    <Button variant="outline" onClick={() => setIsAddingChannel(false)}>
                      Cancel
                    </Button>
                    <Button 
                      onClick={handleAddChannel}
                      disabled={!channelUrl.trim() || addChannelMutation.isPending}
                    >
                      {addChannelMutation.isPending ? "Adding..." : "Add Channel"}
                    </Button>
                  </div>
                </div>
              </DialogContent>
            </Dialog>

            <Dialog open={isBulkImporting} onOpenChange={setIsBulkImporting}>
              <DialogTrigger asChild>
                <Button variant="outline">
                  <Upload className="w-4 h-4 mr-2" />
                  Bulk Import
                </Button>
              </DialogTrigger>
              <DialogContent className="max-w-lg">
                <DialogHeader>
                  <DialogTitle>Bulk Import Channels</DialogTitle>
                  <DialogDescription>
                    Add multiple YouTube channels at once. Enter channel URLs, handles (@channel), or IDs - one per line.
                  </DialogDescription>
                </DialogHeader>
                <div className="space-y-4">
                  <div className="space-y-2">
                    <label className="text-sm font-medium">Channel URLs/Handles/IDs</label>
                    <Textarea
                      placeholder={`Examples:
https://www.youtube.com/@channel
@channelhandle
UCChannelID123
https://youtube.com/c/channelname

Separate multiple entries with new lines, commas, or semicolons.`}
                      value={bulkChannels}
                      onChange={(e) => setBulkChannels(e.target.value)}
                      className="min-h-32"
                    />
                  </div>
                  <div className="flex justify-end gap-2">
                    <Button variant="outline" onClick={() => setIsBulkImporting(false)}>
                      Cancel
                    </Button>
                    <Button 
                      onClick={handleBulkImport}
                      disabled={!bulkChannels.trim() || bulkImportMutation.isPending}
                    >
                      {bulkImportMutation.isPending ? "Importing..." : "Import Channels"}
                    </Button>
                  </div>
                </div>
              </DialogContent>
            </Dialog>
          </div>
        </div>

        {/* Search Control & RSS Feeds */}
        <div className="flex items-center justify-between space-x-4">
          <div className="flex-1 relative max-w-md">
            <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Search channels..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-10"
            />
          </div>
          
          {/* RSS Feeds Section */}
          <Dialog>
            <DialogTrigger asChild>
              <Button variant="outline" size="sm">
                <Rss className="w-4 h-4 mr-2" />
                RSS Feeds
              </Button>
            </DialogTrigger>
            <DialogContent className="max-w-lg">
              <DialogHeader>
                <DialogTitle>Channel RSS Feeds</DialogTitle>
                <DialogDescription>
                  Copy RSS feed URLs to subscribe to summaries from your monitored channels
                </DialogDescription>
              </DialogHeader>
              <div className="space-y-4">
                <div>
                  <h4 className="font-medium mb-2">All Channels Feed</h4>
                  <p className="text-sm text-muted-foreground mb-2">
                    Subscribe to summaries from all monitored channels
                  </p>
                  <div className="flex items-center space-x-2">
                    <Input
                      value={`${window.location.origin}/rss/library`}
                      readOnly
                      className="flex-1"
                    />
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => {
                        navigator.clipboard.writeText(`${window.location.origin}/rss/library`);
                        toast({
                          title: "RSS URL Copied",
                          description: "All channels feed URL copied to clipboard"
                        });
                      }}
                    >
                      <Copy className="w-4 h-4" />
                    </Button>
                  </div>
                </div>
                
                <div>
                  <h4 className="font-medium mb-2">Individual Channel Feeds</h4>
                  <p className="text-sm text-muted-foreground mb-3">
                    Each channel has its own RSS feed. Use the RSS button on individual channels to copy their feed URLs.
                  </p>
                </div>
              </div>
            </DialogContent>
          </Dialog>
        </div>
      </div>

      {/* Monitoring Overview Stats */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Monitored Channels</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{channels.length}</div>
            <p className="text-xs text-muted-foreground">
              {channels.filter((c: Channel) => c.isActive).length} active
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Videos Analyzed</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{monitoredSummaries.length}</div>
            <p className="text-xs text-muted-foreground">From monitored channels</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Time Saved</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {Math.round(monitoredSummaries.length * 2.5)}h
            </div>
            <p className="text-xs text-muted-foreground">Estimated</p>
          </CardContent>
        </Card>
      </div>

      {/* Channels Management Section */}
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <h2 className="text-xl font-semibold">Your Channels</h2>
          <div className="text-sm text-muted-foreground">
            {channels.filter((c: Channel) => c.isActive).length} of {channels.length} active
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {channelsLoading ? (
            Array.from({ length: 6 }).map((_, i) => (
              <Card key={i} className="animate-pulse">
                <CardContent className="p-6">
                  <div className="flex items-center space-x-3">
                    <div className="w-12 h-12 bg-gray-200 rounded-full"></div>
                    <div className="flex-1">
                      <div className="h-4 bg-gray-200 rounded mb-2"></div>
                      <div className="h-3 bg-gray-200 rounded w-2/3"></div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))
          ) : channels.length === 0 ? (
            <Card className="col-span-full p-12 text-center">
              <div className="text-muted-foreground mb-4">
                No channels monitored yet. Add your first channel to start building your knowledge base.
              </div>
              <Button onClick={() => setIsAddingChannel(true)}>
                <Plus className="w-4 h-4 mr-2" />
                Add Your First Channel
              </Button>
            </Card>
          ) : (
            channels
              .filter((channel: Channel) => 
                !searchQuery || 
                channel.channelName.toLowerCase().includes(searchQuery.toLowerCase())
              )
              .map((channel: Channel) => (
                <Card key={channel.id} className="group hover:shadow-md transition-shadow">
                  <CardContent className="p-6">
                    <div className="flex items-start justify-between">
                      <div className="flex items-center space-x-3 flex-1">
                        <Avatar>
                          <AvatarImage src={channel.channelThumbnail} />
                          <AvatarFallback>{channel.channelName[0]}</AvatarFallback>
                        </Avatar>
                        <div className="flex-1 min-w-0">
                          <a 
                            href={`https://www.youtube.com/channel/${channel.channelId}`}
                            target="_blank"
                            rel="noopener noreferrer"
                            className="font-medium truncate text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300 hover:underline transition-colors"
                          >
                            {channel.channelName}
                          </a>
                          <p className="text-sm text-muted-foreground">{channel.subscriberCount} subscribers</p>
                          <div className="flex items-center gap-2 mt-2">
                            <Badge variant={channel.isActive ? "default" : "secondary"}>
                              {channel.isActive ? "Active" : "Paused"}
                            </Badge>
                          </div>
                        </div>
                      </div>
                      
                      <div className="flex items-center gap-2 opacity-0 group-hover:opacity-100 transition-opacity">
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => processRecentVideosMutation.mutate(channel.id)}
                          disabled={processRecentVideosMutation.isPending}
                          className="text-green-600 hover:text-green-800"
                          title="Process 3 Recent Videos"
                        >
                          <FileText className="w-4 h-4" />
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => setManagingChannel(channel)}
                          className="text-purple-600 hover:text-purple-800"
                          title="Manage Topics"
                        >
                          <Tag className="w-4 h-4" />
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => {
                            const rssUrl = `${window.location.origin}/rss/channel/${channel.id}`;
                            navigator.clipboard.writeText(rssUrl);
                            toast({
                              title: "RSS URL Copied",
                              description: `${channel.channelName} feed URL copied to clipboard`
                            });
                          }}
                          className="text-blue-600 hover:text-blue-800"
                        >
                          <Rss className="w-4 h-4" />
                        </Button>
                        <Switch
                          checked={channel.isActive}
                          onCheckedChange={(checked) => 
                            toggleChannelMutation.mutate({ channelId: channel.id, isActive: checked })
                          }
                        />
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => deleteChannelMutation.mutate(channel.id)}
                          className="text-red-500 hover:text-red-700"
                        >
                          <Trash2 className="w-4 h-4" />
                        </Button>
                      </div>
                    </div>
                    
                    {channel.lastChecked && (
                      <p className="text-xs text-muted-foreground mt-3">
                        Last checked: {new Date(channel.lastChecked).toLocaleDateString()}
                      </p>
                    )}
                  </CardContent>
                </Card>
              ))
          )}
        </div>
      </div>

      {/* Recent Activity Section */}
      {monitoredSummaries.length > 0 && (
        <div className="mt-12 space-y-6">
          <div className="flex items-center justify-between">
            <h2 className="text-xl font-semibold">Recent Activity</h2>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {monitoredSummaries.slice(0, 6).map((summary: Summary) => (
              <Card key={summary.id} className="cursor-pointer hover:shadow-md transition-shadow"
                    onClick={() => handleVideoClick(summary)}>
                <CardContent className="p-4">
                  <div className="flex items-start space-x-3">
                    <img 
                      src={summary.videoThumbnail} 
                      alt={summary.videoTitle}
                      className="w-16 h-12 object-cover rounded flex-shrink-0"
                    />
                    <div className="flex-1 min-w-0">
                      <p className="text-sm font-medium line-clamp-2 mb-1">{summary.videoTitle}</p>
                      <p className="text-xs text-muted-foreground mb-2">{summary.videoChannel}</p>
                      <div className="flex items-center gap-2">
                        <Badge variant="secondary" className="text-xs">{summary.readTime}</Badge>
                        <span className="text-xs text-muted-foreground">
                          {new Date(summary.createdAt).toLocaleDateString()}
                        </span>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      )}

      {/* Topic Management Dialog */}
      {managingChannel && (
        <Dialog open={!!managingChannel} onOpenChange={() => setManagingChannel(null)}>
          <DialogContent className="max-w-md">
            <DialogHeader>
              <DialogTitle>Manage Topics for {managingChannel.channelName}</DialogTitle>
              <DialogDescription>
                Assign this channel to topics to organize your content library
              </DialogDescription>
            </DialogHeader>

            
            <div className="space-y-4">
              {/* Current Topics */}
              <div>
                <h4 className="text-sm font-medium mb-2">Current Topics</h4>
                {channelTopics.length === 0 ? (
                  <p className="text-sm text-muted-foreground">No topics assigned</p>
                ) : (
                  <div className="space-y-2">
                    {channelTopics.map((topic) => (
                      <div key={topic.id} className="flex items-center justify-between p-2 border rounded">
                        <div className="flex items-center gap-2">
                          <div 
                            className="w-3 h-3 rounded-full" 
                            style={{ backgroundColor: topic.colorHex || '#3b82f6' }}
                          />
                          <span className="text-sm">{topic.name}</span>
                        </div>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => 
                            removeTopicMutation.mutate({ 
                              channelId: managingChannel.id, 
                              topicId: topic.id 
                            })
                          }
                          disabled={removeTopicMutation.isPending}
                          className="text-red-500 hover:text-red-700"
                        >
                          <Trash2 className="w-3 h-3" />
                        </Button>
                      </div>
                    ))}
                  </div>
                )}
              </div>

              {/* Available Topics */}
              <div>
                <h4 className="text-sm font-medium mb-2">Available Topics</h4>
                {!topics || topics.length === 0 ? (
                  <p className="text-sm text-muted-foreground">No topics available. Create topics first.</p>
                ) : topics.filter(topic => !channelTopics.some(ct => ct.id === topic.id)).length === 0 ? (
                  <p className="text-sm text-muted-foreground">All topics assigned</p>
                ) : (
                  <div className="space-y-2 max-h-48 overflow-y-auto">
                    {topics
                      .filter(topic => !channelTopics.some(ct => ct.id === topic.id))
                      .map((topic) => (
                        <div key={topic.id} className="flex items-center justify-between p-2 border rounded">
                          <div className="flex items-center gap-2">
                            <div 
                              className="w-3 h-3 rounded-full" 
                              style={{ backgroundColor: topic.colorHex || '#3b82f6' }}
                            />
                            <span className="text-sm">{topic.name || 'Unnamed Topic'}</span>
                            {topic.description && (
                              <span className="text-xs text-muted-foreground">
                                - {topic.description}
                              </span>
                            )}
                          </div>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => 
                              assignTopicMutation.mutate({ 
                                channelId: managingChannel.id, 
                                topicId: topic.id 
                              })
                            }
                            disabled={assignTopicMutation.isPending}
                            className="text-green-600 hover:text-green-800"
                          >
                            <Plus className="w-3 h-3" />
                          </Button>
                        </div>
                      ))}
                  </div>
                )}
              </div>

              {/* Quick Links */}
              <div className="flex justify-between pt-4 border-t">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => {
                    setManagingChannel(null);
                    setLocation("/topics");
                  }}
                >
                  <Settings className="w-4 h-4 mr-2" />
                  Manage Topics
                </Button>
                <Button
                  variant="outline"
                  onClick={() => setManagingChannel(null)}
                >
                  Done
                </Button>
              </div>
            </div>
          </DialogContent>
        </Dialog>
      )}
    </div>
  );
}