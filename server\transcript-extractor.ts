import { HttpsProxyAgent } from 'https-proxy-agent';
import { SocksProxyAgent } from 'socks-proxy-agent';

interface ProxyConfig {
  host: string;
  port: number;
  username?: string;
  password?: string;
  protocol: 'http' | 'https' | 'socks4' | 'socks5';
}

interface TranscriptResult {
  success: boolean;
  transcript: string;
  method: string;
  proxyUsed?: string;
  attempts: number;
  error?: string;
}

interface ExtractionMetrics {
  totalAttempts: number;
  successfulExtractions: number;
  failedExtractions: number;
  proxySuccessRate: Map<string, number>;
  methodSuccessRate: Map<string, number>;
}

class TranscriptExtractor {
  private proxies: ProxyConfig[] = [];
  private currentProxyIndex = 0;
  private maxRetries = 3;
  private retryDelay = 1000; // 1 second
  private metrics: ExtractionMetrics;

  constructor() {
    this.metrics = {
      totalAttempts: 0,
      successfulExtractions: 0,
      failedExtractions: 0,
      proxySuccessRate: new Map(),
      methodSuccessRate: new Map()
    };
    this.loadProxyConfiguration();
  }

  private loadProxyConfiguration() {
    // Load proxy configuration from environment variables
    const proxyList = process.env.PROXY_LIST;
    if (proxyList) {
      try {
        this.proxies = JSON.parse(proxyList);
        console.log(`Loaded ${this.proxies.length} proxy configurations`);
      } catch (error) {
        console.error('Failed to parse proxy configuration:', error);
      }
    }

    // Add default free proxy sources as fallback (rotate through public proxies)
    if (this.proxies.length === 0) {
      console.log('No proxy configuration found, transcript extraction may fail on cloud platforms');
    }
  }

  private getNextProxy(): ProxyConfig | null {
    if (this.proxies.length === 0) return null;
    
    const proxy = this.proxies[this.currentProxyIndex];
    this.currentProxyIndex = (this.currentProxyIndex + 1) % this.proxies.length;
    return proxy;
  }

  private createProxyAgent(proxy: ProxyConfig) {
    const auth = proxy.username && proxy.password ? `${proxy.username}:${proxy.password}@` : '';
    
    switch (proxy.protocol) {
      case 'http':
      case 'https':
        const httpUrl = `${proxy.protocol}://${auth}${proxy.host}:${proxy.port}`;
        return new HttpsProxyAgent(httpUrl);
      
      case 'socks4':
      case 'socks5':
        const socksUrl = `${proxy.protocol}://${auth}${proxy.host}:${proxy.port}`;
        return new SocksProxyAgent(socksUrl);
      
      default:
        throw new Error(`Unsupported proxy protocol: ${proxy.protocol}`);
    }
  }

  private async extractWithYtdlCore(videoId: string, proxy?: ProxyConfig): Promise<string> {
    try {
      const ytdl = await import('@distube/ytdl-core');
      const videoUrl = `https://www.youtube.com/watch?v=${videoId}`;
      
      const options: any = {
        requestOptions: {}
      };

      if (proxy) {
        options.requestOptions.agent = this.createProxyAgent(proxy);
      }

      const info = await ytdl.default.getInfo(videoUrl, options);
      const tracks = info.player_response?.captions?.playerCaptionsTracklistRenderer?.captionTracks || [];
      
      if (tracks.length === 0) return "";
      
      const englishTrack = tracks.find((track: any) => track.languageCode === 'en') || tracks[0];
      if (!englishTrack?.baseUrl) return "";
      
      const { default: fetch } = await import('node-fetch');
      const fetchOptions: any = {};
      
      if (proxy) {
        fetchOptions.agent = this.createProxyAgent(proxy);
      }

      const response = await fetch(englishTrack.baseUrl, fetchOptions);
      const xml = await response.text();
      
      if (!xml || xml.length === 0) return "";
      
      const textRegex = /<text[^>]*>([^<]*)<\/text>/g;
      const texts = [];
      let match;
      
      while ((match = textRegex.exec(xml)) !== null) {
        if (match[1] && match[1].trim()) {
          texts.push(match[1].trim());
        }
      }
      
      return texts.join(' ')
        .replace(/&amp;/g, '&')
        .replace(/&lt;/g, '<')
        .replace(/&gt;/g, '>')
        .replace(/&quot;/g, '"')
        .replace(/&#39;/g, "'")
        .replace(/\s+/g, ' ')
        .trim();
    } catch (error) {
      throw new Error(`ytdl-core extraction failed: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  private async extractWithYoutubeTranscript(videoId: string, proxy?: ProxyConfig): Promise<string> {
    try {
      const { YoutubeTranscript } = await import('youtube-transcript');
      
      if (!YoutubeTranscript || !YoutubeTranscript.fetchTranscript) {
        throw new Error('YoutubeTranscript not properly loaded');
      }

      const options: any = {};
      
      if (proxy) {
        // Configure proxy for youtube-transcript
        const agent = this.createProxyAgent(proxy);
        options.agent = agent;
      }

      const transcript = await YoutubeTranscript.fetchTranscript(videoId, options);
      
      if (!transcript || transcript.length === 0) return "";
      
      return transcript
        .map((segment: any) => segment.text)
        .join(' ')
        .replace(/\s+/g, ' ')
        .trim();
    } catch (error) {
      throw new Error(`youtube-transcript extraction failed: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  private async delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  private updateMetrics(method: string, proxy: string | null, success: boolean) {
    this.metrics.totalAttempts++;
    
    if (success) {
      this.metrics.successfulExtractions++;
    } else {
      this.metrics.failedExtractions++;
    }

    // Update method success rate
    const methodKey = method;
    const methodAttempts = this.metrics.methodSuccessRate.get(methodKey) || 0;
    this.metrics.methodSuccessRate.set(methodKey, methodAttempts + (success ? 1 : 0));

    // Update proxy success rate
    if (proxy) {
      const proxyAttempts = this.metrics.proxySuccessRate.get(proxy) || 0;
      this.metrics.proxySuccessRate.set(proxy, proxyAttempts + (success ? 1 : 0));
    }
  }

  async extractTranscript(videoId: string): Promise<TranscriptResult> {
    console.log(`Starting transcript extraction for video: ${videoId}`);
    
    const methods = [
      { name: 'ytdl-core', fn: this.extractWithYtdlCore.bind(this) },
      { name: 'youtube-transcript', fn: this.extractWithYoutubeTranscript.bind(this) }
    ];

    let totalAttempts = 0;
    let lastError = '';

    // Try each method with different proxy configurations
    for (const method of methods) {
      console.log(`Trying method: ${method.name}`);

      // First try without proxy (may work in some environments)
      try {
        totalAttempts++;
        const transcript = await method.fn(videoId);
        
        if (transcript && transcript.length > 0) {
          this.updateMetrics(method.name, null, true);
          console.log(`Successfully extracted transcript using ${method.name} (no proxy): ${transcript.length} characters`);
          
          return {
            success: true,
            transcript,
            method: method.name,
            attempts: totalAttempts
          };
        }
      } catch (error) {
        const errorMsg = error instanceof Error ? error.message : String(error);
        console.log(`Method ${method.name} failed without proxy: ${errorMsg}`);
        lastError = errorMsg;
        this.updateMetrics(method.name, null, false);
      }

      // Try with each available proxy
      for (let proxyAttempt = 0; proxyAttempt < this.maxRetries && this.proxies.length > 0; proxyAttempt++) {
        const proxy = this.getNextProxy();
        if (!proxy) break;

        const proxyString = `${proxy.protocol}://${proxy.host}:${proxy.port}`;
        console.log(`Trying method: ${method.name} with proxy: ${proxyString} (attempt ${proxyAttempt + 1})`);

        try {
          totalAttempts++;
          const transcript = await method.fn(videoId, proxy);
          
          if (transcript && transcript.length > 0) {
            this.updateMetrics(method.name, proxyString, true);
            console.log(`Successfully extracted transcript using ${method.name} with proxy ${proxyString}: ${transcript.length} characters`);
            
            return {
              success: true,
              transcript,
              method: method.name,
              proxyUsed: proxyString,
              attempts: totalAttempts
            };
          }
        } catch (error) {
          const errorMsg = error instanceof Error ? error.message : String(error);
          console.log(`Method ${method.name} failed with proxy ${proxyString}: ${errorMsg}`);
          lastError = errorMsg;
          this.updateMetrics(method.name, proxyString, false);
          
          // Add delay between proxy attempts
          if (proxyAttempt < this.maxRetries - 1) {
            await this.delay(this.retryDelay);
          }
        }
      }
    }

    console.log(`All transcript extraction methods failed for video: ${videoId}`);
    return {
      success: false,
      transcript: '',
      method: 'none',
      attempts: totalAttempts,
      error: lastError
    };
  }

  getMetrics(): ExtractionMetrics {
    return { ...this.metrics };
  }

  getSuccessRate(): number {
    if (this.metrics.totalAttempts === 0) return 0;
    return this.metrics.successfulExtractions / this.metrics.totalAttempts;
  }
}

export default TranscriptExtractor;