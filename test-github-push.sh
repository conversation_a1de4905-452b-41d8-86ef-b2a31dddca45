#!/bin/bash

# Test GitHub Push - VideoSummarize AI
# Quick verification that GitHub pushes are working

echo "Testing GitHub Push for VideoSummarize AI..."

# Create a test file with timestamp
TEST_FILE="github-test-$(date +%s).txt"
echo "GitHub push test at $(date)" > "$TEST_FILE"

# Stage and commit the test file
git add "$TEST_FILE"
git commit -m "Test GitHub push - $(date '+%Y-%m-%d %H:%M:%S')"

# Push and capture output
echo "Pushing to GitHub..."
PUSH_RESULT=$(git push 2>&1)

if [[ $PUSH_RESULT == *"github.com"* ]]; then
    echo "✅ SUCCESS: Push confirmed to GitHub"
    echo "Repository: https://github.com/clark-mackey/video-summarizer"
    
    # Get commit hash
    COMMIT_HASH=$(git rev-parse HEAD | cut -c1-8)
    echo "Latest commit: $COMMIT_HASH"
    echo "View commit: https://github.com/clark-mackey/video-summarizer/commit/$COMMIT_HASH"
    
    # Clean up test file
    rm "$TEST_FILE"
    git add "$TEST_FILE"
    git commit -m "Remove test file"
    git push
    
    echo "✅ Test completed - GitHub sync verified"
else
    echo "❌ FAILED: Push may not have reached GitHub"
    echo "Output: $PUSH_RESULT"
fi