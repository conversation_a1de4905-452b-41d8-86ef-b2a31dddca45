# VideoSummarize AI

**Last Updated: June 7, 2025 - 02:58 UTC**

A cutting-edge YouTube Video Summarizer SaaS platform that transforms video content into concise, actionable insights using advanced AI technologies.

## 🚀 Features

### Core Functionality
- **AI-Powered Summaries**: Generate intelligent summaries with OpenAI GPT-4o
- **Structured Analysis**: 8 comprehensive summary sections including:
  - Title Accuracy Analysis
  - Participant Identification
  - Key Takeaways
  - Important Sections with clickable YouTube timestamps
  - Detailed Analysis
  - Tools/Services Mentioned
  - Important Quotes
  - Direct Implications

### Export & Sharing
- **Multiple Export Formats**: PDF, CSV, Markdown, and Text
- **Native Sharing**: Device-native sharing with clipboard fallback
- **Real-time Processing**: Authentic YouTube video analysis with transcript integration

### User Experience
- **Subscription Tiers**: Free, Pro, and Enterprise plans
- **Feedback System**: Helpful/not helpful ratings for continuous improvement
- **Secure Authentication**: Replit Auth integration
- **Responsive Design**: Modern UI with dark/light mode support

## 🛠 Tech Stack

### Frontend
- **React 18** with TypeScript
- **Vite** for fast development and building
- **Tailwind CSS** for styling
- **shadcn/ui** for UI components
- **TanStack Query** for data fetching
- **Wouter** for client-side routing

### Backend
- **Express.js** with TypeScript
- **PostgreSQL** with Drizzle ORM
- **OpenAI API** for AI summarization
- **YouTube API** for video metadata
- **youtube-transcript** for transcript extraction

## 📋 Prerequisites

- Node.js 18+ 
- PostgreSQL database
- OpenAI API key
- YouTube API key

## ⚙️ Installation & Setup

1. **Clone the repository**
   ```bash
   git clone <your-repo-url>
   cd videosummarize-ai
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Set up environment variables**
   Create environment secrets for:
   - `OPENAI_API_KEY` - Your OpenAI API key
   - `YOUTUBE_API_KEY` - Your YouTube Data API v3 key
   - `DATABASE_URL` - PostgreSQL connection string
   - `SESSION_SECRET` - Session encryption secret

4. **Initialize the database**
   ```bash
   npm run db:push
   ```

5. **Start the development server**
   ```bash
   npm run dev
   ```

## 🚀 API Endpoints

### Authentication
- `GET /api/auth/user` - Get current user
- `GET /api/login` - Initiate login flow
- `GET /api/logout` - Logout user

### Summaries
- `POST /api/summaries` - Create new video summary
- `GET /api/summaries` - Get user's summaries
- `GET /api/summaries/:id` - Get specific summary
- `GET /api/search` - Search summaries by text

### Feedback
- `POST /api/feedback` - Submit summary feedback
- `GET /api/feedback/:summaryId/stats` - Get feedback statistics

## 📁 Project Structure

```
├── client/                 # Frontend React application
│   ├── src/
│   │   ├── components/     # Reusable UI components
│   │   ├── pages/          # Page components
│   │   ├── hooks/          # Custom React hooks
│   │   └── lib/            # Utility functions
├── server/                 # Backend Express application
│   ├── db.ts              # Database connection
│   ├── routes.ts          # API routes
│   ├── storage.ts         # Data access layer
│   └── replitAuth.ts      # Authentication setup
├── shared/                 # Shared types and schemas
│   └── schema.ts          # Database schema and types
└── package.json           # Dependencies and scripts
```

## 🔐 Environment Configuration

Required environment variables:

```bash
# API Keys
OPENAI_API_KEY=your_openai_api_key
YOUTUBE_API_KEY=your_youtube_api_key

# Database
DATABASE_URL=postgresql://user:password@host:port/database

# Authentication
SESSION_SECRET=your_session_secret

# Replit Auth (automatically provided)
REPL_ID=your_repl_id
REPLIT_DOMAINS=your_domain
```

## 🎯 Usage

1. **Sign in** using Replit authentication
2. **Paste a YouTube URL** in the dashboard
3. **Wait for AI processing** (typically 8-20 seconds)
4. **Review the structured summary** with 8 analysis sections
5. **Export or share** your summary in multiple formats
6. **Provide feedback** to help improve the AI system

## 📊 Summary Structure

Each generated summary includes:

1. **Title Explained** - Analysis of video title accuracy
2. **Who?** - Key participants and speakers identified
3. **Key Takeaways** - Main points and insights
4. **Important Sections** - Timestamped key moments
5. **Detailed Analysis** - Comprehensive content breakdown
6. **Tools/Services Mentioned** - Resources referenced in video
7. **Important Quotes** - Notable statements and insights
8. **Direct Implications** - Actionable outcomes and next steps

## 🔄 Development

### Database Migrations
```bash
npm run db:push    # Push schema changes to database
```

### Code Quality
- TypeScript for type safety
- ESLint for code linting
- Prettier for code formatting

## 📝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## 📄 License

This project is proprietary software. All rights reserved.

## 🤝 Support

For technical support or feature requests, please contact the development team.