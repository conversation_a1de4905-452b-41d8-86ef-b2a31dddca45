import { useState, useEffect } from "react";
import { useAuth } from "@/hooks/useAuth";
import { useToast } from "@/hooks/use-toast";
import { useQuery } from "@tanstack/react-query";
import { apiRequest } from "@/lib/queryClient";
import { isUnauthorizedError } from "@/lib/authUtils";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { 
  Search, 
  Filter, 
  Calendar, 
  Clock, 
  Eye, 
  Share, 
  Copy,
  Download,
  Star,
  Grid3X3,
  List,
  SortAsc,
  SortDesc,
  Loader2,
  PlayCircle,
  Rss
} from "lucide-react";
import { Link } from "wouter";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";

interface Summary {
  id: number;
  videoId: string;
  videoTitle: string;
  videoChannel: string;
  videoThumbnail: string;
  videoDuration: string;
  videoViews: string;
  summaryContent: string;
  videoTranscript: string;
  readTime: string;
  timeSaved: string;
  createdAt: string;
  channelId?: number;
  isMonitored?: boolean;
}

interface Channel {
  id: number;
  channelName: string;
  channelThumbnail: string;
}

export default function Library() {
  const { user: authUser, isLoading: authLoading } = useAuth();
  const { toast } = useToast();
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedChannel, setSelectedChannel] = useState<string>("");
  const [sortBy, setSortBy] = useState<string>("recent");
  const [viewMode, setViewMode] = useState<"grid" | "list">("grid");
  const [selectedSummary, setSelectedSummary] = useState<Summary | null>(null);

  // Redirect to login if not authenticated
  useEffect(() => {
    if (!authLoading && !authUser) {
      toast({
        title: "Unauthorized",
        description: "You are logged out. Logging in again...",
        variant: "destructive",
      });
      setTimeout(() => {
        window.location.href = "/api/login";
      }, 500);
      return;
    }
  }, [authUser, authLoading, toast]);

  // Fetch summaries
  const { data: summaries = [], isLoading: summariesLoading } = useQuery<Summary[]>({
    queryKey: ["/api/summaries"],
    enabled: !!authUser,
  });

  // Fetch channels for filtering
  const { data: channels = [] } = useQuery<Channel[]>({
    queryKey: ["/api/channels"],
    enabled: !!authUser,
  });

  // Vector search when search query exists
  const { data: searchResults = [], isLoading: searchLoading } = useQuery<Summary[]>({
    queryKey: ["/api/vector-search", { q: searchQuery, channels: selectedChannel }],
    enabled: !!authUser && searchQuery.length > 2
  });

  // Filter and sort summaries
  const filteredSummaries = (() => {
    let filtered = searchQuery.length > 2 ? searchResults : summaries;
    
    // Filter by channel
    if (selectedChannel && selectedChannel !== "all") {
      filtered = filtered.filter(s => s.channelId?.toString() === selectedChannel);
    }

    // Sort summaries
    switch (sortBy) {
      case "recent":
        return [...filtered].sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime());
      case "oldest":
        return [...filtered].sort((a, b) => new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime());
      case "title":
        return [...filtered].sort((a, b) => a.videoTitle.localeCompare(b.videoTitle));
      case "channel":
        return [...filtered].sort((a, b) => a.videoChannel.localeCompare(b.videoChannel));
      default:
        return filtered;
    }
  })();

  const handleCopySummary = async (summary: Summary) => {
    try {
      const tempDiv = document.createElement('div');
      tempDiv.innerHTML = summary.summaryContent;
      const textContent = tempDiv.textContent || tempDiv.innerText || '';
      
      await navigator.clipboard.writeText(textContent);
      toast({
        title: "Copied!",
        description: "Summary copied to clipboard",
      });
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to copy summary",
        variant: "destructive",
      });
    }
  };

  const handleShareSummary = async (summary: Summary) => {
    const shareUrl = `${window.location.origin}/?summaryId=${summary.id}`;
    const shareText = `Check out this AI summary of "${summary.videoTitle}" on VideoSummarize AI`;
    
    if (navigator.share) {
      try {
        await navigator.share({
          title: summary.videoTitle,
          text: shareText,
          url: shareUrl,
        });
      } catch (error) {
        // User cancelled sharing
      }
    } else {
      try {
        await navigator.clipboard.writeText(shareUrl);
        toast({
          title: "Link Copied",
          description: "Share link copied to clipboard",
        });
      } catch (error) {
        toast({
          title: "Error",
          description: "Failed to copy share link",
          variant: "destructive",
        });
      }
    }
  };

  if (authLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <Loader2 className="w-8 h-8 animate-spin" />
      </div>
    );
  }

  const monitoredSummaries = summaries.filter(s => s.isMonitored);
  const personalSummaries = summaries.filter(s => !s.isMonitored);

  return (
    <div className="container mx-auto p-6 max-w-7xl">
      
      {/* Page Header */}
      <div className="flex flex-col space-y-4 mb-8">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold">Summary Library</h1>
            <p className="text-muted-foreground">Browse and search your video summaries</p>
          </div>
          
          {/* View Controls & RSS */}
          <div className="flex items-center space-x-2">
            {/* RSS Feed Access */}
            <Dialog>
              <DialogTrigger asChild>
                <Button variant="outline" size="sm">
                  <Rss className="w-4 h-4 mr-2" />
                  RSS Feeds
                </Button>
              </DialogTrigger>
              <DialogContent className="max-w-md">
                <DialogHeader>
                  <DialogTitle>RSS Feed Access</DialogTitle>
                  <DialogDescription>
                    Copy RSS feed URLs to subscribe to your video summaries in any RSS reader
                  </DialogDescription>
                </DialogHeader>
                <div className="space-y-4">
                  <div>
                    <h4 className="font-medium mb-2">All Library Feed</h4>
                    <p className="text-sm text-muted-foreground mb-2">
                      Subscribe to all your video summaries
                    </p>
                    <div className="flex items-center space-x-2">
                      <Input
                        readOnly
                        value={`${window.location.origin}/rss/library`}
                        className="text-xs"
                      />
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => {
                          navigator.clipboard.writeText(`${window.location.origin}/rss/library`);
                          toast({
                            title: "RSS URL Copied",
                            description: "Library RSS feed URL copied to clipboard"
                          });
                        }}
                      >
                        <Copy className="w-4 h-4" />
                      </Button>
                    </div>
                  </div>
                  <div className="text-xs text-muted-foreground">
                    <p>Add this URL to your RSS reader to stay updated with new summaries.</p>
                    <p className="mt-1">Popular RSS readers: Feedly, Inoreader, Apple News</p>
                  </div>
                </div>
              </DialogContent>
            </Dialog>
            
            <Button
              variant={viewMode === "grid" ? "default" : "outline"}
              size="sm"
              onClick={() => setViewMode("grid")}
            >
              <Grid3X3 className="w-4 h-4" />
            </Button>
            <Button
              variant={viewMode === "list" ? "default" : "outline"}
              size="sm"
              onClick={() => setViewMode("list")}
            >
              <List className="w-4 h-4" />
            </Button>
          </div>
        </div>

        {/* Search and Filters */}
        <div className="flex flex-col sm:flex-row gap-4">
          <div className="flex-1 relative">
            <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Search summaries using AI-powered vector search..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-10"
            />
          </div>
          
          <Select value={selectedChannel} onValueChange={setSelectedChannel}>
            <SelectTrigger className="w-48">
              <SelectValue placeholder="All Channels" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Channels</SelectItem>
              {channels.map((channel) => (
                <SelectItem key={channel.id} value={channel.id.toString()}>
                  {channel.channelName}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>

          <Select value={sortBy} onValueChange={setSortBy}>
            <SelectTrigger className="w-40">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="recent">Most Recent</SelectItem>
              <SelectItem value="oldest">Oldest First</SelectItem>
              <SelectItem value="title">By Title</SelectItem>
              <SelectItem value="channel">By Channel</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>

      {/* Summary Statistics */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-8">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Summaries</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{summaries.length}</div>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">From Monitoring</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{monitoredSummaries.length}</div>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Personal</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{personalSummaries.length}</div>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Time Saved</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {Math.round(summaries.length * 2.5)}h
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Summary Tabs */}
      <Tabs defaultValue="all" className="space-y-6">
        <TabsList>
          <TabsTrigger value="all">All Summaries ({filteredSummaries.length})</TabsTrigger>
          <TabsTrigger value="monitored">From Channels ({monitoredSummaries.length})</TabsTrigger>
          <TabsTrigger value="personal">Personal ({personalSummaries.length})</TabsTrigger>
        </TabsList>

        <TabsContent value="all">
          {summariesLoading || searchLoading ? (
            <div className="flex items-center justify-center py-12">
              <Loader2 className="w-8 h-8 animate-spin" />
            </div>
          ) : filteredSummaries.length === 0 ? (
            <Card className="p-12 text-center">
              <div className="text-muted-foreground">
                {searchQuery ? "No summaries found matching your search." : "No summaries yet. Create your first summary!"}
              </div>
              {!searchQuery && (
                <Link href="/">
                  <Button className="mt-4">
                    <PlayCircle className="w-4 h-4 mr-2" />
                    Summarize a Video
                  </Button>
                </Link>
              )}
            </Card>
          ) : (
            <SummaryGrid 
              summaries={filteredSummaries} 
              viewMode={viewMode}
              onCopy={handleCopySummary}
              onShare={handleShareSummary}
            />
          )}
        </TabsContent>

        <TabsContent value="monitored">
          <SummaryGrid 
            summaries={monitoredSummaries} 
            viewMode={viewMode}
            onCopy={handleCopySummary}
            onShare={handleShareSummary}
          />
        </TabsContent>

        <TabsContent value="personal">
          <SummaryGrid 
            summaries={personalSummaries} 
            viewMode={viewMode}
            onCopy={handleCopySummary}
            onShare={handleShareSummary}
          />
        </TabsContent>
      </Tabs>
    </div>
  );
}

// Summary Grid Component
interface SummaryGridProps {
  summaries: Summary[];
  viewMode: "grid" | "list";
  onCopy: (summary: Summary) => void;
  onShare: (summary: Summary) => void;
}

function SummaryGrid({ summaries, viewMode, onCopy, onShare }: SummaryGridProps) {
  if (viewMode === "list") {
    return (
      <div className="space-y-4">
        {summaries.map((summary) => (
          <Card key={summary.id} className="hover:shadow-md transition-shadow">
            <CardContent className="p-6">
              <div className="flex items-start space-x-4">
                <img 
                  src={summary.videoThumbnail} 
                  alt="Video thumbnail" 
                  className="w-32 h-20 rounded object-cover flex-shrink-0"
                />
                <div className="flex-1 min-w-0">
                  <div className="flex items-start justify-between mb-2">
                    <div className="flex-1">
                      <h3 className="font-semibold text-lg line-clamp-2 mb-1">
                        {summary.videoTitle}
                      </h3>
                      <p className="text-sm text-muted-foreground mb-2">
                        {summary.videoChannel} • {summary.videoDuration} • {summary.videoViews}
                      </p>
                    </div>
                    <SummaryActions summary={summary} onCopy={onCopy} onShare={onShare} />
                  </div>
                  <div className="flex items-center space-x-4 text-xs text-muted-foreground">
                    <span className="flex items-center">
                      <Clock className="w-3 h-3 mr-1" />
                      {summary.readTime} read
                    </span>
                    <span className="flex items-center">
                      <Calendar className="w-3 h-3 mr-1" />
                      {new Date(summary.createdAt).toLocaleDateString()}
                    </span>
                    {summary.isMonitored && (
                      <Badge variant="secondary" className="text-xs">
                        Auto-generated
                      </Badge>
                    )}
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    );
  }

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
      {summaries.map((summary) => (
        <Card key={summary.id} className="hover:shadow-md transition-shadow">
          <CardContent className="p-4">
            <div className="relative mb-3">
              <img 
                src={summary.videoThumbnail} 
                alt="Video thumbnail" 
                className="w-full h-48 rounded object-cover"
              />
              <div className="absolute top-2 right-2">
                <SummaryActions summary={summary} onCopy={onCopy} onShare={onShare} />
              </div>
            </div>
            
            <div className="space-y-2">
              <h3 className="font-semibold line-clamp-2 text-sm">
                {summary.videoTitle}
              </h3>
              <p className="text-xs text-muted-foreground">
                {summary.videoChannel}
              </p>
              <div className="flex items-center justify-between text-xs text-muted-foreground">
                <span className="flex items-center">
                  <Clock className="w-3 h-3 mr-1" />
                  {summary.readTime}
                </span>
                <span>{new Date(summary.createdAt).toLocaleDateString()}</span>
              </div>
              {summary.isMonitored && (
                <Badge variant="secondary" className="text-xs">
                  Auto-generated
                </Badge>
              )}
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  );
}

// Summary Actions Component
interface SummaryActionsProps {
  summary: Summary;
  onCopy: (summary: Summary) => void;
  onShare: (summary: Summary) => void;
}

function SummaryActions({ summary, onCopy, onShare }: SummaryActionsProps) {
  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant="ghost" size="sm">
          <Eye className="w-4 h-4" />
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end">
        <DropdownMenuItem asChild>
          <Link href={`/?summaryId=${summary.id}`}>
            <Eye className="mr-2 h-4 w-4" />
            View Summary
          </Link>
        </DropdownMenuItem>
        <DropdownMenuSeparator />
        <DropdownMenuItem onClick={() => onCopy(summary)}>
          <Copy className="mr-2 h-4 w-4" />
          Copy Summary
        </DropdownMenuItem>
        <DropdownMenuItem onClick={() => onShare(summary)}>
          <Share className="mr-2 h-4 w-4" />
          Share Summary
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  );
}