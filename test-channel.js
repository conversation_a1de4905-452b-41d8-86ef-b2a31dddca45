// Test script to verify YouTube API functionality

async function testYouTubeAPI() {
  const apiKey = process.env.YOUTUBE_API_KEY;
  
  if (!apiKey) {
    console.log('YouTube API key not found');
    return;
  }

  try {
    // Test with Starter Story channel handle
    const channelHandle = 'starterstory';
    const response = await fetch(
      `https://www.googleapis.com/youtube/v3/search?key=${apiKey}&part=snippet&type=channel&q=${channelHandle}&maxResults=1`
    );
    
    const data = await response.json();
    
    if (data.items && data.items.length > 0) {
      const channel = data.items[0];
      console.log('Channel found:');
      console.log('- Title:', channel.snippet.title);
      console.log('- Channel ID:', channel.snippet.channelId);
      console.log('- Description:', channel.snippet.description.substring(0, 100) + '...');
      
      // Now get detailed channel metadata
      const detailResponse = await fetch(
        `https://www.googleapis.com/youtube/v3/channels?id=${channel.snippet.channelId}&key=${apiKey}&part=snippet,statistics`
      );
      
      const detailData = await detailResponse.json();
      
      if (detailData.items && detailData.items.length > 0) {
        const details = detailData.items[0];
        console.log('- Subscribers:', details.statistics.subscriberCount);
        console.log('- Videos:', details.statistics.videoCount);
        console.log('- Views:', details.statistics.viewCount);
      }
    } else {
      console.log('Channel not found');
    }
  } catch (error) {
    console.error('API test failed:', error.message);
  }
}

testYouTubeAPI();