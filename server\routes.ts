import type { Express } from "express";
import { createServer, type Server } from "http";
import { storage } from "./storage-simple";
import { setupAuth, isAuthenticated } from "./replitAuth";
import { insertSummarySchema } from "@shared/schema";
import { z } from "zod";
import Stripe from "stripe";

// YouTube URL validation schema
const youtubeUrlSchema = z.object({
  url: z.string().url().refine((url) => {
    const pattern = /^(https?:\/\/)?(www\.)?(youtube\.com|youtu\.be)\/.+/;
    return pattern.test(url);
  }, "Invalid YouTube URL"),
});

// Extract YouTube video ID from URL
function extractVideoId(url: string): string | null {
  const regex = /(?:youtube\.com\/(?:[^\/]+\/.+\/|(?:v|e(?:mbed)?)\/|.*[?&]v=)|youtu\.be\/)([^"&?\/\s]{11})/;
  const match = url.match(regex);
  return match ? match[1] : null;
}

// Mock function to get video metadata (in production, use YouTube API)
async function getVideoMetadata(videoId: string) {
  // In production, this would call YouTube Data API
  // For now, return mock data that matches the video ID pattern
  const mockTitles = [
    "10 Essential JavaScript Tips for Modern Development",
    "Building a SaaS Product from Scratch",
    "Advanced React Patterns and Best Practices",
    "The Future of Web Development in 2024",
    "Productivity Hacks That Actually Work",
  ];
  
  const mockChannels = [
    "WebDev Pro",
    "Startup Guide",
    "CodeMaster",
    "Tech Insights",
    "Productivity Hub",
  ];

  const titleIndex = videoId.charCodeAt(0) % mockTitles.length;
  const channelIndex = videoId.charCodeAt(1) % mockChannels.length;

  return {
    title: mockTitles[titleIndex],
    channel: mockChannels[channelIndex],
    thumbnail: `https://img.youtube.com/vi/${videoId}/maxresdefault.jpg`,
    duration: "12:34",
    views: "1.2M views",
  };
}

// Mock function to generate AI summary (in production, use actual AI service)
async function generateSummary(videoId: string, videoTitle: string): Promise<string> {
  // In production, this would call your AI summarization service
  // For now, return a structured summary based on the video
  return `
    <div class="bg-slate-50 p-4 rounded-lg mb-4">
      <h4 class="font-semibold text-secondary mb-3">
        <i class="fas fa-lightbulb text-yellow-500 mr-2"></i>Key Takeaways:
      </h4>
      <ul class="space-y-2 text-slate-700">
        <li>• Main concept 1 from ${videoTitle}</li>
        <li>• Important insight about the topic covered</li>
        <li>• Actionable advice mentioned in the video</li>
        <li>• Key recommendation for implementation</li>
        <li>• Best practice highlighted by the creator</li>
      </ul>
    </div>
    
    <div class="bg-blue-50 p-4 rounded-lg mb-4">
      <h4 class="font-semibold text-secondary mb-3">
        <i class="fas fa-star text-blue-500 mr-2"></i>Main Points:
      </h4>
      <ul class="space-y-2 text-slate-700">
        <li>• Detailed explanation of core concepts</li>
        <li>• Step-by-step process outlined in the video</li>
        <li>• Common mistakes to avoid</li>
        <li>• Tools and resources mentioned</li>
      </ul>
    </div>
  `;
}

// Initialize Stripe if keys are available
let stripe: Stripe | null = null;
if (process.env.STRIPE_SECRET_KEY) {
  stripe = new Stripe(process.env.STRIPE_SECRET_KEY);
}

export async function registerRoutes(app: Express): Promise<Server> {
  // Auth middleware
  await setupAuth(app);

  // Auth routes
  app.get('/api/auth/user', isAuthenticated, async (req: any, res) => {
    try {
      const userId = req.user.claims.sub;
      const user = await storage.getUser(userId);
      res.json(user);
    } catch (error) {
      console.error("Error fetching user:", error);
      res.status(500).json({ message: "Failed to fetch user" });
    }
  });

  // Get user summaries
  app.get('/api/summaries', isAuthenticated, async (req: any, res) => {
    try {
      const userId = req.user.claims.sub;
      const summaries = await storage.getUserSummaries(userId);
      res.json(summaries);
    } catch (error) {
      console.error("Error fetching summaries:", error);
      res.status(500).json({ message: "Failed to fetch summaries" });
    }
  });

  // Create new summary
  app.post('/api/summaries', isAuthenticated, async (req: any, res) => {
    try {
      const userId = req.user.claims.sub;
      const { url } = youtubeUrlSchema.parse(req.body);
      
      // Get user to check usage limits
      const user = await storage.getUser(userId);
      if (!user) {
        return res.status(404).json({ message: "User not found" });
      }

      // Check usage limits based on subscription type
      let usageLimit = 5; // free tier default
      if (user.subscriptionType === 'pro') {
        usageLimit = 100;
      } else if (user.subscriptionType === 'enterprise') {
        usageLimit = 400;
      } else if (user.subscriptionType === 'super-premium') {
        usageLimit = 999999; // Unlimited for super-premium
      }

      if ((user.usageCount ?? 0) >= usageLimit) {
        let upgradeMessage = "Usage limit exceeded. Please upgrade to Pro for 100 summaries per month.";
        if (user.subscriptionType === 'pro') {
          upgradeMessage = "Usage limit exceeded. Please upgrade to Enterprise for 400 summaries per month.";
        } else if (user.subscriptionType === 'enterprise') {
          upgradeMessage = "Usage limit exceeded. You've reached the maximum allowed summaries for this month.";
        }
        
        return res.status(429).json({ 
          message: upgradeMessage,
          code: "USAGE_LIMIT_EXCEEDED"
        });
      }

      // Extract video ID
      const videoId = extractVideoId(url);
      if (!videoId) {
        return res.status(400).json({ message: "Invalid YouTube URL" });
      }

      // Check if summary already exists for this user and video
      const existingSummary = await storage.getSummaryByVideoId(userId, videoId);
      if (existingSummary) {
        return res.json(existingSummary);
      }

      // Get video metadata
      const metadata = await getVideoMetadata(videoId);
      
      // Generate AI summary
      const summaryContent = await generateSummary(videoId, metadata.title);
      
      // Calculate read time and time saved
      const readTime = "3 min read";
      const timeSaved = "9 minutes";

      // Create summary
      const summaryData = insertSummarySchema.parse({
        userId,
        videoId,
        videoTitle: metadata.title,
        videoChannel: metadata.channel,
        videoThumbnail: metadata.thumbnail,
        videoDuration: metadata.duration,
        videoViews: metadata.views,
        summaryContent,
        readTime,
        timeSaved,
      });

      const summary = await storage.createSummary(summaryData);
      
      // Update user usage count
      await storage.incrementUserUsage(userId);

      res.json(summary);
    } catch (error) {
      if (error instanceof z.ZodError) {
        return res.status(400).json({ 
          message: "Invalid request data",
          errors: error.errors 
        });
      }
      console.error("Error creating summary:", error);
      res.status(500).json({ message: "Failed to create summary" });
    }
  });

  // Get specific summary
  app.get('/api/summaries/:id', isAuthenticated, async (req: any, res) => {
    try {
      const userId = req.user.claims.sub;
      const summaryId = parseInt(req.params.id);
      
      const summary = await storage.getSummary(summaryId, userId);
      if (!summary) {
        return res.status(404).json({ message: "Summary not found" });
      }

      res.json(summary);
    } catch (error) {
      console.error("Error fetching summary:", error);
      res.status(500).json({ message: "Failed to fetch summary" });
    }
  });

  // MCP Server endpoints for super-premium tier
  
  // Semantic search across summaries
  app.post('/api/mcp/search-summaries', isAuthenticated, async (req: any, res) => {
    try {
      const userId = req.user.claims.sub;
      const user = await storage.getUser(userId);
      
      // Check if user has super-premium access
      if (user?.subscriptionType !== 'super-premium') {
        return res.status(403).json({ 
          message: "Super-premium subscription required for MCP server access" 
        });
      }

      const { query, filters } = req.body;
      const results = await storage.searchSummariesByText(userId, query);
      
      res.json(results);
    } catch (error) {
      console.error("Error in MCP search:", error);
      res.status(500).json({ message: "Failed to search summaries" });
    }
  });

  // Get topics for exploration
  app.get('/api/mcp/topics', isAuthenticated, async (req: any, res) => {
    try {
      const userId = req.user.claims.sub;
      const user = await storage.getUser(userId);
      
      if (user?.subscriptionType !== 'super-premium') {
        return res.status(403).json({ 
          message: "Super-premium subscription required" 
        });
      }

      const topics = await storage.getTopics();
      res.json(topics);
    } catch (error) {
      console.error("Error fetching topics:", error);
      res.status(500).json({ message: "Failed to fetch topics" });
    }
  });

  // Get related topics for knowledge exploration
  app.get('/api/mcp/topics/:id/related', isAuthenticated, async (req: any, res) => {
    try {
      const userId = req.user.claims.sub;
      const user = await storage.getUser(userId);
      
      if (user?.subscriptionType !== 'super-premium') {
        return res.status(403).json({ 
          message: "Super-premium subscription required" 
        });
      }

      const topicId = parseInt(req.params.id);
      const depth = parseInt(req.query.depth as string) || 1;
      
      const relatedTopics = await storage.getRelatedTopics(topicId, depth);
      
      await storage.recordMcpUsage(userId, 'get_related_topics', 0, 0);
      
      res.json(relatedTopics);
    } catch (error) {
      console.error("Error fetching related topics:", error);
      res.status(500).json({ message: "Failed to fetch related topics" });
    }
  });

  // Get topic trends
  app.get('/api/mcp/topics/:name/trends', isAuthenticated, async (req: any, res) => {
    try {
      const userId = req.user.claims.sub;
      const user = await storage.getUser(userId);
      
      if (user?.subscriptionType !== 'super-premium') {
        return res.status(403).json({ 
          message: "Super-premium subscription required" 
        });
      }

      const topicName = req.params.name;
      const timeframe = req.query.timeframe as string || 'month';
      
      const trends = await storage.getTopicTrends(topicName, timeframe);
      
      await storage.recordMcpUsage(userId, 'topic_trends', 0, 0);
      
      res.json(trends);
    } catch (error) {
      console.error("Error fetching topic trends:", error);
      res.status(500).json({ message: "Failed to fetch topic trends" });
    }
  });

  // Knowledge synthesis endpoint
  app.post('/api/mcp/synthesize-knowledge', isAuthenticated, async (req: any, res) => {
    try {
      const userId = req.user.claims.sub;
      const user = await storage.getUser(userId);
      
      if (user?.subscriptionType !== 'super-premium') {
        return res.status(403).json({ 
          message: "Super-premium subscription required" 
        });
      }

      const { query, summaryIds } = req.body;
      
      // Get the specified summaries
      const summaries = [];
      for (const id of summaryIds) {
        const summary = await storage.getSummary(id, userId);
        if (summary) summaries.push(summary);
      }
      
      // Simple knowledge synthesis (can be enhanced with AI)
      const synthesis = {
        query,
        summaryCount: summaries.length,
        commonThemes: [], // Placeholder for AI-extracted themes
        keyInsights: [], // Placeholder for synthesized insights
        relatedTopics: [], // Placeholder for related topics
        summaries: summaries.map(s => ({
          id: s.id,
          title: s.videoTitle,
          channel: s.videoChannel,
          relevantContent: s.summaryContent.substring(0, 200) + '...'
        }))
      };
      
      await storage.recordMcpUsage(userId, 'synthesize_knowledge', query.length, 0);
      
      res.json(synthesis);
    } catch (error) {
      console.error("Error synthesizing knowledge:", error);
      res.status(500).json({ message: "Failed to synthesize knowledge" });
    }
  });

  // Stripe subscription route
  app.post('/api/create-subscription', isAuthenticated, async (req: any, res) => {
    if (!stripe) {
      return res.status(500).json({ 
        message: "Payment processing not configured. Please contact support." 
      });
    }

    try {
      const userId = req.user.claims.sub;
      const user = await storage.getUser(userId);
      
      if (!user) {
        return res.status(404).json({ message: "User not found" });
      }

      if (!user.email) {
        return res.status(400).json({ message: "User email required for subscription" });
      }

      // Create Stripe customer if needed
      const customer = await stripe.customers.create({
        email: user.email,
        name: `${user.firstName || ''} ${user.lastName || ''}`.trim() || user.email,
      });

      // Create a payment intent for the subscription
      const paymentIntent = await stripe.paymentIntents.create({
        amount: 1999, // $19.99 in cents
        currency: 'usd',
        customer: customer.id,
        metadata: {
          userId: userId,
          subscriptionType: 'pro',
        },
      });

      res.json({
        clientSecret: paymentIntent.client_secret,
      });
    } catch (error: any) {
      console.error("Stripe subscription error:", error);
      res.status(500).json({ 
        message: "Failed to create subscription",
        error: error.message 
      });
    }
  });

  const httpServer = createServer(app);
  return httpServer;
}
