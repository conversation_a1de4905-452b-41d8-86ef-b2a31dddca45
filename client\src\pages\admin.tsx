import { useState, useEffect } from "react";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { Shield, Users, BarChart3, DollarSign, Settings, Eye, Ban, Play, RotateCcw, Edit, Activity, Server } from "lucide-react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { useToast } from "@/hooks/use-toast";
import { apiRequest } from "@/lib/queryClient";
import EnhancedLayout from "@/components/enhanced-layout";

interface User {
  id: string;
  email: string;
  firstName: string;
  lastName: string;
  role: string;
  usageCount: number;
  usageLimit: number;
  subscriptionType: string;
  isActive: boolean;
  totalTokensUsed: number;
  tokenCosts: number;
  createdAt: string;
  updatedAt: string;
}

interface UserMetrics {
  totalUsers: number;
  activeUsers: number;
  subscriptionDistribution: { [key: string]: number };
  totalUsage: number;
  totalTokenCosts: number;
}

interface TranscriptMetrics {
  totalAttempts: number;
  successfulExtractions: number;
  failedExtractions: number;
  overallSuccessRate: number;
  proxySuccessRates: { [key: string]: number };
  methodSuccessRates: { [key: string]: number };
}

export default function AdminPage() {
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const [selectedUser, setSelectedUser] = useState<User | null>(null);
  const [editDialogOpen, setEditDialogOpen] = useState(false);

  // Check admin status
  const { data: adminStatus } = useQuery<{ isAdmin: boolean; role: string }>({
    queryKey: ["/api/admin/status"],
    retry: false,
  });

  // Fetch all users
  const { data: users = [], isLoading: usersLoading } = useQuery<User[]>({
    queryKey: ["/api/admin/users"],
    enabled: adminStatus?.isAdmin,
  });

  // Fetch metrics
  const { data: metrics } = useQuery<UserMetrics>({
    queryKey: ["/api/admin/metrics"],
    enabled: adminStatus?.isAdmin,
  });

  // Fetch transcript extraction metrics
  const { data: transcriptMetrics } = useQuery<TranscriptMetrics>({
    queryKey: ["/api/transcript/metrics"],
    enabled: adminStatus?.isAdmin,
    refetchInterval: 30000, // Refresh every 30 seconds
  });

  // User management mutations
  const updateSubscriptionMutation = useMutation({
    mutationFn: async ({ userId, subscriptionType, usageLimit }: { userId: string; subscriptionType: string; usageLimit: number }) => {
      const response = await fetch(`/api/admin/users/${userId}/subscription`, {
        method: "PATCH",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ subscriptionType, usageLimit }),
      });
      if (!response.ok) throw new Error("Failed to update subscription");
      return response.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["/api/admin/users"] });
      queryClient.invalidateQueries({ queryKey: ["/api/admin/metrics"] });
      toast({ title: "Success", description: "Subscription updated successfully" });
      setEditDialogOpen(false);
    },
    onError: () => {
      toast({ title: "Error", description: "Failed to update subscription", variant: "destructive" });
    },
  });

  const suspendUserMutation = useMutation({
    mutationFn: async (userId: string) => {
      const response = await fetch(`/api/admin/users/${userId}/suspend`, {
        method: "POST",
        headers: { "Content-Type": "application/json" },
      });
      if (!response.ok) throw new Error("Failed to suspend user");
      return response.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["/api/admin/users"] });
      queryClient.invalidateQueries({ queryKey: ["/api/admin/metrics"] });
      toast({ title: "Success", description: "User suspended successfully" });
    },
    onError: () => {
      toast({ title: "Error", description: "Failed to suspend user", variant: "destructive" });
    },
  });

  const activateUserMutation = useMutation({
    mutationFn: async (userId: string) => {
      const response = await fetch(`/api/admin/users/${userId}/activate`, {
        method: "POST",
        headers: { "Content-Type": "application/json" },
      });
      if (!response.ok) throw new Error("Failed to activate user");
      return response.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["/api/admin/users"] });
      queryClient.invalidateQueries({ queryKey: ["/api/admin/metrics"] });
      toast({ title: "Success", description: "User activated successfully" });
    },
    onError: () => {
      toast({ title: "Error", description: "Failed to activate user", variant: "destructive" });
    },
  });

  const resetUsageMutation = useMutation({
    mutationFn: async (userId: string) => {
      const response = await fetch(`/api/admin/users/${userId}/reset-usage`, {
        method: "POST",
        headers: { "Content-Type": "application/json" },
      });
      if (!response.ok) throw new Error("Failed to reset usage");
      return response.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["/api/admin/users"] });
      queryClient.invalidateQueries({ queryKey: ["/api/admin/metrics"] });
      toast({ title: "Success", description: "Usage reset successfully" });
    },
    onError: () => {
      toast({ title: "Error", description: "Failed to reset usage", variant: "destructive" });
    },
  });

  if (!adminStatus?.isAdmin) {
    return (
      <EnhancedLayout>
        <div className="container mx-auto p-6">
          <Card>
            <CardContent className="text-center py-8">
              <Shield className="w-12 h-12 mx-auto text-muted-foreground mb-4" />
              <h2 className="text-2xl font-bold mb-2">Access Denied</h2>
              <p className="text-muted-foreground">You don't have permission to access this admin panel.</p>
            </CardContent>
          </Card>
        </div>
      </EnhancedLayout>
    );
  }

  const getSubscriptionColor = (subType: string) => {
    switch (subType) {
      case 'free': return 'bg-gray-100 text-gray-800';
      case 'pro': return 'bg-blue-100 text-blue-800';
      case 'enterprise': return 'bg-purple-100 text-purple-800';
      case 'super-premium': return 'bg-gold-100 text-gold-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const formatCurrency = (hundredthsOfCents: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 4,
      maximumFractionDigits: 6,
    }).format(hundredthsOfCents / 10000);
  };

  return (
    <EnhancedLayout>
      <div className="container mx-auto p-6 space-y-6">
        <div className="flex items-center space-x-2">
          <Shield className="w-8 h-8 text-primary" />
          <h1 className="text-3xl font-bold">Admin Dashboard</h1>
        </div>

        {/* Metrics Overview */}
        {metrics && (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Total Users</CardTitle>
                <Users className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{metrics.totalUsers}</div>
                <p className="text-xs text-muted-foreground">
                  {metrics.activeUsers} active
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Total Usage</CardTitle>
                <BarChart3 className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{metrics.totalUsage.toLocaleString()}</div>
                <p className="text-xs text-muted-foreground">
                  summaries generated
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Token Costs</CardTitle>
                <DollarSign className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{formatCurrency(metrics.totalTokenCosts)}</div>
                <p className="text-xs text-muted-foreground">
                  total API costs
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Subscriptions</CardTitle>
                <Settings className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="space-y-1">
                  {Object.entries(metrics.subscriptionDistribution).map(([type, count]) => (
                    <div key={type} className="flex justify-between text-sm">
                      <span className="capitalize">{type}:</span>
                      <span className="font-medium">{count}</span>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>
        )}

        {/* Transcript Extraction Metrics */}
        {transcriptMetrics && (
          <Card className="mb-6">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Activity className="h-5 w-5" />
                Transcript Extraction Performance
              </CardTitle>
              <CardDescription>
                Monitor proxy infrastructure and extraction success rates
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
                <div className="text-center">
                  <div className="text-2xl font-bold text-green-600">
                    {(transcriptMetrics.overallSuccessRate * 100).toFixed(1)}%
                  </div>
                  <div className="text-sm text-muted-foreground">Success Rate</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold">{transcriptMetrics.totalAttempts}</div>
                  <div className="text-sm text-muted-foreground">Total Attempts</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-green-600">{transcriptMetrics.successfulExtractions}</div>
                  <div className="text-sm text-muted-foreground">Successful</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-red-600">{transcriptMetrics.failedExtractions}</div>
                  <div className="text-sm text-muted-foreground">Failed</div>
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {/* Method Success Rates */}
                <div>
                  <h4 className="font-medium mb-3 flex items-center gap-2">
                    <Server className="h-4 w-4" />
                    Extraction Methods
                  </h4>
                  <div className="space-y-2">
                    {Object.entries(transcriptMetrics.methodSuccessRates).map(([method, rate]) => (
                      <div key={method} className="flex justify-between items-center">
                        <span className="text-sm">{method}</span>
                        <div className="flex items-center gap-2">
                          <div className="w-20 bg-secondary rounded-full h-2">
                            <div 
                              className="bg-green-500 h-2 rounded-full" 
                              style={{ width: `${(rate * 100)}%` }}
                            />
                          </div>
                          <span className="text-sm font-medium w-12 text-right">
                            {(rate * 100).toFixed(0)}%
                          </span>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>

                {/* Proxy Success Rates */}
                <div>
                  <h4 className="font-medium mb-3 flex items-center gap-2">
                    <Shield className="h-4 w-4" />
                    Proxy Performance
                  </h4>
                  {Object.keys(transcriptMetrics.proxySuccessRates).length > 0 ? (
                    <div className="space-y-2">
                      {Object.entries(transcriptMetrics.proxySuccessRates).map(([proxy, rate]) => (
                        <div key={proxy} className="flex justify-between items-center">
                          <span className="text-sm truncate max-w-[120px]" title={proxy}>
                            {proxy.split('://')[1]?.split('@')[1] || proxy}
                          </span>
                          <div className="flex items-center gap-2">
                            <div className="w-20 bg-secondary rounded-full h-2">
                              <div 
                                className="bg-blue-500 h-2 rounded-full" 
                                style={{ width: `${(rate * 100)}%` }}
                              />
                            </div>
                            <span className="text-sm font-medium w-12 text-right">
                              {(rate * 100).toFixed(0)}%
                            </span>
                          </div>
                        </div>
                      ))}
                    </div>
                  ) : (
                    <div className="text-sm text-muted-foreground bg-yellow-50 dark:bg-yellow-900/20 p-3 rounded-md">
                      <div className="font-medium text-yellow-800 dark:text-yellow-200 mb-1">
                        No Proxy Configuration Detected
                      </div>
                      <div className="text-yellow-700 dark:text-yellow-300">
                        Transcript extraction may fail on cloud platforms. 
                        Configure PROXY_LIST environment variable for production reliability.
                      </div>
                    </div>
                  )}
                </div>
              </div>
            </CardContent>
          </Card>
        )}

        {/* User Management Table */}
        <Card>
          <CardHeader>
            <CardTitle>User Management</CardTitle>
            <CardDescription>
              Manage user accounts, subscriptions, and usage limits
            </CardDescription>
          </CardHeader>
          <CardContent>
            {usersLoading ? (
              <div className="text-center py-8">Loading users...</div>
            ) : (
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>User</TableHead>
                    <TableHead>Subscription</TableHead>
                    <TableHead>Usage</TableHead>
                    <TableHead>Token Costs</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {users.map((user) => (
                    <TableRow key={user.id}>
                      <TableCell>
                        <div>
                          <div className="font-medium">{user.firstName} {user.lastName}</div>
                          <div className="text-sm text-muted-foreground">{user.email}</div>
                        </div>
                      </TableCell>
                      <TableCell>
                        <Badge className={getSubscriptionColor(user.subscriptionType)}>
                          {user.subscriptionType}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        <div className="text-sm">
                          {user.usageCount} / {user.usageLimit}
                          <div className="w-full bg-secondary rounded-full h-1.5 mt-1">
                            <div 
                              className="bg-primary h-1.5 rounded-full" 
                              style={{ width: `${Math.min((user.usageCount / user.usageLimit) * 100, 100)}%` }}
                            />
                          </div>
                        </div>
                      </TableCell>
                      <TableCell>
                        {formatCurrency(user.tokenCosts || 0)}
                      </TableCell>
                      <TableCell>
                        <Badge variant={user.isActive ? "default" : "destructive"}>
                          {user.isActive ? "Active" : "Suspended"}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        <div className="flex space-x-2">
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => {
                              setSelectedUser(user);
                              setEditDialogOpen(true);
                            }}
                          >
                            <Edit className="w-4 h-4" />
                          </Button>
                          
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => resetUsageMutation.mutate(user.id)}
                            disabled={resetUsageMutation.isPending}
                          >
                            <RotateCcw className="w-4 h-4" />
                          </Button>

                          {user.isActive ? (
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => suspendUserMutation.mutate(user.id)}
                              disabled={suspendUserMutation.isPending}
                            >
                              <Ban className="w-4 h-4" />
                            </Button>
                          ) : (
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => activateUserMutation.mutate(user.id)}
                              disabled={activateUserMutation.isPending}
                            >
                              <Play className="w-4 h-4" />
                            </Button>
                          )}
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            )}
          </CardContent>
        </Card>

        {/* Edit User Dialog */}
        <Dialog open={editDialogOpen} onOpenChange={setEditDialogOpen}>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Edit User Subscription</DialogTitle>
              <DialogDescription>
                Update the user's subscription type and usage limits
              </DialogDescription>
            </DialogHeader>
            
            {selectedUser && (
              <EditUserForm
                user={selectedUser}
                onSubmit={(data) => updateSubscriptionMutation.mutate({ userId: selectedUser.id, ...data })}
                isLoading={updateSubscriptionMutation.isPending}
              />
            )}
          </DialogContent>
        </Dialog>
      </div>
    </EnhancedLayout>
  );
}

interface EditUserFormProps {
  user: User;
  onSubmit: (data: { subscriptionType: string; usageLimit: number }) => void;
  isLoading: boolean;
}

function EditUserForm({ user, onSubmit, isLoading }: EditUserFormProps) {
  const [subscriptionType, setSubscriptionType] = useState(user.subscriptionType);
  const [usageLimit, setUsageLimit] = useState(user.usageLimit.toString());

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onSubmit({
      subscriptionType,
      usageLimit: parseInt(usageLimit),
    });
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-4">
      <div>
        <Label htmlFor="subscriptionType">Subscription Type</Label>
        <Select value={subscriptionType} onValueChange={setSubscriptionType}>
          <SelectTrigger>
            <SelectValue />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="free">Free</SelectItem>
            <SelectItem value="pro">Pro</SelectItem>
            <SelectItem value="enterprise">Enterprise</SelectItem>
            <SelectItem value="super-premium">Super Premium</SelectItem>
          </SelectContent>
        </Select>
      </div>
      
      <div>
        <Label htmlFor="usageLimit">Usage Limit</Label>
        <Input
          id="usageLimit"
          type="number"
          value={usageLimit}
          onChange={(e) => setUsageLimit(e.target.value)}
          min="0"
          step="1"
        />
      </div>

      <div className="flex justify-end space-x-2">
        <Button type="submit" disabled={isLoading}>
          {isLoading ? "Updating..." : "Update"}
        </Button>
      </div>
    </form>
  );
}