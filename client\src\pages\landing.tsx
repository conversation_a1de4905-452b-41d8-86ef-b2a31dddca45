import { Card, CardContent } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { 
  Video, 
  Sparkles, 
  Clock, 
  List, 
  Download, 
  Layers, 
  Code, 
  Check, 
  Star,
  Play,
  Copy,
  Share,
  Bookmark,
  Lightbulb
} from "lucide-react";

export default function Landing() {
  const handleGetStarted = () => {
    window.location.href = "/api/login";
  };

  const features = [
    {
      icon: <Sparkles className="w-6 h-6" />,
      title: "AI-Powered Summaries",
      description: "Advanced AI extracts key insights, main points, and actionable takeaways from any YouTube video.",
      color: "bg-primary/10 text-primary"
    },
    {
      icon: <Clock className="w-6 h-6" />,
      title: "Save Hours Daily",
      description: "Transform 30-minute videos into 2-minute summaries without losing important information.",
      color: "bg-accent/10 text-accent"
    },
    {
      icon: <List className="w-6 h-6" />,
      title: "Multiple Formats",
      description: "Get summaries in bullet points, paragraphs, or detailed outlines based on your preference.",
      color: "bg-yellow-100 text-yellow-600"
    },
    {
      icon: <Download className="w-6 h-6" />,
      title: "Export & Save",
      description: "Export summaries to PDF, Word, or Markdown. Organize with tags and search through your library.",
      color: "bg-purple-100 text-purple-600"
    },
    {
      icon: <Layers className="w-6 h-6" />,
      title: "Batch Processing",
      description: "Process multiple videos at once. Perfect for research, competitive analysis, or content curation.",
      color: "bg-red-100 text-red-600"
    },
    {
      icon: <Code className="w-6 h-6" />,
      title: "API Access",
      description: "Integrate with your workflow using our developer-friendly API. Perfect for automation and custom tools.",
      color: "bg-blue-100 text-blue-600"
    }
  ];

  const freeFeatures = [
    "5 summaries per month",
    "Basic AI summarization",
    "Copy to clipboard",
    "Standard summary format"
  ];

  const proFeatures = [
    "100 summaries per month",
    "Advanced AI summarization",
    "Custom summary length & style",
    "Batch processing",
    "Save & organize summaries",
    "Export to PDF, Word, Markdown",
    "Priority processing"
  ];

  const enterpriseFeatures = [
    "400 summaries per month",
    "All Pro features included",
    "Custom vector database",
    "MCP server connection",
    "Evergreen data warehouse for AI",
    "API access",
    "Priority support",
    "Custom integrations"
  ];

  return (
    <div className="min-h-screen bg-slate-50">
      {/* Auth Actions Bar */}
      <div className="bg-white border-b border-slate-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-12">
            <div className="hidden md:flex items-center space-x-8">
              <a href="#features" className="text-slate-600 hover:text-primary transition-colors text-sm">Features</a>
              <a href="#pricing" className="text-slate-600 hover:text-primary transition-colors text-sm">Pricing</a>
            </div>
            <div className="flex items-center space-x-4">
              <Button 
                variant="ghost" 
                size="sm"
                onClick={() => window.location.href = "/api/login"}
                className="text-slate-600 hover:text-primary"
              >
                Sign In
              </Button>
              <Button size="sm" onClick={handleGetStarted}>
                Get Started Free
              </Button>
            </div>
          </div>
        </div>
      </div>

      {/* Hero Section */}
      <section className="py-20 lg:py-28">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <h1 className="text-4xl md:text-6xl font-bold text-secondary mb-6">
              Transform YouTube Videos into{" "}
              <span className="text-primary">Actionable Summaries</span>
            </h1>
            <p className="text-xl text-slate-600 mb-8 max-w-3xl mx-auto">
              Save hours of watch time and learn faster with AI-powered video summaries. Get key insights, main points, and actionable takeaways in seconds.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center items-center mb-12">
              <Button 
                size="lg" 
                onClick={handleGetStarted}
                className="px-8 py-4 text-lg font-semibold"
              >
                Start Summarizing Free
              </Button>
              <Button 
                variant="ghost" 
                size="lg"
                className="text-slate-600 hover:text-primary flex items-center"
              >
                <Play className="w-5 h-5 mr-2" />
                Watch Demo
              </Button>
            </div>
            
            {/* Demo Summary Card */}
            <Card className="max-w-4xl mx-auto shadow-lg">
              <CardContent className="p-6 text-left">
                <div className="flex items-start space-x-4 mb-4">
                  <img 
                    src="https://images.unsplash.com/photo-1611224923853-80b023f02d71?ixlib=rb-4.0.3&auto=format&fit=crop&w=120&h=68" 
                    alt="Sample video thumbnail" 
                    className="w-20 h-12 rounded object-cover"
                  />
                  <div className="flex-1">
                    <h3 className="font-semibold text-secondary mb-1">
                      "10 Productivity Hacks That Changed My Life"
                    </h3>
                    <p className="text-sm text-slate-500">TechTalk Channel • 2.1M views • 15:32</p>
                  </div>
                  <div className="text-sm text-slate-500 flex items-center">
                    <Clock className="w-4 h-4 mr-1" />
                    2 min read
                  </div>
                </div>
                
                <div className="prose prose-slate max-w-none">
                  <div className="bg-slate-50 p-4 rounded-lg mb-4">
                    <h4 className="font-semibold text-secondary mb-2 flex items-center">
                      <Lightbulb className="w-5 h-5 text-yellow-500 mr-2" />
                      Key Takeaways:
                    </h4>
                    <ul className="space-y-2 text-slate-700">
                      <li>• Time-blocking increases productivity by 40% when used consistently</li>
                      <li>• The "2-minute rule" eliminates procrastination on small tasks</li>
                      <li>• Digital detox periods improve focus and reduce decision fatigue</li>
                      <li>• Morning routines create momentum for the entire day</li>
                    </ul>
                  </div>
                  
                  <div className="flex items-center justify-between pt-4 border-t border-slate-200">
                    <div className="text-sm text-slate-500 flex items-center">
                      <Clock className="w-4 h-4 mr-1" />
                      Time saved: 13 minutes
                    </div>
                    <div className="flex space-x-3">
                      <Button variant="ghost" size="sm">
                        <Copy className="w-4 h-4" />
                      </Button>
                      <Button variant="ghost" size="sm">
                        <Share className="w-4 h-4" />
                      </Button>
                      <Button variant="ghost" size="sm">
                        <Bookmark className="w-4 h-4" />
                      </Button>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section id="features" className="py-20 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-secondary mb-4">
              Everything you need to consume content faster
            </h2>
            <p className="text-xl text-slate-600 max-w-2xl mx-auto">
              Powerful features designed to help you extract maximum value from video content in minimum time.
            </p>
          </div>
          
          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            {features.map((feature, index) => (
              <div key={index} className="text-center p-6">
                <div className={`w-16 h-16 ${feature.color} rounded-xl flex items-center justify-center mx-auto mb-4`}>
                  {feature.icon}
                </div>
                <h3 className="text-xl font-semibold text-secondary mb-3">{feature.title}</h3>
                <p className="text-slate-600">{feature.description}</p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Pricing Section */}
      <section id="pricing" className="py-20 bg-slate-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-secondary mb-4">
              Simple, transparent pricing
            </h2>
            <p className="text-xl text-slate-600">
              Start free, upgrade when you need more. No hidden fees.
            </p>
          </div>
          
          <div className="grid md:grid-cols-3 gap-8 max-w-6xl mx-auto">
            {/* Free Tier */}
            <Card className="border-2 border-slate-200">
              <CardContent className="p-6">
                <div className="text-center mb-6">
                  <h3 className="text-xl font-bold text-secondary mb-2">Free</h3>
                  <div className="text-3xl font-bold text-secondary mb-2">$0</div>
                  <p className="text-slate-600 text-sm">Perfect for trying out the service</p>
                </div>
                
                <ul className="space-y-3 mb-6">
                  {freeFeatures.map((feature, index) => (
                    <li key={index} className="flex items-center text-sm">
                      <Check className="w-4 h-4 text-accent mr-2 flex-shrink-0" />
                      <span>{feature}</span>
                    </li>
                  ))}
                  <li className="flex items-center text-slate-400 text-sm">
                    <span className="w-4 h-4 mr-2 flex-shrink-0">✕</span>
                    <span>Save summaries</span>
                  </li>
                  <li className="flex items-center text-slate-400 text-sm">
                    <span className="w-4 h-4 mr-2 flex-shrink-0">✕</span>
                    <span>Export to PDF/Word</span>
                  </li>
                </ul>
                
                <Button 
                  variant="outline" 
                  className="w-full" 
                  size="lg"
                  onClick={handleGetStarted}
                >
                  Get Started Free
                </Button>
              </CardContent>
            </Card>
            
            {/* Pro Tier */}
            <Card className="border-2 border-primary relative">
              <div className="absolute -top-4 left-1/2 transform -translate-x-1/2">
                <div className="bg-primary text-white px-4 py-1 rounded-full text-sm font-semibold flex items-center">
                  <Star className="w-4 h-4 mr-1" />
                  Most Popular
                </div>
              </div>
              
              <CardContent className="p-6">
                <div className="text-center mb-6">
                  <h3 className="text-xl font-bold text-secondary mb-2">Pro</h3>
                  <div className="text-3xl font-bold text-secondary mb-2">
                    $19.99
                    <span className="text-lg font-normal text-slate-500">/month</span>
                  </div>
                  <p className="text-slate-600 text-sm">For power users and professionals</p>
                </div>
                
                <ul className="space-y-3 mb-6">
                  {proFeatures.map((feature, index) => (
                    <li key={index} className="flex items-center text-sm">
                      <Check className="w-4 h-4 text-accent mr-2 flex-shrink-0" />
                      <span className={index === 0 ? "font-semibold" : ""}>{feature}</span>
                    </li>
                  ))}
                </ul>
                
                <Button 
                  className="w-full" 
                  size="lg"
                  onClick={handleGetStarted}
                >
                  Start Pro Trial
                </Button>
                
                <p className="text-center text-sm text-slate-500 mt-4">
                  7-day free trial, cancel anytime
                </p>
              </CardContent>
            </Card>

            {/* Enterprise Tier */}
            <Card className="border-2 border-slate-300 relative opacity-75">
              <div className="absolute -top-4 left-1/2 transform -translate-x-1/2">
                <div className="bg-slate-600 text-white px-4 py-1 rounded-full text-sm font-semibold">
                  Coming Soon
                </div>
              </div>
              
              <CardContent className="p-6">
                <div className="text-center mb-6">
                  <h3 className="text-xl font-bold text-secondary mb-2">Enterprise</h3>
                  <div className="text-3xl font-bold text-secondary mb-2">
                    Custom Pricing
                  </div>
                  <p className="text-slate-600 text-sm">Advanced AI infrastructure</p>
                </div>
                
                <ul className="space-y-3 mb-6">
                  {enterpriseFeatures.map((feature, index) => (
                    <li key={index} className="flex items-center text-sm">
                      <Check className="w-4 h-4 text-slate-400 mr-2 flex-shrink-0" />
                      <span className={index === 0 ? "font-semibold text-slate-600" : "text-slate-600"}>{feature}</span>
                    </li>
                  ))}
                </ul>
                
                <Button 
                  variant="outline" 
                  className="w-full" 
                  size="lg"
                  disabled
                >
                  Coming Soon
                </Button>
                
                <p className="text-center text-sm text-slate-500 mt-4">
                  Notify me when available
                </p>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>
    </div>
  );
}
