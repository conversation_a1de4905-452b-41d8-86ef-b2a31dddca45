import { useState } from "react";
import { useMutation, useQ<PERSON>y, useQueryClient } from "@tanstack/react-query";
import { apiRequest } from "@/lib/queryClient";
import { useToast } from "@/hooks/use-toast";
import { useLocation } from "wouter";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import {
  Plus,
  Sparkles,
  MonitorSpeaker,
  Tag,
  Loader2,
  FileText,
  Youtube,
  Folder,
  AlertCircle,
  Library,
  Upload,
} from "lucide-react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";

// Form schemas
const summaryFormSchema = z.object({
  url: z.string().url("Please enter a valid YouTube URL"),
});

const channelFormSchema = z.object({
  channelUrl: z.string().min(1, "Channel URL is required"),
});

const topicFormSchema = z.object({
  name: z.string().min(1, "Topic name is required").max(50, "Topic name must be less than 50 characters"),
  description: z.string().optional(),
  colorHex: z.string().optional(),
});

interface User {
  id: string;
  email: string;
  firstName: string;
  lastName: string;
  usageCount: number;
  usageLimit: number;
  subscriptionType: string;
}

interface Summary {
  id: number;
  videoId: string;
  videoTitle: string;
  videoChannel: string;
  videoThumbnail: string;
  summaryContent: string;
  readTime: string;
  timeSaved: string;
  createdAt: string;
}

interface Channel {
  id: number;
  channelId: string;
  channelName: string;
  channelThumbnail: string;
  subscriberCount: string;
  isActive: boolean;
  videoCount: number;
  createdAt: string;
}

interface Topic {
  id: number;
  name: string;
  description: string | null;
  colorHex: string | null;
  topicType: 'auto_extracted' | 'user_defined' | 'playlist';
}

function isUnauthorizedError(error: any): boolean {
  return error?.message?.includes("Unauthorized") || error?.status === 401;
}

export default function CreatePage() {
  const [activeTab, setActiveTab] = useState("summary");
  const [, setLocation] = useLocation();
  const { toast } = useToast();
  const queryClient = useQueryClient();
  
  // Bulk import state
  const [isBulkChannelImporting, setIsBulkChannelImporting] = useState(false);
  const [isBulkVideoImporting, setIsBulkVideoImporting] = useState(false);
  const [bulkChannels, setBulkChannels] = useState("");
  const [bulkVideos, setBulkVideos] = useState("");

  // Fetch user data
  const { data: user } = useQuery<User>({
    queryKey: ["/api/auth/user"],
  });

  // Fetch topics for parent selection
  const { data: topics = [] } = useQuery<Topic[]>({
    queryKey: ["/api/topics"],
  });

  // Fetch recent activity for the activity section
  const { data: recentSummaries = [] } = useQuery<Summary[]>({
    queryKey: ["/api/summaries"],
  });

  // Fetch monitored summaries for recent activity
  const { data: monitoredSummaries = [] } = useQuery<Summary[]>({
    queryKey: ["/api/monitored-summaries"],
  });

  // Form instances
  const summaryForm = useForm<z.infer<typeof summaryFormSchema>>({
    resolver: zodResolver(summaryFormSchema),
    defaultValues: {
      url: "",
    },
  });

  const channelForm = useForm<z.infer<typeof channelFormSchema>>({
    resolver: zodResolver(channelFormSchema),
    defaultValues: {
      channelUrl: "",
    },
  });

  const topicForm = useForm<z.infer<typeof topicFormSchema>>({
    resolver: zodResolver(topicFormSchema),
    defaultValues: {
      name: "",
      description: "",
      colorHex: "",
    },
  });

  // Create summary mutation
  const createSummaryMutation = useMutation({
    mutationFn: async (data: z.infer<typeof summaryFormSchema>) => {
      const response = await apiRequest("POST", "/api/summaries", { url: data.url });
      return await response.json();
    },
    onSuccess: (summary: Summary) => {
      summaryForm.reset();
      queryClient.invalidateQueries({ queryKey: ["/api/summaries"] });
      queryClient.invalidateQueries({ queryKey: ["/api/auth/user"] });
      toast({
        title: "Summary Created",
        description: "Your video summary has been generated successfully!",
      });
      setLocation(`/summary/${summary.id}`);
    },
    onError: (error) => {
      if (isUnauthorizedError(error)) {
        toast({
          title: "Unauthorized",
          description: "You are logged out. Logging in again...",
          variant: "destructive",
        });
        setTimeout(() => {
          window.location.href = "/api/login";
        }, 500);
        return;
      }
      
      const errorMessage = error.message;
      if (errorMessage.includes("USAGE_LIMIT_EXCEEDED")) {
        toast({
          title: "Usage Limit Exceeded",
          description: "You've reached your monthly limit. Please upgrade to Pro for unlimited summaries.",
          variant: "destructive",
        });
      } else {
        toast({
          title: "Error",
          description: "Failed to generate summary. Please try again.",
          variant: "destructive",
        });
      }
    },
  });

  // Create channel mutation
  const createChannelMutation = useMutation({
    mutationFn: async (data: z.infer<typeof channelFormSchema>) => {
      const response = await apiRequest("POST", "/api/channels", { channelUrl: data.channelUrl });
      return response.json();
    },
    onSuccess: (channel: Channel) => {
      channelForm.reset();
      queryClient.invalidateQueries({ queryKey: ["/api/channels"] });
      toast({
        title: "Channel Added",
        description: "Channel monitoring started successfully",
      });
      setLocation("/channels");
    },
    onError: (error) => {
      if (isUnauthorizedError(error)) {
        toast({
          title: "Unauthorized",
          description: "You are logged out. Logging in again...",
          variant: "destructive",
        });
        setTimeout(() => {
          window.location.href = "/api/login";
        }, 500);
        return;
      }
      toast({
        title: "Error",
        description: error.message || "Failed to add channel",
        variant: "destructive",
      });
    },
  });

  // Create topic mutation
  const createTopicMutation = useMutation({
    mutationFn: async (data: z.infer<typeof topicFormSchema>) => {
      const payload = {
        name: data.name,
        description: data.description || null,
        colorHex: data.colorHex || null,
        topicType: 'user_defined' as const,
      };
      const response = await apiRequest("POST", "/api/topics", payload);
      return response.json();
    },
    onSuccess: (topic: Topic) => {
      topicForm.reset();
      queryClient.invalidateQueries({ queryKey: ["/api/topics"] });
      toast({
        title: "Topic Created",
        description: "Your topic has been created successfully!",
      });
      setLocation("/topics");
    },
    onError: (error) => {
      if (isUnauthorizedError(error)) {
        toast({
          title: "Unauthorized",
          description: "You are logged out. Logging in again...",
          variant: "destructive",
        });
        setTimeout(() => {
          window.location.href = "/api/login";
        }, 500);
        return;
      }
      toast({
        title: "Error",
        description: error.message || "Failed to create topic",
        variant: "destructive",
      });
    },
  });

  // Bulk import channels mutation
  const bulkChannelImportMutation = useMutation({
    mutationFn: async ({ channelUrls }: { channelUrls: string[] }) => {
      return await apiRequest("POST", "/api/channels/bulk", { channelUrls });
    },
    onSuccess: (data: any) => {
      queryClient.invalidateQueries({ queryKey: ["/api/channels"] });
      setIsBulkChannelImporting(false);
      setBulkChannels("");
      toast({
        title: "Bulk Import Complete",
        description: `${data.successful || 0} channels added successfully. ${data.failed || 0} failed.`,
      });
    },
    onError: (error) => {
      if (isUnauthorizedError(error)) {
        toast({
          title: "Unauthorized",
          description: "You are logged out. Logging in again...",
          variant: "destructive",
        });
        setTimeout(() => {
          window.location.href = "/api/login";
        }, 500);
        return;
      }
      toast({
        title: "Error",
        description: error.message || "Failed to import channels",
        variant: "destructive",
      });
    },
  });

  // Bulk import videos mutation (processes all recent videos)
  const bulkVideoImportMutation = useMutation({
    mutationFn: async ({ videoUrls }: { videoUrls: string[] }) => {
      // Process multiple video URLs
      const results = await Promise.allSettled(
        videoUrls.map(url => apiRequest("POST", "/api/summaries", { url }))
      );
      return results;
    },
    onSuccess: (results: any) => {
      queryClient.invalidateQueries({ queryKey: ["/api/summaries"] });
      setIsBulkVideoImporting(false);
      setBulkVideos("");
      const successful = results.filter((r: any) => r.status === 'fulfilled').length;
      const failed = results.filter((r: any) => r.status === 'rejected').length;
      toast({
        title: "Bulk Video Import Complete",
        description: `${successful} videos processed successfully. ${failed} failed.`,
      });
    },
    onError: (error) => {
      if (isUnauthorizedError(error)) {
        toast({
          title: "Unauthorized",
          description: "You are logged out. Logging in again...",
          variant: "destructive",
        });
        setTimeout(() => {
          window.location.href = "/api/login";
        }, 500);
        return;
      }
      toast({
        title: "Error",
        description: error.message || "Failed to import videos",
        variant: "destructive",
      });
    },
  });

  // Bulk import handlers
  const handleBulkChannelImport = () => {
    if (!bulkChannels.trim()) return;
    
    const urls = bulkChannels
      .split(/[\n,;]/)
      .map(url => url.trim())
      .filter(url => url.length > 0)
      .filter(url => 
        url.includes('youtube.com') || 
        url.includes('youtu.be') || 
        url.startsWith('@') || 
        url.startsWith('UC') ||
        url.startsWith('HC') ||
        url.startsWith('UU')
      );

    if (urls.length === 0) {
      toast({
        title: "No Valid URLs",
        description: "Please enter valid YouTube channel URLs, handles, or IDs",
        variant: "destructive",
      });
      return;
    }

    bulkChannelImportMutation.mutate({ channelUrls: urls });
  };

  const handleBulkVideoImport = () => {
    if (!bulkVideos.trim()) return;
    
    const urls = bulkVideos
      .split(/[\n,;]/)
      .map(url => url.trim())
      .filter(url => url.length > 0)
      .filter(url => 
        url.includes('youtube.com') || 
        url.includes('youtu.be')
      );

    if (urls.length === 0) {
      toast({
        title: "No Valid URLs",
        description: "Please enter valid YouTube video URLs",
        variant: "destructive",
      });
      return;
    }

    bulkVideoImportMutation.mutate({ videoUrls: urls });
  };

  const onSubmitSummary = (data: z.infer<typeof summaryFormSchema>) => {
    if (!user) return;
    
    const currentLimit = user.subscriptionType === 'pro' ? 100 : 
                      user.subscriptionType === 'enterprise' ? 400 :
                      user.subscriptionType === 'super-premium' ? 999999 : 5;
    
    if ((user.usageCount ?? 0) >= currentLimit) {
      toast({
        title: "Usage Limit Exceeded",
        description: "You've reached your monthly limit. Please upgrade to Pro for unlimited summaries.",
        variant: "destructive",
      });
      return;
    }

    createSummaryMutation.mutate(data);
  };

  const onSubmitChannel = (data: z.infer<typeof channelFormSchema>) => {
    createChannelMutation.mutate(data);
  };

  const onSubmitTopic = (data: z.infer<typeof topicFormSchema>) => {
    createTopicMutation.mutate(data);
  };

  // Helper function to handle video click
  const handleVideoClick = (summary: Summary) => {
    setLocation(`/summary/${summary.id}`);
  };

  // Combine and sort recent activity, removing duplicates by ID
  const uniqueSummaries = new Map();
  [...recentSummaries, ...monitoredSummaries].forEach(summary => {
    uniqueSummaries.set(summary.id, summary);
  });
  const allRecentActivity = Array.from(uniqueSummaries.values())
    .sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime())
    .slice(0, 6);

  return (
    <div className="container mx-auto px-3 sm:px-6 py-4 sm:py-6 max-w-6xl">

      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4 sm:space-y-6">
        <TabsList className="grid w-full grid-cols-3 bg-muted/50 p-1 h-12 sm:h-10">
          <TabsTrigger 
            value="summary" 
            className="flex items-center gap-1 sm:gap-2 data-[state=active]:bg-primary data-[state=active]:text-primary-foreground data-[state=active]:shadow-md data-[state=active]:border data-[state=active]:border-primary/20 font-medium transition-all duration-200 text-xs sm:text-sm px-1 sm:px-3"
          >
            <FileText className="w-3 h-3 sm:w-4 sm:h-4" />
            <span className="hidden sm:inline">Video Summary</span>
            <span className="sm:hidden">Summary</span>
          </TabsTrigger>
          <TabsTrigger 
            value="channel" 
            className="flex items-center gap-1 sm:gap-2 data-[state=active]:bg-primary data-[state=active]:text-primary-foreground data-[state=active]:shadow-md data-[state=active]:border data-[state=active]:border-primary/20 font-medium transition-all duration-200 text-xs sm:text-sm px-1 sm:px-3"
          >
            <MonitorSpeaker className="w-3 h-3 sm:w-4 sm:h-4" />
            <span className="hidden sm:inline">Channel Monitoring</span>
            <span className="sm:hidden">Monitor</span>
          </TabsTrigger>
          <TabsTrigger 
            value="topic" 
            className="flex items-center gap-1 sm:gap-2 data-[state=active]:bg-primary data-[state=active]:text-primary-foreground data-[state=active]:shadow-md data-[state=active]:border data-[state=active]:border-primary/20 font-medium transition-all duration-200 text-xs sm:text-sm px-1 sm:px-3"
          >
            <Tag className="w-3 h-3 sm:w-4 sm:h-4" />
            <span className="hidden sm:inline">Topic Organization</span>
            <span className="sm:hidden">Topics</span>
          </TabsTrigger>
        </TabsList>

        {/* Video Summary Tab */}
        <TabsContent value="summary" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Sparkles className="w-5 h-5 text-primary" />
                Create Video Summary
              </CardTitle>
              <CardDescription>
                Transform any YouTube video into actionable insights in seconds
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Form {...summaryForm}>
                <form onSubmit={summaryForm.handleSubmit(onSubmitSummary)} className="space-y-4">
                  <FormField
                    control={summaryForm.control}
                    name="url"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>YouTube URL</FormLabel>
                        <FormControl>
                          <Input
                            placeholder="https://www.youtube.com/watch?v=..."
                            {...field}
                          />
                        </FormControl>
                        <FormDescription>
                          Paste the URL of any YouTube video to generate a comprehensive summary
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  
                  {user && (
                    <div className="p-4 bg-muted rounded-lg">
                      <div className="flex items-center justify-between">
                        <span className="text-sm font-medium">Usage this month:</span>
                        <Badge variant="outline">
                          {user.usageCount || 0} / {user.usageLimit} summaries
                        </Badge>
                      </div>
                      {((user.usageCount || 0) / user.usageLimit) > 0.8 && (
                        <div className="flex items-center gap-2 mt-2 text-amber-600">
                          <AlertCircle className="w-4 h-4" />
                          <span className="text-sm">Approaching usage limit</span>
                        </div>
                      )}
                    </div>
                  )}

                  <div className="flex flex-col sm:flex-row gap-2">
                    <Button 
                      type="submit" 
                      disabled={createSummaryMutation.isPending}
                      className="w-full sm:flex-1"
                      size="lg"
                    >
                      {createSummaryMutation.isPending ? (
                        <>
                          <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                          Creating Summary...
                        </>
                      ) : (
                        <>
                          <Sparkles className="w-4 h-4 mr-2" />
                          Create Summary
                        </>
                      )}
                    </Button>
                    
                    <Dialog open={isBulkVideoImporting} onOpenChange={setIsBulkVideoImporting}>
                      <DialogTrigger asChild>
                        <Button variant="outline" size="lg" className="hidden sm:flex">
                          <Upload className="w-4 h-4 mr-2" />
                          Bulk Import
                        </Button>
                      </DialogTrigger>
                      <DialogContent className="max-w-lg mx-3 sm:mx-auto max-h-[90vh] overflow-y-auto">
                        <DialogHeader>
                          <DialogTitle>Bulk Import Videos</DialogTitle>
                          <DialogDescription>
                            Add multiple YouTube videos at once. Enter video URLs - one per line.
                          </DialogDescription>
                        </DialogHeader>
                        <div className="space-y-4">
                          <div className="space-y-2">
                            <label className="text-sm font-medium">Video URLs</label>
                            <Textarea
                              placeholder={`Examples:
https://www.youtube.com/watch?v=videoID1
https://youtu.be/videoID2
https://www.youtube.com/watch?v=videoID3

Separate multiple entries with new lines, commas, or semicolons.`}
                              value={bulkVideos}
                              onChange={(e) => setBulkVideos(e.target.value)}
                              className="min-h-32"
                            />
                          </div>
                          <div className="flex justify-end gap-2">
                            <Button variant="outline" onClick={() => setIsBulkVideoImporting(false)}>
                              Cancel
                            </Button>
                            <Button 
                              onClick={handleBulkVideoImport}
                              disabled={!bulkVideos.trim() || bulkVideoImportMutation.isPending}
                            >
                              {bulkVideoImportMutation.isPending ? "Importing..." : "Import Videos"}
                            </Button>
                          </div>
                        </div>
                      </DialogContent>
                    </Dialog>
                  </div>
                </form>
              </Form>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Channel Monitoring Tab */}
        <TabsContent value="channel" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Youtube className="w-5 h-5 text-red-500" />
                Add Channel for Monitoring
              </CardTitle>
              <CardDescription>
                Monitor YouTube channels for automatic summaries of new videos
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Form {...channelForm}>
                <form onSubmit={channelForm.handleSubmit(onSubmitChannel)} className="space-y-4">
                  <FormField
                    control={channelForm.control}
                    name="channelUrl"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Channel URL or Handle</FormLabel>
                        <FormControl>
                          <Input
                            placeholder="https://youtube.com/@channelname or @channelname"
                            {...field}
                          />
                        </FormControl>
                        <FormDescription>
                          Enter a YouTube channel URL, handle (@channel), or channel ID
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <div className="p-4 bg-blue-50 dark:bg-blue-950/20 rounded-lg border border-blue-200 dark:border-blue-800">
                    <h4 className="font-medium text-blue-900 dark:text-blue-100 mb-2">
                      What happens after adding a channel?
                    </h4>
                    <ul className="text-sm text-blue-700 dark:text-blue-300 space-y-1">
                      <li>• 3 most recent videos are summarized automatically</li>
                      <li>• Automatic monitoring for new video uploads</li>
                      <li>• AI-generated summaries for each new video</li>
                      <li>• RSS feed for the channel's summaries</li>
                      <li>• Topic-based organization and search</li>
                    </ul>
                  </div>

                  <div className="flex gap-2">
                    <Button 
                      type="submit" 
                      disabled={createChannelMutation.isPending}
                      className="flex-1"
                      size="lg"
                    >
                      {createChannelMutation.isPending ? (
                        <>
                          <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                          Adding Channel...
                        </>
                      ) : (
                        <>
                          <Plus className="w-4 h-4 mr-2" />
                          Start Monitoring Channel
                        </>
                      )}
                    </Button>
                    
                    <Dialog open={isBulkChannelImporting} onOpenChange={setIsBulkChannelImporting}>
                      <DialogTrigger asChild>
                        <Button variant="outline" size="lg" className="hidden sm:flex">
                          <Upload className="w-4 h-4 mr-2" />
                          Bulk Import
                        </Button>
                      </DialogTrigger>
                      <DialogContent className="max-w-lg mx-3 sm:mx-auto max-h-[90vh] overflow-y-auto">
                        <DialogHeader>
                          <DialogTitle>Bulk Import Channels</DialogTitle>
                          <DialogDescription>
                            Add multiple YouTube channels at once. Enter channel URLs, handles (@channel), or IDs - one per line.
                          </DialogDescription>
                        </DialogHeader>
                        <div className="space-y-4">
                          <div className="space-y-2">
                            <label className="text-sm font-medium">Channel URLs/Handles/IDs</label>
                            <Textarea
                              placeholder={`Examples:
https://www.youtube.com/@channel
@channelhandle
UCChannelID123
https://youtube.com/c/channelname

Separate multiple entries with new lines, commas, or semicolons.`}
                              value={bulkChannels}
                              onChange={(e) => setBulkChannels(e.target.value)}
                              className="min-h-32"
                            />
                          </div>
                          <div className="flex justify-end gap-2">
                            <Button variant="outline" onClick={() => setIsBulkChannelImporting(false)}>
                              Cancel
                            </Button>
                            <Button 
                              onClick={handleBulkChannelImport}
                              disabled={!bulkChannels.trim() || bulkChannelImportMutation.isPending}
                            >
                              {bulkChannelImportMutation.isPending ? "Importing..." : "Import Channels"}
                            </Button>
                          </div>
                        </div>
                      </DialogContent>
                    </Dialog>
                  </div>
                </form>
              </Form>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Topic Organization Tab */}
        <TabsContent value="topic" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Folder className="w-5 h-5 text-purple-500" />
                Create Topic
              </CardTitle>
              <CardDescription>
                Organize your summaries and channels into custom topics
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Form {...topicForm}>
                <form onSubmit={topicForm.handleSubmit(onSubmitTopic)} className="space-y-4">
                  <FormField
                    control={topicForm.control}
                    name="name"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Topic Name</FormLabel>
                        <FormControl>
                          <Input
                            placeholder="e.g., AI & Machine Learning, Marketing, Tech Reviews"
                            {...field}
                          />
                        </FormControl>
                        <FormDescription>
                          Choose a descriptive name for your topic
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={topicForm.control}
                    name="description"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Description (Optional)</FormLabel>
                        <FormControl>
                          <Textarea
                            placeholder="Describe what this topic covers..."
                            className="resize-none"
                            {...field}
                          />
                        </FormControl>
                        <FormDescription>
                          Help others understand what this topic is about
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />



                  <FormField
                    control={topicForm.control}
                    name="colorHex"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Color (Optional)</FormLabel>
                        <FormControl>
                          <Input
                            type="color"
                            {...field}
                            className="w-20 h-10"
                          />
                        </FormControl>
                        <FormDescription>
                          Choose a color to help identify this topic
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <Separator />

                  <div className="p-4 bg-purple-50 dark:bg-purple-950/20 rounded-lg border border-purple-200 dark:border-purple-800">
                    <h4 className="font-medium text-purple-900 dark:text-purple-100 mb-2">
                      Topic Features
                    </h4>
                    <ul className="text-sm text-purple-700 dark:text-purple-300 space-y-1">
                      <li>• Organize channels and summaries by subject</li>
                      <li>• Generate topic-specific RSS feeds</li>
                      <li>• Create hierarchical topic structures</li>
                      <li>• Filter content by topics in your library</li>
                    </ul>
                  </div>

                  <Button 
                    type="submit" 
                    disabled={createTopicMutation.isPending}
                    className="w-full"
                    size="lg"
                  >
                    {createTopicMutation.isPending ? (
                      <>
                        <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                        Creating Topic...
                      </>
                    ) : (
                      <>
                        <Tag className="w-4 h-4 mr-2" />
                        Create Topic
                      </>
                    )}
                  </Button>
                </form>
              </Form>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* Recent Activity Section */}
      {allRecentActivity.length > 0 && (
        <div className="mt-12 space-y-6">
          <div className="flex items-center justify-between">
            <div>
              <h2 className="text-2xl font-bold">Recent Activity</h2>
              <p className="text-muted-foreground">
                Your latest video summaries and channel updates
              </p>
            </div>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {allRecentActivity.map((summary: Summary) => (
              <Card 
                key={summary.id} 
                className="cursor-pointer hover:shadow-md transition-shadow group"
                onClick={() => handleVideoClick(summary)}
              >
                <CardContent className="p-4">
                  <div className="flex items-start space-x-3">
                    <img 
                      src={summary.videoThumbnail} 
                      alt={summary.videoTitle}
                      className="w-16 h-12 object-cover rounded flex-shrink-0"
                    />
                    <div className="flex-1 min-w-0">
                      <p className="text-sm font-medium line-clamp-2 mb-1 group-hover:text-primary transition-colors">
                        {summary.videoTitle}
                      </p>
                      <p className="text-xs text-muted-foreground mb-2">
                        {summary.videoChannel}
                      </p>
                      <div className="flex items-center justify-between">
                        <Badge variant="secondary" className="text-xs">
                          {summary.readTime}
                        </Badge>
                        <span className="text-xs text-muted-foreground">
                          {new Date(summary.createdAt).toLocaleDateString()}
                        </span>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
          
          <div className="text-center">
            <Button 
              variant="outline" 
              onClick={() => setLocation("/library")}
              className="w-full max-w-xs"
            >
              <Library className="w-4 h-4 mr-2" />
              View All Summaries
            </Button>
          </div>
        </div>
      )}
    </div>
  );
}