template:
  id: agent-teams-template-v2
  name: Agent Team Configuration
  version: 2.0
  output:
    format: yaml
    filename: "agent-teams/{{team_name}}.yaml"
    title: "{{team_name}}"

workflow:
  mode: interactive

sections:
  - id: header
    title: Agent Team Configuration Template
    instruction: |
      This template is for creating agent team configurations in YAML format. Follow the structure carefully and replace all placeholders with appropriate values. The team name should reflect the team's purpose and domain focus.

  - id: yaml-configuration
    type: code
    language: yaml
    template: |
      bundle:
        name: {{team_display_name}}
        icon: {{team_emoji}}
        description: {{team_description}}
      
      agents:
        {{agent_list}}
      
      workflows:
        {{workflow_list}}
    instruction: |
      Use format "Team [Descriptor]" for generic teams or "[Domain] Team" for specialized teams. Examples: "Team Fullstack", "Healthcare Team", "Legal Team"
      
      Choose a single emoji that best represents the team's function or name
      
      Write a concise description (1 sentence) that explains:
      1. The team's primary purpose
      2. What types of projects they handle
      3. Any special capabilities or focus areas
      4. Keep it short as its displayed in menus
      Example: "Full Stack Ideation Web App Team." or "Startup Business Coaching team"
      
      List the agents that make up this team. Guidelines:
      - Use shortened agent names (e.g., 'analyst' not 'business-analyst')
      - Include 'bmad-orchestrator' for bmad-core teams as the coordinator
      - Only use '*' for an all-inclusive team (rare)
      - Order agents logically by workflow (analysis → design → development → testing)
      - For expansion packs, include both core agents and custom agents
      
      Define the workflows this team can execute that will guide the user through a multi-step multi agent process. Guidelines:
      - Use null if the team doesn't have predefined workflows
      - Workflow names should be descriptive
      - use domain-specific workflow names
    sections:
      - id: standard-team
        condition: Standard team configuration
        template: |
          # Core workflow agents
          - bmad-orchestrator  # Team coordinator
          - analyst            # Requirements and analysis
          - pm                 # Product management
          - architect          # System design
          - dev                # Development
          - qa                 # Quality assurance
      - id: minimal-team
        condition: Minimal team configuration
        template: |
          # Minimal team for quick iterations
          - bmad-orchestrator  # Team coordinator
          - architect          # Design and planning
          - dev                # Implementation
      - id: specialized-team
        condition: Domain-specific team
        template: |
          # Domain-specific team composition
          - {{domain}}-orchestrator  # Domain coordinator
          - {{agent_short_name}}     # {{agent_role_description}}
      - id: all-agents
        condition: Include all available agents
        template: |
          - '*'  # Include all available agents
      - id: no-workflows
        condition: No predefined workflows
        template: |
          null  # No predefined workflows
      - id: standard-workflows
        condition: Standard project workflows
        template: |
          # New project workflows
          - greenfield-fullstack  # New full-stack application
          - greenfield-service    # New backend service
          - greenfield-ui         # New frontend application
          
          # Existing project workflows
          - brownfield-fullstack  # Enhance existing full-stack app
          - brownfield-service    # Enhance existing service
          - brownfield-ui         # Enhance existing UI
      - id: domain-workflows
        condition: Domain-specific workflows
        template: |
          # Domain-specific workflows
          - {{workflow_name}}  # {{workflow_description}}

  - id: examples
    title: Examples
    sections:
      - id: example-1
        title: "Example 1: Standard fullstack team"
        type: code
        language: yaml
        template: |
          bundle:
            name: Team Fullstack
            icon: 🚀
            description: Complete agile team for full-stack web applications. Handles everything from requirements to deployment.
          agents:
            - bmad-orchestrator
            - analyst
            - pm
            - architect
            - dev
            - qa
            - ux-expert
          workflows:
            - greenfield-fullstack
            - greenfield-service
            - greenfield-ui
            - brownfield-fullstack
            - brownfield-service
            - brownfield-ui
      - id: example-2
        title: "Example 2: Healthcare expansion pack team"
        type: code
        language: yaml
        template: |
          bundle:
            name: Healthcare Compliance Team
            icon: ⚕️
            description: Specialized team for healthcare applications with HIPAA compliance focus. Manages clinical workflows and regulatory requirements.
          agents:
            - healthcare-orchestrator
            - clinical-analyst
            - compliance-officer
            - architect
            - dev
            - qa
          workflows:
            - healthcare-patient-portal
            - healthcare-compliance-audit
            - clinical-trial-management
      - id: example-3
        title: "Example 3: Minimal IDE team"
        type: code
        language: yaml
        template: |
          bundle:
            name: Team IDE Minimal
            icon: ⚡
            description: Minimal team for IDE usage. Just the essentials for quick development.
          agents:
            - bmad-orchestrator
            - architect
            - dev
          workflows: null

  - id: creation-instructions
    instruction: |
      When creating a new team configuration:
      
      1. Choose the most appropriate condition block based on team type
      2. Remove all unused condition blocks
      3. Replace all placeholders with actual values
      4. Ensure agent names match available agents in the system
      5. Verify workflow names match available workflows
      6. Save as team-[descriptor].yaml or [domain]-team.yaml
      7. Place in the agent-teams directory of the appropriate location