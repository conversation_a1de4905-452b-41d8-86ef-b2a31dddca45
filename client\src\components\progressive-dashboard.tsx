import { useState, useEffect } from "react";
import { useAuth } from "@/hooks/useAuth";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { useToast } from "@/hooks/use-toast";
import { useLocation } from "wouter";
import { apiRequest } from "@/lib/queryClient";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { 
  Video, 
  Plus, 
  Library, 
  MonitorSpeaker,
  Tag,
  Clock,
  TrendingUp,
  Zap,
  ChevronDown,
  ChevronUp,
  Eye,
  EyeOff,
  BarChart3,
  Activity
} from "lucide-react";
import { cn } from "@/lib/utils";
import ContentCard from "@/components/ui/content-card";

interface User {
  id: string;
  email: string;
  firstName: string;
  lastName: string;
  usageCount: number;
  subscriptionType: string;
}

interface Summary {
  id: number;
  videoId: string;
  videoTitle: string;
  videoChannel: string;
  videoThumbnail: string;
  videoDuration: string;
  summaryContent: string;
  readTime: string;
  timeSaved: string;
  createdAt: string;
  channelId?: number;
  isMonitored?: boolean;
}

interface DashboardSection {
  id: string;
  title: string;
  description: string;
  priority: "high" | "medium" | "low";
  isCollapsed: boolean;
  showCount?: number;
}

export default function ProgressiveDashboard() {
  const { user: authUser } = useAuth();
  const [videoUrl, setVideoUrl] = useState("");
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const [, setLocation] = useLocation();
  const [sections, setSections] = useState<Record<string, DashboardSection>>({
    quickCreate: {
      id: "quickCreate",
      title: "Create Summary",
      description: "Primary action - always visible",
      priority: "high",
      isCollapsed: false
    },
    recentActivity: {
      id: "recentActivity", 
      title: "Recent Activity",
      description: "Latest automated summaries",
      priority: "high",
      isCollapsed: false,
      showCount: 3
    },
    library: {
      id: "library",
      title: "Your Library", 
      description: "Browse recent summaries",
      priority: "medium",
      isCollapsed: false,
      showCount: 6
    },
    channels: {
      id: "channels",
      title: "Channel Monitoring",
      description: "Automated content tracking", 
      priority: "medium",
      isCollapsed: true,
      showCount: 4
    },
    topics: {
      id: "topics",
      title: "Topic Organization",
      description: "Content categorization",
      priority: "low", 
      isCollapsed: true,
      showCount: 6
    }
  });

  const [userPreferences, setUserPreferences] = useState({
    showDetailedStats: false,
    compactMode: false,
    autoExpand: true
  });

  // Fetch user data
  const { data: user } = useQuery<User>({
    queryKey: ["/api/auth/user"],
    enabled: !!authUser,
  });

  // Fetch content data
  const { data: summaries = [] } = useQuery<Summary[]>({
    queryKey: ["/api/summaries"],
    enabled: !!authUser,
  });

  const { data: monitoredSummaries = [] } = useQuery<Summary[]>({
    queryKey: ["/api/monitored-summaries"],
    enabled: !!authUser,
  });

  const { data: channels = [] } = useQuery<any[]>({
    queryKey: ["/api/channels"],
    enabled: !!authUser,
  });

  const { data: topics = [] } = useQuery<any[]>({
    queryKey: ["/api/topics"],
    enabled: !!authUser,
  });

  // Progressive disclosure logic
  useEffect(() => {
    if (!user) return;

    // Auto-expand sections based on user activity
    const newSections = { ...sections };

    // New users (< 5 summaries) - focus on core features
    if (summaries.length < 5) {
      newSections.library.isCollapsed = true;
      newSections.channels.isCollapsed = true;
      newSections.topics.isCollapsed = true;
    }

    // Active monitoring users - prioritize recent activity
    if (monitoredSummaries.length > 0) {
      newSections.recentActivity.isCollapsed = false;
      newSections.recentActivity.showCount = Math.min(monitoredSummaries.length, 5);
    }

    // Power users (> 20 summaries) - show more content
    if (summaries.length > 20) {
      newSections.library.showCount = 9;
      newSections.channels.isCollapsed = false;
    }

    setSections(newSections);
  }, [user, summaries.length, monitoredSummaries.length]);

  const toggleSection = (sectionId: string) => {
    setSections(prev => ({
      ...prev,
      [sectionId]: {
        ...prev[sectionId],
        isCollapsed: !prev[sectionId].isCollapsed
      }
    }));
  };

  const expandAll = () => {
    setSections(prev => {
      const updated = { ...prev };
      Object.keys(updated).forEach(key => {
        updated[key].isCollapsed = false;
      });
      return updated;
    });
  };

  const collapseNonEssential = () => {
    setSections(prev => ({
      ...prev,
      channels: { ...prev.channels, isCollapsed: true },
      topics: { ...prev.topics, isCollapsed: true }
    }));
  };

  // Summary creation mutation
  const createSummaryMutation = useMutation({
    mutationFn: async (url: string) => {
      const response = await fetch(`/api/summaries`, {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ url }),
      });
      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.message || "Failed to create summary");
      }
      return response.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["/api/summaries"] });
      queryClient.invalidateQueries({ queryKey: ["/api/auth/user"] });
      setVideoUrl("");
      toast({
        title: "Summary created successfully!",
        description: "Your video summary is ready to view.",
      });
    },
    onError: (error: any) => {
      toast({
        title: "Error creating summary",
        description: error.message || "Failed to create summary. Please try again.",
        variant: "destructive",
      });
    },
  });

  const handleSummarize = () => {
    if (!videoUrl.trim()) return;
    createSummaryMutation.mutate(videoUrl);
  };

  // User activity metrics for cognitive load optimization
  const userMetrics = {
    totalSummaries: summaries.length,
    monitoredChannels: Array.isArray(channels) ? channels.filter((c: any) => c.isMonitored).length : 0,
    activeTopics: Array.isArray(topics) ? topics.length : 0,
    todayActivity: monitoredSummaries.filter(s => 
      new Date(s.createdAt).toDateString() === new Date().toDateString()
    ).length,
    usagePercentage: user ? (user.usageCount / (user.subscriptionType === 'free' ? 5 : 100)) * 100 : 0
  };

  return (
    <div className="container mx-auto px-4 py-6 max-w-6xl">
      
      {/* Cognitive Load Controls */}
      <div className="flex items-center justify-between mb-6">
        <div>
          <h1 className="text-2xl font-bold">Dashboard</h1>
          <p className="text-muted-foreground">
            {userMetrics.todayActivity > 0 
              ? `${userMetrics.todayActivity} new summaries today`
              : "Create your first summary or monitor channels"}
          </p>
        </div>
        
        <div className="flex items-center gap-2">
          <Button 
            variant="outline" 
            size="sm"
            onClick={collapseNonEssential}
          >
            <EyeOff className="h-4 w-4 mr-2" />
            Focus Mode
          </Button>
          <Button 
            variant="outline" 
            size="sm"
            onClick={expandAll}
          >
            <Eye className="h-4 w-4 mr-2" />
            Show All
          </Button>
        </div>
      </div>

      {/* Primary Action - Always Visible */}
      <Card className="mb-8 border-2 border-primary/20 bg-gradient-to-r from-primary/5 to-blue-50 dark:to-blue-950/20">
        <CardContent className="p-6">
          <div className="flex items-center gap-4 mb-4">
            <div className="h-10 w-10 bg-primary rounded-lg flex items-center justify-center">
              <Zap className="h-5 w-5 text-primary-foreground" />
            </div>
            <div>
              <h2 className="text-xl font-semibold">Create New Summary</h2>
              <p className="text-muted-foreground">Transform any YouTube video into actionable insights</p>
            </div>
          </div>
          
          <div className="flex gap-3">
            <Input
              placeholder="Paste YouTube URL here..."
              value={videoUrl}
              onChange={(e) => setVideoUrl(e.target.value)}
              className="flex-1 max-w-lg"
            />
            <Button 
              size="lg" 
              disabled={!videoUrl.trim() || createSummaryMutation.isPending}
              onClick={handleSummarize}
            >
              <Zap className="h-4 w-4 mr-2" />
              {createSummaryMutation.isPending ? "Creating..." : "Summarize"}
            </Button>
          </div>
          
          {user && userMetrics.usagePercentage > 80 && (
            <div className="mt-4 p-3 bg-amber-50 dark:bg-amber-950/20 rounded-lg border border-amber-200 dark:border-amber-800">
              <div className="flex items-center justify-between">
                <span className="text-sm text-amber-800 dark:text-amber-200">
                  Usage: {user.usageCount}/{user.subscriptionType === 'free' ? 5 : 100}
                </span>
                <Progress value={userMetrics.usagePercentage} className="w-24 h-2" />
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Progressive Content Sections */}
      <div className="space-y-6">
        
        {/* Recent Activity - High Priority */}
        {!sections.recentActivity.isCollapsed && monitoredSummaries.length > 0 && (
          <div>
            <div className="flex items-center justify-between mb-4">
              <div className="flex items-center gap-2">
                <Activity className="h-5 w-5 text-green-600" />
                <h2 className="text-lg font-semibold">Recent Activity</h2>
                <Badge variant="secondary">{monitoredSummaries.length} automated</Badge>
              </div>
              <Button 
                variant="ghost" 
                size="sm"
                onClick={() => toggleSection('recentActivity')}
              >
                <ChevronUp className="h-4 w-4" />
              </Button>
            </div>
            
            <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-4">
              {monitoredSummaries.slice(0, sections.recentActivity.showCount || 3).map((summary) => (
                <ContentCard
                  key={summary.id}
                  variant="summary"
                  title={summary.videoTitle}
                  subtitle={summary.videoChannel}
                  thumbnail={summary.videoThumbnail}
                  duration={summary.videoDuration}
                  metadata={{
                    readTime: summary.readTime,
                    timeSaved: summary.timeSaved
                  }}
                  status="new"
                  actions={{
                    primary: {
                      label: "View Summary",
                      onClick: () => setLocation(`/summary/${summary.id}`),
                      icon: Eye
                    }
                  }}
                />
              ))}
            </div>
          </div>
        )}

        {/* Library - Medium Priority */}
        <div>
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center gap-2">
              <Library className="h-5 w-5" />
              <h2 className="text-lg font-semibold">Your Library</h2>
              <Badge variant="outline">{summaries.length} summaries</Badge>
            </div>
            <Button 
              variant="ghost" 
              size="sm"
              onClick={() => toggleSection('library')}
            >
              {sections.library.isCollapsed ? <ChevronDown className="h-4 w-4" /> : <ChevronUp className="h-4 w-4" />}
            </Button>
          </div>
          
          {!sections.library.isCollapsed && (
            <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-4">
              {summaries.slice(0, sections.library.showCount || 6).map((summary) => (
                <ContentCard
                  key={summary.id}
                  variant="summary"
                  title={summary.videoTitle}
                  subtitle={summary.videoChannel}
                  thumbnail={summary.videoThumbnail}
                  duration={summary.videoDuration}
                  metadata={{
                    readTime: summary.readTime,
                    timeSaved: summary.timeSaved
                  }}
                  actions={{
                    primary: {
                      label: "View Summary",
                      onClick: () => setLocation(`/summary/${summary.id}`),
                      icon: Eye
                    }
                  }}
                />
              ))}
            </div>
          )}
        </div>

        {/* Channels - Expandable */}
        {userMetrics.monitoredChannels > 0 && (
          <div>
            <div className="flex items-center justify-between mb-4">
              <div className="flex items-center gap-2">
                <MonitorSpeaker className="h-5 w-5" />
                <h2 className="text-lg font-semibold">Channel Monitoring</h2>
                <Badge variant="outline">{userMetrics.monitoredChannels} active</Badge>
              </div>
              <Button 
                variant="ghost" 
                size="sm"
                onClick={() => toggleSection('channels')}
              >
                {sections.channels.isCollapsed ? <ChevronDown className="h-4 w-4" /> : <ChevronUp className="h-4 w-4" />}
              </Button>
            </div>
            
            {!sections.channels.isCollapsed && (
              <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-4">
                {Array.isArray(channels) && channels.slice(0, sections.channels.showCount || 4).map((channel: any) => (
                  <ContentCard
                    key={channel.id}
                    variant="channel"
                    title={channel.channelName}
                    thumbnail={channel.channelThumbnail}
                    status={channel.isMonitored ? "active" : "inactive"}
                    actions={{
                      primary: {
                        label: channel.isMonitored ? "View Summaries" : "Start Monitoring",
                        onClick: () => {}
                      }
                    }}
                  />
                ))}
              </div>
            )}
          </div>
        )}

        {/* Topics - Expandable */}
        {Array.isArray(topics) && topics.length > 0 && (
          <div>
            <div className="flex items-center justify-between mb-4">
              <div className="flex items-center gap-2">
                <Tag className="h-5 w-5" />
                <h2 className="text-lg font-semibold">Topics</h2>
                <Badge variant="outline">{topics.length} topics</Badge>
              </div>
              <Button 
                variant="ghost" 
                size="sm"
                onClick={() => toggleSection('topics')}
              >
                {sections.topics.isCollapsed ? <ChevronDown className="h-4 w-4" /> : <ChevronUp className="h-4 w-4" />}
              </Button>
            </div>
            
            {!sections.topics.isCollapsed && (
              <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-4">
                {Array.isArray(topics) && topics.slice(0, sections.topics.showCount || 6).map((topic: any) => (
                  <ContentCard
                    key={topic.id}
                    variant="topic"
                    title={topic.name}
                    description={topic.description}
                    colorAccent={topic.colorHex}
                    actions={{
                      primary: {
                        label: "View Content",
                        onClick: () => {}
                      }
                    }}
                  >
                    <div className="flex gap-4 text-sm text-muted-foreground">
                      <span>{topic.channelCount || 0} channels</span>
                      <span>{topic.summaryCount || 0} summaries</span>
                    </div>
                  </ContentCard>
                ))}
              </div>
            )}
          </div>
        )}
      </div>

      {/* Onboarding for New Users */}
      {summaries.length === 0 && monitoredSummaries.length === 0 && (
        <Card className="mt-8 border-dashed">
          <CardContent className="p-8 text-center">
            <Video className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
            <h3 className="text-lg font-semibold mb-2">Welcome to VideoSummarizer</h3>
            <p className="text-muted-foreground mb-6">
              Get started by creating your first summary or setting up channel monitoring
            </p>
            <div className="flex justify-center gap-4">
              <Button>
                <Plus className="h-4 w-4 mr-2" />
                Create Summary
              </Button>
              <Button variant="outline">
                <MonitorSpeaker className="h-4 w-4 mr-2" />
                Add Channels
              </Button>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}