import { useState, useEffect } from "react";
import { useAuth } from "@/hooks/useAuth";
import { useToast } from "@/hooks/use-toast";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { apiRequest } from "@/lib/queryClient";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { 
  Video, 
  Plus, 
  Search, 
  Filter, 
  Clock, 
  MonitorSpeaker,
  Tag,
  Library,
  Sparkles,
  TrendingUp,
  Zap
} from "lucide-react";
import { Link } from "wouter";

interface Summary {
  id: number;
  videoId: string;
  videoTitle: string;
  videoChannel: string;
  videoThumbnail: string;
  videoDuration: string;
  videoViews: string;
  summaryContent: string;
  readTime: string;
  timeSaved: string;
  createdAt: string;
  channelId?: number;
  isMonitored?: boolean;
}

interface Channel {
  id: number;
  channelName: string;
  channelThumbnail: string;
  isMonitored: boolean;
  lastChecked?: string;
}

interface Topic {
  id: number;
  name: string;
  description: string;
  colorHex: string;
  channelCount: number;
  summaryCount: number;
}

export default function UnifiedDashboard() {
  const { user: authUser, isLoading: authLoading } = useAuth();
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const [videoUrl, setVideoUrl] = useState("");
  const [activeTab, setActiveTab] = useState("create");

  // Fetch all data
  const { data: summaries = [] } = useQuery<Summary[]>({
    queryKey: ["/api/summaries"],
    enabled: !!authUser,
  });

  const { data: channels = [] } = useQuery<Channel[]>({
    queryKey: ["/api/channels"], 
    enabled: !!authUser,
  });

  const { data: topics = [] } = useQuery<Topic[]>({
    queryKey: ["/api/topics"],
    enabled: !!authUser,
  });

  const { data: monitoredSummaries = [] } = useQuery<Summary[]>({
    queryKey: ["/api/monitored-summaries"],
    enabled: !!authUser,
  });

  // Create summary mutation
  const createSummaryMutation = useMutation({
    mutationFn: async (data: { url: string }) => {
      return apiRequest(`/api/summaries`, {
        method: "POST",
        body: JSON.stringify(data),
      });
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["/api/summaries"] });
      setVideoUrl("");
      toast({
        title: "Success",
        description: "Video summary created successfully!",
      });
    },
    onError: (error: any) => {
      toast({
        title: "Error",
        description: error.message || "Failed to create summary",
        variant: "destructive",
      });
    },
  });

  const handleCreateSummary = () => {
    if (!videoUrl.trim()) return;
    createSummaryMutation.mutate({ url: videoUrl });
  };

  // Quick stats
  const stats = {
    totalSummaries: summaries.length,
    monitoredChannels: channels.filter(c => c.isMonitored).length,
    activeTopics: topics.length,
    newToday: monitoredSummaries.filter(s => 
      new Date(s.createdAt).toDateString() === new Date().toDateString()
    ).length
  };

  return (
    <div className="container mx-auto px-6 py-8 max-w-7xl">
      
      {/* Hero Section with Quick Create */}
      <div className="mb-8">
        <div className="bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-950/20 dark:to-indigo-950/20 rounded-2xl p-8 border border-blue-100 dark:border-blue-900/30">
          <div className="grid lg:grid-cols-2 gap-8 items-center">
            
            {/* Quick Create - Primary Action */}
            <div>
              <div className="flex items-center gap-2 mb-4">
                <div className="h-8 w-8 bg-primary rounded-lg flex items-center justify-center">
                  <Sparkles className="h-4 w-4 text-primary-foreground" />
                </div>
                <h1 className="text-2xl font-bold">Create New Summary</h1>
              </div>
              
              <p className="text-muted-foreground mb-6">
                Transform any YouTube video into actionable insights in seconds
              </p>
              
              <div className="flex gap-3">
                <Input
                  placeholder="Paste YouTube URL here..."
                  value={videoUrl}
                  onChange={(e) => setVideoUrl(e.target.value)}
                  className="flex-1"
                />
                <Button 
                  onClick={handleCreateSummary}
                  disabled={!videoUrl.trim() || createSummaryMutation.isPending}
                  size="lg"
                >
                  {createSummaryMutation.isPending ? (
                    <>
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2" />
                      Creating...
                    </>
                  ) : (
                    <>
                      <Zap className="h-4 w-4 mr-2" />
                      Summarize
                    </>
                  )}
                </Button>
              </div>
            </div>

            {/* Quick Stats Dashboard */}
            <div className="grid grid-cols-2 gap-4">
              <Card>
                <CardContent className="p-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm text-muted-foreground">Total Summaries</p>
                      <p className="text-2xl font-bold">{stats.totalSummaries}</p>
                    </div>
                    <Library className="h-8 w-8 text-blue-500" />
                  </div>
                </CardContent>
              </Card>
              
              <Card>
                <CardContent className="p-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm text-muted-foreground">Monitored Channels</p>
                      <p className="text-2xl font-bold">{stats.monitoredChannels}</p>
                    </div>
                    <MonitorSpeaker className="h-8 w-8 text-green-500" />
                  </div>
                </CardContent>
              </Card>
              
              <Card>
                <CardContent className="p-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm text-muted-foreground">Topics</p>
                      <p className="text-2xl font-bold">{stats.activeTopics}</p>
                    </div>
                    <Tag className="h-8 w-8 text-purple-500" />
                  </div>
                </CardContent>
              </Card>
              
              <Card>
                <CardContent className="p-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm text-muted-foreground">New Today</p>
                      <p className="text-2xl font-bold">{stats.newToday}</p>
                    </div>
                    <TrendingUp className="h-8 w-8 text-orange-500" />
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      </div>

      {/* Unified Content Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="grid w-full grid-cols-4 mb-6">
          <TabsTrigger value="recent" className="flex items-center gap-2">
            <Clock className="h-4 w-4" />
            Recent Activity
          </TabsTrigger>
          <TabsTrigger value="library" className="flex items-center gap-2">
            <Library className="h-4 w-4" />
            Library
          </TabsTrigger>
          <TabsTrigger value="channels" className="flex items-center gap-2">
            <MonitorSpeaker className="h-4 w-4" />
            Channels
          </TabsTrigger>
          <TabsTrigger value="topics" className="flex items-center gap-2">
            <Tag className="h-4 w-4" />
            Topics
          </TabsTrigger>
        </TabsList>

        {/* Recent Activity - Monitoring Dashboard */}
        <TabsContent value="recent" className="space-y-6">
          <div className="flex items-center justify-between">
            <h2 className="text-xl font-semibold">Recent Activity</h2>
            <Badge variant="secondary">
              {monitoredSummaries.length} new summaries from monitoring
            </Badge>
          </div>
          
          <div className="grid gap-4">
            {monitoredSummaries.slice(0, 6).map((summary) => (
              <Card key={summary.id} className="hover:shadow-md transition-shadow">
                <CardContent className="p-4">
                  <div className="flex gap-4">
                    <img 
                      src={summary.videoThumbnail} 
                      alt={summary.videoTitle}
                      className="w-24 h-16 object-cover rounded-lg"
                    />
                    <div className="flex-1">
                      <h3 className="font-medium line-clamp-2 mb-1">{summary.videoTitle}</h3>
                      <p className="text-sm text-muted-foreground mb-2">{summary.videoChannel}</p>
                      <div className="flex gap-4 text-xs text-muted-foreground">
                        <span>📺 {summary.videoDuration}</span>
                        <span>⏱️ {summary.readTime} read</span>
                        <span>💾 {summary.timeSaved} saved</span>
                      </div>
                    </div>
                    <Badge variant="outline">Auto-generated</Badge>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
          
          <div className="text-center">
            <Link href="/library">
              <Button variant="outline">View All Summaries</Button>
            </Link>
          </div>
        </TabsContent>

        {/* Library Tab - Search & Browse */}
        <TabsContent value="library" className="space-y-6">
          <div className="flex items-center justify-between">
            <h2 className="text-xl font-semibold">Summary Library</h2>
            <div className="flex gap-2">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                <Input placeholder="Search summaries..." className="pl-10 w-64" />
              </div>
              <Button variant="outline" size="icon">
                <Filter className="h-4 w-4" />
              </Button>
            </div>
          </div>
          
          <div className="grid lg:grid-cols-2 xl:grid-cols-3 gap-4">
            {summaries.slice(0, 9).map((summary) => (
              <Card key={summary.id} className="hover:shadow-md transition-shadow">
                <CardContent className="p-4">
                  <div className="aspect-video relative mb-3">
                    <img 
                      src={summary.videoThumbnail} 
                      alt={summary.videoTitle}
                      className="w-full h-full object-cover rounded-lg"
                    />
                    <div className="absolute bottom-2 right-2 bg-black/80 text-white px-2 py-1 rounded text-xs">
                      {summary.videoDuration}
                    </div>
                  </div>
                  <h3 className="font-medium line-clamp-2 mb-2">{summary.videoTitle}</h3>
                  <p className="text-sm text-muted-foreground mb-3">{summary.videoChannel}</p>
                  <div className="flex justify-between items-center text-xs text-muted-foreground">
                    <span>⏱️ {summary.readTime}</span>
                    <span>💾 {summary.timeSaved}</span>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
          
          <div className="text-center">
            <Link href="/library">
              <Button variant="outline">View Full Library</Button>
            </Link>
          </div>
        </TabsContent>

        {/* Channels Tab - Management */}
        <TabsContent value="channels" className="space-y-6">
          <div className="flex items-center justify-between">
            <h2 className="text-xl font-semibold">Channel Management</h2>
            <Link href="/channels">
              <Button>
                <Plus className="h-4 w-4 mr-2" />
                Add Channels
              </Button>
            </Link>
          </div>
          
          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-4">
            {channels.slice(0, 6).map((channel) => (
              <Card key={channel.id}>
                <CardContent className="p-4">
                  <div className="flex items-center gap-3 mb-3">
                    <img 
                      src={channel.channelThumbnail} 
                      alt={channel.channelName}
                      className="w-12 h-12 rounded-full"
                    />
                    <div className="flex-1">
                      <h3 className="font-medium">{channel.channelName}</h3>
                      <p className="text-sm text-muted-foreground">
                        {channel.isMonitored ? 'Monitoring active' : 'Not monitored'}
                      </p>
                    </div>
                  </div>
                  <div className="flex gap-2">
                    <Badge variant={channel.isMonitored ? "default" : "secondary"}>
                      {channel.isMonitored ? "Active" : "Inactive"}
                    </Badge>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>

        {/* Topics Tab - Organization */}
        <TabsContent value="topics" className="space-y-6">
          <div className="flex items-center justify-between">
            <h2 className="text-xl font-semibold">Topic Organization</h2>
            <Link href="/topics">
              <Button>
                <Plus className="h-4 w-4 mr-2" />
                Create Topic
              </Button>
            </Link>
          </div>
          
          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-4">
            {topics.map((topic) => (
              <Card key={topic.id}>
                <CardContent className="p-4">
                  <div className="flex items-center gap-3 mb-3">
                    <div 
                      className="w-4 h-4 rounded-full"
                      style={{ backgroundColor: topic.colorHex }}
                    />
                    <h3 className="font-medium">{topic.name}</h3>
                  </div>
                  <p className="text-sm text-muted-foreground mb-3">{topic.description}</p>
                  <div className="flex gap-4 text-sm">
                    <span>{topic.channelCount} channels</span>
                    <span>{topic.summaryCount} summaries</span>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
}