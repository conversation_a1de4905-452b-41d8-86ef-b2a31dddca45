// Test script to manually trigger video processing for existing channels
const fetch = require('node-fetch');

async function testProcessRecentVideos() {
  try {
    // Test the API endpoint directly
    const response = await fetch('http://localhost:5000/api/channels/17/process-recent', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        // Note: This will need proper authentication in real usage
      }
    });
    
    const result = await response.text();
    console.log('Response status:', response.status);
    console.log('Response:', result);
    
    if (response.status === 401) {
      console.log('Authentication required - this is expected when testing without session');
      return;
    }
    
    if (response.ok) {
      console.log('Video processing started successfully');
    } else {
      console.log('Error starting video processing');
    }
  } catch (error) {
    console.error('Test error:', error);
  }
}

testProcessRecentVideos();